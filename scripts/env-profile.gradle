// 环境配置文件脚本，可自由选择和切换环境 env-profile.gradle

// 当前定义的环境
def defaultProfileEnv = "dev"

// 可定义的环境
def profileEnvList = ['Dev', 'Test', 'Beta', 'Prod']

// 从参数中获得-Dprofile=文件中，如果不存在则使用defaultProfileEnv
def profileEnv = System.getProperty("profile") ?: defaultProfileEnv


// 设置Resources
sourceSets {
    main.resources {
        srcDirs += ["src/profile/$profileEnv"]
        println("resources dir: $srcDirs")
    }
}