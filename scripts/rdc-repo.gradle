// 首展阿里云私有库
buildscript.repositories {
    maven {
        url 'https://packages.aliyun.com/maven/repository/2095455-release-Xz5oPZ/'
        credentials {
            username '61c41097d6190210d84fd51e'
            password 'KATf7WjH1Bee'
        }
    }

    maven {
        url 'https://packages.aliyun.com/maven/repository/2095455-snapshot-vkcgv3/'
        credentials {
            username '61c41097d6190210d84fd51e'
            password 'KATf7WjH1Bee'
        }
    }
}

// 子项目
subprojects {
    repositories {
        maven {
            url 'https://packages.aliyun.com/maven/repository/2095455-release-Xz5oPZ/'
            credentials {
                username '61c41097d6190210d84fd51e'
                password 'KATf7WjH1Bee'
            }
        }

        maven {
            url 'https://packages.aliyun.com/maven/repository/2095455-snapshot-vkcgv3/'
            credentials {
                username '61c41097d6190210d84fd51e'
                password 'KATf7WjH1Bee'
            }
        }
    }
}