package com.huike.nova.web.test;

import com.fshows.knowledge.service.business.api.YqOpenService;
import com.fshows.knowledge.web.FShowsKnowledgeWebApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.regex.Pattern;

@SpringBootTest(classes = FShowsKnowledgeWebApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
public class CommonTest {

    @Autowired
    private YqOpenService yqOpenService;

    @Test
    public void test() {
        System.out.println("开始");
        String cookie = "lang=zh-cn; _yuque_session=wEBXOAr4uWYdSZnbD3UlbmzlyqbO3Wv7FpZCGkGcoBZxc-xr8j4VIcGi4tcATQhAw4LGzSScXDdawN-wS60REA==; tfstk=g76naAVtcFgbgAa96O9QgRpF6dVtdp9WOaHJyLLz_F8sJ2Hd48kP2ifpay9dENb15wId2TfsEis5pk98ApsBFLzYk-BlAM97jc_cN9DZ_3sZLb8z6dJk7WeLk-eAY1yOZyUAeHZ7Be-yUHRyYCrMmHJrUX7y7C-2D2JPzaJNbnKqzUleaFkw53JyzaJzjG86qL8r1_owJa7CQPFXC54RLDXwxBYVYejdROyHTXsMSYkPIgRHuTYiUYWMxgjo0NMUnF5da9pNLPuXQi1O8hv0E0xGiGJexawjdpSVb_RhE7nXP1jNMCfsvktGKiXkmCuaxU6d-TAC3rH681X1EpCT-xLWa1BBG9UZKK5fvpCP7ukyr1-P4VcZgPNxFhrRQbGWThtMkTBtbptK9u7YjlcVGB-6Y-EgjbwyThtM4lqiiAAefHVA.; receive-cookie-deprecation=1; _tea_utm_cache_10000007=undefined; aliyungf_tc=8231b7fb21ee5f74948166f9280e2699e4ca3377ac18704fae088bd7ccc937e1; yuque_ctoken=69mlfQIbviYUXYCkV3B6FZEz; current_theme=default; acw_tc=ac11000117550671069185187e6b1d8916826b3d259813a29f058a76d05669";
        String csrfToken = "69mlfQIbviYUXYCkV3B6FZEz";
        String string = yqOpenService.downloadYuQueArticle("230864101", cookie, csrfToken);
        System.out.println("结束");
    }

    @Test
    public void test2() {
        System.out.println("开始");
        String cookie = "lang=zh-cn; _yuque_session=wEBXOAr4uWYdSZnbD3UlbmzlyqbO3Wv7FpZCGkGcoBZxc-xr8j4VIcGi4tcATQhAw4LGzSScXDdawN-wS60REA==; tfstk=g76naAVtcFgbgAa96O9QgRpF6dVtdp9WOaHJyLLz_F8sJ2Hd48kP2ifpay9dENb15wId2TfsEis5pk98ApsBFLzYk-BlAM97jc_cN9DZ_3sZLb8z6dJk7WeLk-eAY1yOZyUAeHZ7Be-yUHRyYCrMmHJrUX7y7C-2D2JPzaJNbnKqzUleaFkw53JyzaJzjG86qL8r1_owJa7CQPFXC54RLDXwxBYVYejdROyHTXsMSYkPIgRHuTYiUYWMxgjo0NMUnF5da9pNLPuXQi1O8hv0E0xGiGJexawjdpSVb_RhE7nXP1jNMCfsvktGKiXkmCuaxU6d-TAC3rH681X1EpCT-xLWa1BBG9UZKK5fvpCP7ukyr1-P4VcZgPNxFhrRQbGWThtMkTBtbptK9u7YjlcVGB-6Y-EgjbwyThtM4lqiiAAefHVA.; receive-cookie-deprecation=1; _tea_utm_cache_10000007=undefined; aliyungf_tc=8231b7fb21ee5f74948166f9280e2699e4ca3377ac18704fae088bd7ccc937e1; yuque_ctoken=69mlfQIbviYUXYCkV3B6FZEz; current_theme=default; acw_tc=ac11000117550671069185187e6b1d8916826b3d259813a29f058a76d05669";
        String csrfToken = "69mlfQIbviYUXYCkV3B6FZEz";
        String string = yqOpenService.getYuQueArticleContent("https://fshows.yuque.com/rqxish/rgu3dn/ka7tmmtvn18sab8t/markdown?attachment=true&latexcode=false&anchor=false&linebreak=false", cookie, csrfToken);
        // 预编译正则表达式（匹配一个或多个 #）
        Pattern pattern = Pattern.compile("(?<!#)###(?!#)");

        // 使用正则表达式分割字符串
        String[] parts = pattern.split(string);
        System.out.println("结束");
    }



}
