package com.fshows.knowledge.web.domain.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * LoginRequest
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Data
public class LoginRequest {

    /**
     * 来源:1-代理商；2-客服
     */
    @NotNull(message = "登录来源不能为空")
    private Integer source;

    /**
     * 代理商登录token
     */
    private String token;

    /**
     * 代理商登录的设备Id
     */
    private String deviceId;

    /**
     * 客服登录用户名
     */
    private String userName;

    /**
     * 客服登录密码
     */
    private String password;

}