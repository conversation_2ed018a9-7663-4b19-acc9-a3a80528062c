package com.fshows.knowledge.web.domain.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryResponse {

	/**
	 * 请求是否成功
	 */
	private boolean success;

	/**
	 * 错误或状态消息 (例如，在 success=false 时显示 "网络繁忙，请稍后重试")
	 */
	private String message;

	/**
	 * 用户输入的问题
	 */
	private String query;

	/**
	 * 答案
	 */
	private String answer;

	/**
	 * 消息 id 本次提问的唯一标识
	 */
	private String messageId;

	/**
	 * 答案开始文本
	 */
	private String answerStartText;

	/**
	 * 答案结束文本
	 */
	private String answerEndText;

	/**
	 * “猜您还想问”的问题列表
	 */
	private List<String> relatedQuestionList;

	/**
	 * AI回答所引用的附件/资料来源列表
	 */
	private List<MatchResult> matchResultList;

	@Data
	public static class MatchResult {

		/**
		 * 引用的资料内容
		 */
		private String text;

		/**
		 * 相似度分数
		 */
		private double score;

		/**
		 * 语雀文档原地址 url
		 */
		private String yuQueUrl;

		/**
		 * 语雀文档名称
		 */
		private String source;

		/**
		 * 包含锚点信息的语雀文档地址 url
		 */
		private String yuQueUrlAnchor;

	}

}
