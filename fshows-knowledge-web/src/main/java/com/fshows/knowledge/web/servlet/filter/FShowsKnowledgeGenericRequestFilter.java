/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.knowledge.web.servlet.filter;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.fshows.knowledge.web.servlet.wrapper.FShowsGenericRequestWrapper;
import com.huike.nova.common.constant.CommonConstant;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 回客请求Servlet请求过滤器
 *
 * <AUTHOR> (<EMAIL>)
 * @version HuikeGenericRequestFilter.java, v1.0 2022/8/31 22:44 John Exp$
 */
@Component
@WebFilter(filterName = "FShowsKnowledgeGenericRequestFilter", urlPatterns = "/")
@Order(100)
public class FShowsKnowledgeGenericRequestFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        Filter.super.init(filterConfig);
    }

    /**
     * 检查是否过滤请求
     *
     * @param request HttpServletRequest对象
     * @return true需要进行过滤，false 不需要进行过滤
     */
    private boolean checkFilter(ServletRequest request) {
        if (request instanceof HttpServletRequest) {
            val httpServletRequest = (HttpServletRequest) request;
            String servletPath = httpServletRequest.getServletPath();
            if (CommonConstant.ALIPAY_THIRD_PARTY_CERTIFICATE_SEND_PATH.equals(servletPath) || CommonConstant.ALIPAY_THIRD_PARTY_CERTIFICATE_SEND_PATH_NEW.equals(servletPath)) {
                return Boolean.FALSE;
            }
            val contentType = request.getContentType();
            if (ContentType.isFormUrlEncode(contentType) || isMultiPartFormData(contentType)) {
                return false;
            }
            // 暂时拦截GET方法
            return !StringUtils.equalsIgnoreCase(CommonConstant.HTTP_METHOD_GET, httpServletRequest.getMethod());
        }
        // 非HttpServletRequest
        return false;
    }

    /**
     * 是否是multipart/form-data
     *
     * @param contentType 类型
     * @return true-是 false-否
     */
    private static boolean isMultiPartFormData(String contentType) {
        return StrUtil.startWithIgnoreCase(contentType, ContentType.MULTIPART.toString());
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        // 拦截所有HTTP请求
        if (checkFilter(request)) {
            FShowsGenericRequestWrapper requestWrapper = FShowsGenericRequestWrapper.wrap((HttpServletRequest) request);
            chain.doFilter(requestWrapper, response);

            // 非Http请求，不做处理
        } else {
            chain.doFilter(request, response);
        }
    }

    @Override
    public void destroy() {
        Filter.super.destroy();
    }
}
