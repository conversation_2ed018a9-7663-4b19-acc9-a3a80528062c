/*
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.knowledge.web.domain.mapper;


import com.fshows.knowledge.service.domain.model.FavoriteListPageModel;
import com.fshows.knowledge.service.domain.param.FavoriteCancelParam;
import com.fshows.knowledge.service.domain.param.FavoriteListPageParam;
import com.fshows.knowledge.service.domain.param.FavoriteSaveParam;
import com.fshows.knowledge.web.domain.request.FavoriteCancelRequest;
import com.fshows.knowledge.web.domain.request.FavoriteListPageRequest;
import com.fshows.knowledge.web.domain.request.FavoriteSaveRequest;
import com.fshows.knowledge.web.domain.request.PageRequest;
import com.fshows.knowledge.web.domain.response.FavoriteListPageResponse;
import com.fshows.knowledge.web.domain.response.common.PageResponse;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version componentModel.java
 */
@Mapper(componentModel = "spring")
public interface FavoriteApiObjMapper {


    /**
     * 参数转换
     *
     * @param request 参数
     * @return 结果
     */
    FavoriteSaveParam toFavoriteSaveParam(FavoriteSaveRequest request);

    /**
     * 参数转换
     *
     * @param request 参数
     * @return 结果
     */
    FavoriteCancelParam toFavoriteCancelParam(FavoriteCancelRequest request);


    /**
     * 转换
     *
     * @param request 入参
     * @return 出参
     */
    PageParam<FavoriteListPageParam> toFavoriteListPageParam(PageRequest<FavoriteListPageRequest> request);


    /**
     * 转换
     *
     * @param pageModelPage 出参
     * @return 入参
     */
    PageResponse<FavoriteListPageResponse> toFavoriteListPageResponse(PageResult<FavoriteListPageModel> pageModelPage);


}