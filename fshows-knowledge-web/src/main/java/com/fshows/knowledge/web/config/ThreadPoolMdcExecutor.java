package com.fshows.knowledge.web.config;

import com.huike.nova.common.util.MdcUtil;
import org.slf4j.MDC;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;

/**
 * 把当前的traceId透传到子线程特意加的实现。
 * 重点就是 MDC.getCopyOfContextMap()，此方法获取当前线程（父线程）的traceId
 *
 * <AUTHOR>
 * @date 2023/4/19 11:11
 * @copyright 2022 barm Inc. All rights reserved
 */
public class ThreadPoolMdcExecutor extends ThreadPoolTaskExecutor {
    @Override
    public void execute(Runnable task) {
        super.execute(MdcUtil.wrap(task, MDC.getCopyOfContextMap()));
    }

    @Override
    public Future<?> submit(Runnable task) {
        return super.submit(MdcUtil.wrap(task, MDC.getCopyOfContextMap()));
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return super.submit(MdcUtil.wrap(task, MDC.getCopyOfContextMap()));
    }
}
