/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.fshows.knowledge.web.aop;

import com.alibaba.fastjson.JSON;
import com.huike.nova.common.annotation.RestLog;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 控制层接口 日志
 * <AUTHOR>
 * @version RestLogConfig.java, v 0.1 2022-12-21 10:00 zhangling
 */
@Slf4j
@Aspect
@Order(2)
//@Component
public class RestLogConfig {
    @Pointcut("@annotation(com.huike.nova.common.annotation.RestLog)")
    public void restLog() {}

    @Around("restLog()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long begin = System.currentTimeMillis();
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        RestLog resLog = method.getAnnotation(RestLog.class);

        // 先进 filter 在进 interceptor 最后进 aop
        printBeforeLog(resLog, request, joinPoint, signature);
        Object result = null;
        try {
            result = joinPoint.proceed();
        } catch (Throwable throwable) {
            throw throwable;
        }
        printAfterLog(resLog, begin, result);
        return result;
    }

    private void printBeforeLog(RestLog restLog, HttpServletRequest request, ProceedingJoinPoint joinPoint, Signature signature) {
        if (restLog.level().equals(RestLog.Level.TRACE)) {
            log.trace("请求地址 {}", request.getRequestURL().toString());
            log.trace("请求接口 {}，{}，{}", restLog.desc(), request.getMethod(), signature.getName());
            log.trace("请求入参 {}", JSON.toJSONString(joinPoint.getArgs()));
        } else if (restLog.level().equals(RestLog.Level.DEBUG)) {
            log.debug("请求地址 {}", request.getRequestURL().toString());
            log.debug("请求接口 {}，{}，{}", restLog.desc(), request.getMethod(), signature.getName());
//            log.debug("请求入参 {}", new Gson().toJson(joinPoint.getArgs()));
            log.debug("请求入参 {}", JSON.toJSONString(joinPoint.getArgs()));
        } else if (restLog.level().equals(RestLog.Level.INFO)) {
            log.info("请求地址 {}", request.getRequestURL().toString());
            log.info("请求接口 {}，{}，{}", restLog.desc(), request.getMethod(), signature.getName());
//            log.info("请求入参 {}", new Gson().toJson(joinPoint.getArgs()));
        } else if (restLog.level().equals(RestLog.Level.WARN)) {
            log.warn("请求地址 {}", request.getRequestURL().toString());
            log.warn("请求接口 {}，{}，{}", restLog.desc(), request.getMethod(), signature.getName());
//            log.warn("请求入参 {}", new Gson().toJson(joinPoint.getArgs()));
        } else if (restLog.level().equals(RestLog.Level.ERROR)) {
            log.error("请求地址 {}", request.getRequestURL().toString());
            log.error("请求接口 {}，{}，{}", restLog.desc(), request.getMethod(), signature.getName());
//            log.error("请求入参 {}", new Gson().toJson(joinPoint.getArgs()));
        }
    }

    private void printAfterLog(RestLog restLog, long beginTime, Object result) {
        if (restLog.level().equals(RestLog.Level.TRACE)) {
            log.trace("请求耗时 {}，{} ms", restLog.desc(), System.currentTimeMillis() - beginTime);
            log.trace("请求返回 {}", JSON.toJSONString(result));
        } else if (restLog.level().equals(RestLog.Level.DEBUG)) {
            log.debug("请求耗时 {}，{} ms", restLog.desc(), System.currentTimeMillis() - beginTime);
            log.debug("请求返回 {}", JSON.toJSONString(result));
        } else if (restLog.level().equals(RestLog.Level.INFO)) {
            log.info("请求耗时 {}，{} ms", restLog.desc(), System.currentTimeMillis() - beginTime);
            log.info("请求返回 {}", JSON.toJSONString(result));
        } else if (restLog.level().equals(RestLog.Level.WARN)) {
            log.warn("请求耗时 {}，{} ms", restLog.desc(), System.currentTimeMillis() - beginTime);
            log.warn("请求返回 {}", JSON.toJSONString(result));
        } else if (restLog.level().equals(RestLog.Level.ERROR)) {
            log.error("请求耗时 {}，{} ms", restLog.desc(), System.currentTimeMillis() - beginTime);
            log.error("请求返回 {}", JSON.toJSONString(result));
        }
    }
}