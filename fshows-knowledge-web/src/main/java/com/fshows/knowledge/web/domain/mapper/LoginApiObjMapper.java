/*
 * ailike.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.knowledge.web.domain.mapper;


import com.fshows.knowledge.service.domain.model.LoginModel;
import com.fshows.knowledge.service.domain.param.YuQueWebHookParam;
import com.fshows.knowledge.service.domain.param.login.LoginParam;
import com.fshows.knowledge.web.domain.request.LoginRequest;
import com.fshows.knowledge.web.domain.request.YuQueWebHookRequest;
import com.fshows.knowledge.web.domain.response.LoginResponse;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version componentModel.java
 */
@Mapper(componentModel = "spring")
public interface LoginApiObjMapper {


    /**
     * 参数转换
     *
     * @param request 参数
     * @return 结果
     */
    LoginParam toLoginParam(LoginRequest request);

    /**
     * 参数转换
     *
     * @param model 参数
     * @return 结果
     */
    LoginResponse toLoginResponse(LoginModel model);
}