package com.fshows.knowledge.web.domain.response.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 配置响应
 * <AUTHOR>
 * @version 1.0 ConfigResult
 * @date 2022/10/21 18:23
 */
@Data
public class ConfigResponse {

    /**
     * key
     */
    @ApiModelProperty(value = "type key")
    private String typeKey;


    /**
     * value
     */
    @ApiModelProperty(value = "type value")
    private String typeValue;

    /**
     * 说明
     */
    @ApiModelProperty(value = "说明")
    private String remark;
}
