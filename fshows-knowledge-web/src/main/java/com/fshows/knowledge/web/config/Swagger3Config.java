/*
 *
 *  * Hangzhou Huike Technology co.,ltd.
 *  * Copyright (C) 2013-2022 All Rights Reserved.
 *
 *
 */

package com.fshows.knowledge.web.config;

import org.assertj.core.util.Lists;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuate.autoconfigure.endpoint.web.CorsEndpointProperties;
import org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties;
import org.springframework.boot.actuate.autoconfigure.web.server.ManagementPortType;
import org.springframework.boot.actuate.endpoint.ExposableEndpoint;
import org.springframework.boot.actuate.endpoint.web.*;
import org.springframework.boot.actuate.endpoint.web.annotation.ControllerEndpointsSupplier;
import org.springframework.boot.actuate.endpoint.web.annotation.ServletEndpointsSupplier;
import org.springframework.boot.actuate.endpoint.web.servlet.WebMvcEndpointHandlerMapping;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.oas.annotations.EnableOpenApi;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Swagger3 配置类
 *
 * <AUTHOR> (<EMAIL>)
 * @version Swagger3Config.java, v1.0 11/16/2022 20:17 John Exp$
 */
@Configuration
@EnableOpenApi
public class Swagger3Config {

    /**
     * 配置文件中，是否已开启Swagger
     */
    @Value("${swagger.enable:false}")
    private boolean enableSwagger;

    /**
     * 设置Swagger显示文档的信息
     */
    @Bean
    public ApiInfo apiInfo() {
        return new ApiInfoBuilder()
                .title("来团呗")
                .description("Huike-Nova API doc")
                .version("1.2")
                .build();
    }

    /**
     * Swagger全局通用属性配置
     */
    @Bean
    public Docket api() {
        return new Docket(DocumentationType.OAS_30)
                //应用文档基本信息
                .apiInfo(apiInfo())
                // 是否启用Swagger
                .enable(enableSwagger)
                .select()
                // swagger扫描路径
                .apis(RequestHandlerSelectors.basePackage("com.huike.nova.web.api"))
                // 应用于包下所有路径
                .paths(PathSelectors.any())
                .build();
    }

    /**
     * SpringBoot与Swagger兼容
     */
    @Bean
    public WebMvcEndpointHandlerMapping webEndpointServletHandlerMapping(WebEndpointsSupplier webEndpointsSupplier,
                                                                         ServletEndpointsSupplier servletEndpointsSupplier,
                                                                         ControllerEndpointsSupplier controllerEndpointsSupplier,
                                                                         EndpointMediaTypes endpointMediaTypes,
                                                                         CorsEndpointProperties corsProperties,
                                                                         WebEndpointProperties webEndpointProperties,
                                                                         Environment environment) {
        List<ExposableEndpoint<?>> allEndpoints = Lists.newArrayList();
        Collection<ExposableWebEndpoint> webEndpoints = webEndpointsSupplier.getEndpoints();
        allEndpoints.addAll(webEndpoints);
        allEndpoints.addAll(servletEndpointsSupplier.getEndpoints());
        allEndpoints.addAll(controllerEndpointsSupplier.getEndpoints());
        String basePath = webEndpointProperties.getBasePath();
        EndpointMapping endpointMapping = new EndpointMapping(basePath);
        boolean shouldRegisterLinksMapping = this.shouldRegisterLinksMapping(webEndpointProperties, environment, basePath);
        return new WebMvcEndpointHandlerMapping(endpointMapping, webEndpoints, endpointMediaTypes, corsProperties.toCorsConfiguration(), new EndpointLinksResolver(allEndpoints, basePath), shouldRegisterLinksMapping, null);
    }

    /**
     * 是否注册关联映射
     *
     * @param webEndpointProperties 节点配置
     * @param environment 环境配置
     * @param basePath 基本路径
     * @return TRUE/FALSE
     */
    private boolean shouldRegisterLinksMapping(WebEndpointProperties webEndpointProperties, Environment environment, String basePath) {
        return webEndpointProperties.getDiscovery().isEnabled() && (StringUtils.hasText(basePath) || ManagementPortType.get(environment).equals(ManagementPortType.DIFFERENT));
    }
}
