package com.fshows.knowledge.web.domain.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * YuQueWebHookRequest
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/11
 */
@Data
public class YuQueWebHookRequest {


    /**
     * 语雀数据
     * <a href="https://www.yuque.com/yuque/developer/doc-webhook">...</a>
     */
    private YuQueData data;

    /**
     * 语雀数据
     */
    @Data
    public static class YuQueData {
        /**
         * 动作类型
         * - publish:文档发布
         * - update:文档更新
         * - delete:文档删除
         */
        @JsonProperty( "action_type")
        private String actionType;

        @JsonProperty( "webhook_subject_type")
        private String webhookSubjectType;

        private String path;
        private String url;
        private Long id;
        private String slug;
        /**
         * 标题
         */
        private String title;
        /**
         * 正文 Markdown 内容
         */
        private String body;

        /**
         * 正文 HTML 内容
         */
        @JsonProperty( "body_html")
        private String bodyHtml;

        @JsonProperty( "created_at")
        private Date createdAt;

        @JsonProperty( "updated_at")
        private Date updatedAt;

        @JsonProperty( "deleted_at")
        private Date deletedAt;

        @JsonProperty( "published_at")
        private Date publishedAt;

        @JsonProperty( "first_published_at")
        private Date firstPublishedAt;

        @JsonProperty( "content_updated_at")
        private Date contentUpdatedAt;

        private User user;
        /**
         * 文档所属知识库
         */
        private Book book;
        private User actor;
        private List<Tag> tags;

        @Data
        public static class User {
            private Long id;
            private String login;
            private String name;

            @JsonProperty( "avatar_url")
            private String avatarUrl;
        }

        @Data
        public static class Book {
            private Long id;
            private String slug;
            private String name;
            private String description;
        }

        @Data
        public static class Tag {
            private Long id;
            private String title;

            @JsonProperty( "created_at")
            private Date createdAt;

            @JsonProperty( "updated_at")
            private Date updatedAt;
        }
    }
}