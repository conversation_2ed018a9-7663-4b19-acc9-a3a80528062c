package com.fshows.knowledge.web.domain.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * LoginRequest
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Data
public class FavoriteSaveRequest {

    /**
     * 用户提问问题
     */
    @NotBlank(message = "用户提问问题不能为空")
    private String question;

    /**
     * 收藏的AI回答内容
     */
    @NotBlank(message = "回调内容不能为空")
    private String answer;

    /**
     * '消息id'
     */
    @NotBlank(message = "消息id不能为空")
    private String messageId;

}