/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.knowledge.web.interceptor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fshows.knowledge.web.domain.response.common.BaseResponse;
import com.fshows.knowledge.web.servlet.wrapper.FShowsGenericRequestWrapper;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.MD5Util;
import com.huike.nova.common.util.WebUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.http.MediaType;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;


/**
 * 接口拦截器
 *
 * <AUTHOR>
 * @version TraceInterceptor.java, v 0.1 2022-08-31 11:33 AM ruanzy
 */
@Slf4j

public class InterfaceInterceptor implements HandlerInterceptor {

    private static final Integer SECONDS = 60 * 10;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String servletPath = request.getServletPath();
        LogUtil.info(log, "InterfaceInterceptor.preHandle >> 进入拦截, {} ,{}", servletPath, handler.getClass().getName());
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        // 判断是否是Json对象
        if (!StringUtils.containsIgnoreCase(request.getContentType(), MediaType.APPLICATION_JSON_VALUE)) {
            LogUtil.error(log, "InterfaceInterceptor.preHandle >> 错误，当前不是JSON数据, contentType={}", Strings.nullToEmpty(request.getContentType()));
            //登录失效异常
            WebUtil.sendResponse(response, BaseResponse.failure(ErrorCodeEnum.INTERFACE_INVALID));
            return false;
        }

        // JSON对象解析
        String body = "{}";
        try {
            if (request instanceof FShowsGenericRequestWrapper) {
                body = ((FShowsGenericRequestWrapper) request).body();
            } else {
                body = FShowsGenericRequestWrapper.wrap(request).body();
            }
        } catch (Exception e) {
            LogUtil.error(log, "InterfaceInterceptor.preHandle >> 对象解析失败 ,body={}", body, e);
            // 对象解析失败
            WebUtil.sendResponse(response, BaseResponse.failure(ErrorCodeEnum.INTERFACE_INVALID));
            return false;
        }
        TreeMap<String, Object> sortedMap = Maps.newTreeMap();

        //请求头中取出签名、时间戳和随机字符串
        final String signature = ServletUtil.getHeaderIgnoreCase(request, CommonConstant.SIGN);
        final String timestamp = ServletUtil.getHeaderIgnoreCase(request, CommonConstant.TIMESTAMP);
        final String nonce = ServletUtil.getHeaderIgnoreCase(request, CommonConstant.NONCE);

        //签名处理
        String signMd5 = this.getMd5Sign(servletPath, body, sortedMap, nonce, timestamp);
        if (!signMd5.equalsIgnoreCase(signature)) {
            LogUtil.warn(log, "InterfaceInterceptor.preHandle >> 接口预校验 >> signature error, expected signature is {}", signMd5);
            WebUtil.sendResponse(response, BaseResponse.failure(ErrorCodeEnum.INTERFACE_INVALID));
            return false;
        }

        //判断时间是否是60s内
        long nowTime = DateUtil.currentSeconds();
        long reqTime = Long.parseLong(timestamp);
        long abs = Math.abs(nowTime - reqTime);
        if (abs > SECONDS) {
            LogUtil.warn(log, "InterfaceInterceptor.preHandle >> 接口预校验 >> 时间校验, 时间差值超过10分钟, 检查手机系统时间, nowTime={}, reqTime={} ", nowTime, reqTime);
            WebUtil.sendResponse(response, BaseResponse.failure(ErrorCodeEnum.INTERFACE_INVALID, "请校验手机系统时间是否准确"));
            return false;
        }

        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.INTERFACE_NONCE_REDIS_KEY, nonce));
        if (bucket.isExists()) {
            LogUtil.warn(log, "InterfaceInterceptor.preHandle >> 缓存存在 , nonce={}", nonce);
            WebUtil.sendResponse(response, BaseResponse.failure(ErrorCodeEnum.INTERFACE_INVALID));
            return false;
        }
        //存入缓存
        bucket.set(nonce, 5, TimeUnit.MINUTES);
        return true;
    }

    /**
     * 签名处理
     *
     * @param body
     * @param sortedMap
     * @return
     */
    private String getMd5Sign(String servletPath, String body, TreeMap<String, Object> sortedMap, String nonce, String timestamp) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(CommonConstant.NONCE, nonce);
        jsonObject.put(CommonConstant.TIMESTAMP, timestamp);
        // 加盐
        jsonObject.put(CommonConstant.SALT, "4dfL0R1IDSlwog5");
        jsonObject.put(CommonConstant.SIGN_CONTENT, body);
        // 参数排序
        sortedMap.putAll(jsonObject);
        String signStr = JSON.toJSONString(sortedMap);
        LogUtil.info(log, "InterfaceInterceptor.preHandle >> 签名前 ,servletPath = {} ,signStr={}", servletPath, signStr);
        return MD5Util.sign(signStr, CommonConstant.UTF8);
    }


}