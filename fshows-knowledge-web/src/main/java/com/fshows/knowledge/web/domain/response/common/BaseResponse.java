/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.knowledge.web.domain.response.common;

import cn.hutool.core.util.StrUtil;
import com.huike.nova.common.enums.ErrorCodeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version BaseResponse.java, v 0.1 2022-08-31 3:30 PM ruanzy
 */
@Data
public class BaseResponse<T> implements Serializable {

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 响应信息
     */
    private String message;

    private BaseResponse(String code, String message, T data) {
        this.data = data;
        this.code = code;
        this.message = message;
    }

    /**
     * 成功响应
     *
     * @param data 响应数据
     * @return 响应主体
     */
    public static <T> BaseResponse<T> success(T data) {
        final ErrorCodeEnum success = ErrorCodeEnum.SUCCESS;
        return new BaseResponse<>(success.getCode(), success.getMessage(), data);
    }

    /**
     * 成功响应（无返回数据）
     *
     * @return {@link BaseResponse<T> }
     * <AUTHOR>
     */
    public static <T> BaseResponse<T> success() {
        final ErrorCodeEnum success = ErrorCodeEnum.SUCCESS;
        return new BaseResponse<>(success.getCode(), success.getMessage(), null);
    }

    /**
     * 失败响应
     *
     * @param errorCodeEnum 错误码枚举
     * @return 响应实体类
     */
    public static BaseResponse<Object> failure(ErrorCodeEnum errorCodeEnum) {
        return new BaseResponse<>(errorCodeEnum.getCode(), errorCodeEnum.getMessage(), null);
    }

    /**
     * 失败响应，带异常信息
     *
     * @param errorCodeEnum 错误枚举
     * @param detailMsg     详细错误信息
     * @return 响应信息类
     */
    public static BaseResponse<Object> failure(ErrorCodeEnum errorCodeEnum, String detailMsg) {
        final BaseResponse<Object> response = failure(errorCodeEnum);
        if (StrUtil.isNotBlank(detailMsg)) {
            response.setMessage(detailMsg);
        }
        return response;
    }
}