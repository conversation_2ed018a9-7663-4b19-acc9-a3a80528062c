/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.fshows.knowledge.web.domain.request;

import lombok.Data;
import org.jetbrains.annotations.NotNull;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 */
@Data
public class PageRequest<T> {
    /**
     * 请求对象
     */
    T query;
    /**
     * 页码
     */
    @NotNull
    private Integer page;
    /**
     * 每页数量
     */
    @NotNull
    @Max(1000)
    @Min(1)
    private Integer pageSize;
}