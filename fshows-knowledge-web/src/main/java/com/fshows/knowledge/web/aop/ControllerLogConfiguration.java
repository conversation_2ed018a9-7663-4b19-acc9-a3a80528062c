/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.fshows.knowledge.web.aop;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import com.alibaba.fastjson.JSONObject;
import com.annimon.stream.function.Supplier;
import com.huike.nova.common.annotation.RestLog;
import com.huike.nova.common.annotation.RestNoArgsLog;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.enums.RequestMethodEnum;
import com.huike.nova.common.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version ControllerLogConfiguration.java, v 0.1 2022-12-21 09:59 zhangling
 */
@Slf4j
@Aspect
@Order(1)  //数字值越小越先执行，如果没有Order，spring会按照随机顺序执行切面
@Component
public class ControllerLogConfiguration {

    @Pointcut("execution(* com.fshows.knowledge.web.api..*.*(..))")
    public void normalControllerLog() {}

    /**
     * 不拦截回调接口
     */
    @Pointcut("execution(* com.fshows.knowledge.web.api.CallbackApi.*(..))")
    public void excludeControllerLog() {}

    @Pointcut("normalControllerLog() && !excludeControllerLog()")
    public void controllerLog() {}

    @Around("controllerLog()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTimeMillis = System.currentTimeMillis();
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        // 如果attributes为空，直接返回
        if (Objects.isNull(attributes)) {
            return joinPoint.proceed();
        }
        val loggerContentBuilder = new StringBuilder();
        loggerContentBuilder.append("<ControllerLog>|");
        try {
            HttpServletRequest request = attributes.getRequest();
            Signature signature = joinPoint.getSignature();
            Method method = ((MethodSignature) signature).getMethod();
            String requestUrl = request.getServletPath();
            String serverName = request.getServerName();

            // 如果有RestLog注解，则不打印，因为这个注解会有独立的日志打印
            boolean unRestLog = !method.isAnnotationPresent(RestLog.class);
            if (unRestLog) {
                // 先进 filter 在进 interceptor 最后进 aop
                if (method.isAnnotationPresent(RestNoArgsLog.class)) {
                    RestNoArgsLog restNoArgsLog = method.getAnnotation(RestNoArgsLog.class);
                    loggerContentBuilder.append(StrUtil.format("serverName={}^requestUrl={}^method={}^api={}.{}^notPrintLog=1^notPrintLogReason={}",
                            serverName, requestUrl, request.getMethod(), signature.getDeclaringTypeName(), signature.getName(), restNoArgsLog.desc()));
                } else {
                    String pathSegments = Supplier.Util.safe(() ->
                                    StringUtils.replace(requestUrl.substring(1), StringPool.SLASH, StringPool.COMMA), StringPool.EMPTY)
                            .get();

                    // POST模式
                    if (StringUtils.equalsIgnoreCase(RequestMethodEnum.POST.getValue(), request.getMethod())) {
                        // JSON对象
                        if (StrUtil.startWithIgnoreCase(request.getContentType(), ContentType.JSON.toString())) {
                            Object validParam = null;
                            for (val param : joinPoint.getArgs()) {
                                val isServlet = param instanceof HttpServletRequest || param instanceof HttpServletResponse;
                                if (!isServlet) {
                                    validParam = param;
                                    break;
                                }
                            }
                            loggerContentBuilder.append(StrUtil.format("requestUrl={}^pathSegments={}^method={}^api={}.{}^param={}",
                                    requestUrl, pathSegments, request.getMethod(), signature.getDeclaringTypeName(), signature.getName(), JSONObject.toJSONString(validParam)));
                        } else {
                            List<Object> paramList = CollectionUtil.newArrayList();
                            for (val param : joinPoint.getArgs()) {
                                val isServlet = param instanceof HttpServletRequest || param instanceof HttpServletResponse;
                                if (!isServlet) {
                                    if (param instanceof MultipartFile) {
                                        paramList.add(StrUtil.format("MultipartFile({})", ((MultipartFile) param).getOriginalFilename()));
                                    } else {
                                        paramList.add(param);
                                    }
                                }
                            }
                            loggerContentBuilder.append(StrUtil.format("requestUrl={}^pathSegments={}^method={}^api={}.{}^param={}",
                                    requestUrl, pathSegments, request.getMethod(), signature.getDeclaringTypeName(), signature.getName(), JSONObject.toJSONString(paramList)));
                        }
                    } else {
                        loggerContentBuilder.append(StrUtil.format("requestUrl={}^pathSegments={}^method={}^api={}.{}^param={}",
                                requestUrl, pathSegments, request.getMethod(), signature.getDeclaringTypeName(), signature.getName(), JSONObject.toJSONString(request.getParameterMap())));
                    }
                }
            }
            val accessToken = request.getHeader("access-token");
            if (StringUtils.isNotBlank(accessToken)) {
                loggerContentBuilder.append(StrUtil.format("^accessToken={}", accessToken));
            }

            val result = joinPoint.proceed();
            if (unRestLog) {
                val duration = System.currentTimeMillis() - startTimeMillis;
                loggerContentBuilder.append(StrUtil.format("^duration={}ms", duration));
                if (result instanceof SseEmitter) {
                    loggerContentBuilder.append("^result=").append(result);
                } else {
                    loggerContentBuilder.append("^result=").append(JSONObject.toJSONString(result));
                }
            }
            return result;
        } catch (Exception ex) {
            loggerContentBuilder.append(StrUtil.format("^exception={}", StringUtils.defaultString(ex.getMessage(), ex.toString())));
            throw ex;
        } finally {
            LogUtil.info(log, loggerContentBuilder.toString());
        }
    }
}