package com.fshows.knowledge.web.config;

import cn.hutool.core.date.SystemClock;
import com.huike.nova.common.util.LogUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;

/**
 * 增强异步线程
 *
 * <AUTHOR>
 * @date 2023/4/19 11:19
 * @copyright 2022 barm Inc. All rights reserved
 */
@Slf4j
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TaskDecoratorForMdc implements TaskDecorator {
    private String threadPoolName;

    @Override
    public @NotNull Runnable decorate(@NotNull Runnable runnable) {
        long currentTime = SystemClock.now();
        try {
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            return () -> {
                try {
                    MDC.setContextMap(contextMap);
                    runnable.run();
                } finally {
                    MDC.clear();
                }
            };
        } catch (Exception e) {
            LogUtil.error(log, "【异步线程池】异步任务执行过程中发生异常！耗时={}ms, 线程池名称={}", e, (SystemClock.now() - currentTime), threadPoolName);
            return runnable;
        }
    }
}