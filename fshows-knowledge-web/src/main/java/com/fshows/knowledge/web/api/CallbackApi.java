package com.fshows.knowledge.web.api;

import com.alibaba.fastjson.JSON;
import com.fshows.knowledge.service.business.YqService;
import com.fshows.knowledge.web.domain.mapper.CallbackApiObjMapper;
import com.fshows.knowledge.web.domain.request.YuQueWebHookRequest;
import com.huike.nova.common.util.LogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 知识库管理系统/CallbackApi
 *
 * <AUTHOR> (<EMAIL>)
 * @version CallbackApi.java, v1.0 2024/6/3 23:26 John Exp$
 */
@RestController
@RequestMapping("/callback")
@AllArgsConstructor
@Slf4j
public class CallbackApi {

    YqService yqService;
    CallbackApiObjMapper callbackApiObjMapper;

    @PostMapping("/yu-que-webhook")
    public String handleYuQueWebhook(@RequestBody YuQueWebHookRequest request) {
        log.info(" yuQue webhook  payload: {}", JSON.toJSONString(request));
        yqService.handleYuQueWebhook(callbackApiObjMapper.toActivitySaveParam(request));
        return "success";
    }

    /**
     * 账户中台回调处理
     *
     * @param content
     * @return
     */
    @PostMapping("/acct-callback")
    public String acctCallback(@RequestParam Map<String, String> content) {
        return "";
    }
}
