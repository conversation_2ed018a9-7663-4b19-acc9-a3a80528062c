/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.fshows.knowledge.web.aop;

import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.DingTalkUtil;
import com.huike.nova.common.util.LogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version WebhookLogAop.java, v 0.1 2023-02-09 10:09 AM ruanzy
 */
@Slf4j
@Aspect
@Component
@AllArgsConstructor
public class WebhookLogAop {



    @Pointcut("@annotation(com.huike.nova.common.annotation.WebhookLog)")
    public void webhookLog() {
    }


    /**
     * 添加第三方接口日志
     *
     * @param joinPoint
     * @throws Throwable
     */
    @Around("webhookLog()")
    public void around(ProceedingJoinPoint joinPoint) throws Throwable {
        LogUtil.info(log, "WebhookLogAop.around >> 消息添加日志开始");
        try {
            String msgId = (String) joinPoint.getArgs()[1];
            Integer interfaceType = (Integer) joinPoint.getArgs()[2];
            String webhook = (String) joinPoint.getArgs()[3];


            //todo 测试
            StringBuilder dingMess = new StringBuilder();
            dingMess.append(String.format("抖音外卖订单有变化了！！！")).append(CommonConstant.NEWLINE)
                    .append("消息Msg-Id:").append(msgId).append(CommonConstant.NEWLINE);
            DingTalkUtil.sendAlarm(CommonConstant.URL, DingTalkUtil.buildMessage(dingMess.toString()));

        } catch (Exception e) {
            LogUtil.error(log, "WebhookLogAop.around >> 消息添加日志异常");
            throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("消息添加日志异常");
        }
        joinPoint.proceed();
        LogUtil.info(log, "WebhookLogAop.around >> 消息添加日志结束");
    }
}