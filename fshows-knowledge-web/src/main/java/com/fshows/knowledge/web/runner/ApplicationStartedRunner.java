package com.fshows.knowledge.web.runner;

import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.StrUtil;
import com.fshows.knowledge.service.business.common.DingDingCommonService;
import com.fshows.knowledge.web.FShowsKnowledgeWebApplication;
import com.huike.nova.common.config.SysConfig;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 程序完全执行后回调
 *
 * <AUTHOR> (<EMAIL>)
 * @version ApplicationStartedRunner.java, v1.0 11/22/2023 20:03 John Exp$
 */
@Component
@Slf4j
@AllArgsConstructor
public class ApplicationStartedRunner implements ApplicationRunner {

    @Autowired
    private DingDingCommonService dingDingCommonService;
    private SysConfig sysConfig;

    @Override
    public void run(ApplicationArguments args) {
        val ip = NetUtil.getLocalhostStr();
        long startUpdateTime = FShowsKnowledgeWebApplication.startUpTime;
//         dingDingCommonService.sendMessageDiscardEnv(
//                "知识库 knowledge", "应用已启动", StrUtil.format("**{}环境**knowledge服务已在服务器**{}**成功启动, 启动耗时：**{} ms**",
//                        sysConfig.getEnv().toUpperCase(),  ip, System.currentTimeMillis() - startUpdateTime)
//        );
    }
}
