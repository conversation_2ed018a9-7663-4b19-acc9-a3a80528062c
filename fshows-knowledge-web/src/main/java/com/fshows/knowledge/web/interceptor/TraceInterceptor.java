/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.knowledge.web.interceptor;

import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.util.TraceIdGenerator;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version TraceInterceptor.java, v 0.1 2022-11-02 11:53 AM ruanzy
 */
public class TraceInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, HttpServletResponse response, @NotNull Object handler) throws Exception {
        //添加阿里云日志ID
        String traceId = TraceIdGenerator.generate();
        String headerTraceId = request.getHeader("Traceid");
        if (StringUtils.isNotBlank(headerTraceId)) {
            traceId = headerTraceId;
        }
        MDC.put(CommonConstant.TRACE_ID, traceId);
        response.addHeader(CommonConstant.LTB_TRACE_ID, traceId);
        return true;
    }
}