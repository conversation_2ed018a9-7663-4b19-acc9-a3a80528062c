package com.fshows.knowledge.web;

import cn.dev33.satoken.SaManager;
import com.huike.nova.common.util.LogUtil;
import com.purgeteam.dynamic.config.starter.annotation.EnableDynamicConfigEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {
        "com.fshows.knowledge.web",
        "com.fshows.knowledge.service",
        "com.fshows.knowledge.dao",
        "com.huike.nova.common",
})
@EnableAsync
@EnableScheduling
@EnableTransactionManagement
@Import(cn.hutool.extra.spring.SpringUtil.class)
@EnableDynamicConfigEvent
@Slf4j
public class FShowsKnowledgeWebApplication {

    public static final Long startUpTime = System.currentTimeMillis();

    public static void main(String[] args) {
        SpringApplication.run(FShowsKnowledgeWebApplication.class, args);
        LogUtil.info(log, "启动成功：Sa-Token配置如下：", SaManager.getConfig());
    }

}
