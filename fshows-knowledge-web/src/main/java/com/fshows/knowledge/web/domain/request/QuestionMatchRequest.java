package com.fshows.knowledge.web.domain.request;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * QuestionMatchRequest
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Data
public class QuestionMatchRequest {
    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空")
    private String content;

    /**
     * 记录数
     */
    @Max(value = 20, message = "最多20条")
    @Min(value = 1, message = "最少1条")
    private Integer limit;
}