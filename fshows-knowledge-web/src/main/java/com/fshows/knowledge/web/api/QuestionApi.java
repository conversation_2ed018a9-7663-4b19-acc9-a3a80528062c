package com.fshows.knowledge.web.api;

import com.fshows.knowledge.service.business.QuestionService;
import com.fshows.knowledge.web.domain.mapper.QuestionApiObjMapper;
import com.fshows.knowledge.web.domain.request.BaseRequest;
import com.fshows.knowledge.web.domain.request.QuestionMatchRequest;
import com.fshows.knowledge.web.domain.response.GreetingResponse;
import com.fshows.knowledge.web.domain.response.common.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 知识库管理系统/QuestionApi
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@RestController
@RequestMapping("/knowledge")
@AllArgsConstructor
@Slf4j
public class QuestionApi {
    private QuestionApiObjMapper questionApiObjMapper;
    private QuestionService questionService;

    /**
     * 推荐问题及问候语
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/question/greeting")
    public BaseResponse<GreetingResponse> greeting(@Validated @RequestBody BaseRequest request) {
        return BaseResponse.success(questionApiObjMapper.toGreetingResponse(questionService.getGreeting()));
    }

    /**
     * 问题模糊匹配
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/question/question-match")
    public BaseResponse<List<String>> questionMatch(@Validated @RequestBody QuestionMatchRequest request) {
        return BaseResponse.success(questionService.questionMatch(questionApiObjMapper.toQuestionMatchParam(request)));
    }
}