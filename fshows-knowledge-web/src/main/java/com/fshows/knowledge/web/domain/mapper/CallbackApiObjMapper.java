/*
 * ailike.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.knowledge.web.domain.mapper;


import com.fshows.knowledge.service.domain.param.YuQueWebHookParam;
import com.fshows.knowledge.web.domain.request.YuQueWebHookRequest;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version componentModel.java
 */
@Mapper(componentModel = "spring")
public interface CallbackApiObjMapper {


    /**
     * 参数转换
     *
     * @param request 参数
     * @return 结果
     */
    YuQueWebHookParam toActivitySaveParam(YuQueWebHookRequest request);
}