package com.fshows.knowledge.web.api;

import com.fshows.knowledge.service.RagQueryService;
import com.fshows.knowledge.service.dto.QueryRequest;
import com.fshows.knowledge.service.dto.ChatResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * RAG查询API接口
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/rag")
@RequiredArgsConstructor
@Validated
@Tag(name = "RAG查询", description = "基于检索增强生成的智能问答接口")
public class RagApi {

    private final RagQueryService ragQueryService;

    /**
     * 智能问答查询接口
     * 
     * @param request 查询请求参数
     * @return 问答响应结果
     */
    @PostMapping("/query")
    @Operation(summary = "智能问答查询", description = "基于向量检索和大模型生成的智能问答服务")
    public ChatResponse query(
            @Parameter(description = "查询请求参数", required = true)
            @Valid @RequestBody QueryRequest request) {
        
        log.info("收到RAG查询请求: query={}, source={}", request.getQuery(), request.getQuerySource());
        
        try {
            ChatResponse response = ragQueryService.processQuery(request);
            log.info("RAG查询完成: messageId={}, success={}", response.getMessageId(), response.isSuccess());
            return response;
        } catch (Exception e) {
            log.error("RAG查询异常: query={}", request.getQuery(), e);
            return ChatResponse.builder()
                    .success(false)
                    .message("网络繁忙，请稍后重试")
                    .query(request.getQuery())
                    .build();
        }
    }

    /**
     * 健康检查接口
     * 
     * @return 服务状态
     */
    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "检查RAG服务运行状态")
    public String health() {
        return "RAG Service is running";
    }
}