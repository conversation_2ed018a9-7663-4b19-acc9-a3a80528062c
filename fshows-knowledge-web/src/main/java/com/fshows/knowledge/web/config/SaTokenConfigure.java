package com.fshows.knowledge.web.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.strategy.SaStrategy;
import cn.hutool.core.lang.UUID;
import com.fshows.knowledge.web.interceptor.InterfaceInterceptor;
import com.fshows.knowledge.web.interceptor.TraceInterceptor;
import com.fshows.knowledge.web.interceptor.UserInterceptor;
import com.huike.nova.common.config.SysConfig;
import org.hibernate.validator.HibernateValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.beanvalidation.MethodValidationPostProcessor;
import org.springframework.validation.beanvalidation.SpringValidatorAdapter;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.PostConstruct;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/1/28 14:57
 * @copyright 2022 barm Inc. All rights reserved
 */
@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {
    @Autowired
    private SysConfig sysConfig;

    @Bean
    public TraceInterceptor initTraceInterceptor() {
        return new TraceInterceptor();
    }

    @Bean
    public InterfaceInterceptor initInterfaceInterceptor() {
        return new InterfaceInterceptor();
    }

    @Bean
    public UserInterceptor initUserInterceptor() {
        return new UserInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 阿里云traceId
        registry.addInterceptor(initTraceInterceptor())
                .addPathPatterns("/**");
        Boolean signSwitch = sysConfig.getSignSwitch();
        if (!signSwitch) {
            registry.addInterceptor(initInterfaceInterceptor())
                    .addPathPatterns("/**")
                    .excludePathPatterns("/**");
        } else {
            registry.addInterceptor(initInterfaceInterceptor())
                    .addPathPatterns("/**")
                    .excludePathPatterns("/error")
                    .excludePathPatterns("/callback")
                    .excludePathPatterns("/callback/*")
                    .excludePathPatterns("/debug")
                    .excludePathPatterns("/gateway")
            ;
        }
        // 注册 Sa-Token 拦截器，定义详细认证规则
        registry.addInterceptor(new SaInterceptor(handler -> {
            // 指定 match 规则
            SaRouter.match("/**")    // 拦截的 path 列表
                    .notMatch(
                            Arrays.asList(
                                    "/error",
                                    "/callback",
                                    "/callback/*",
                                    "/knowledge/login",
                                    "/common/*",
                                    "/debug",
                                    "/debug/*",
                                    "/gateway"
                            ))
                    // 排除掉的 path 列表，可以写多个
                    .check(r -> StpUtil.checkLogin());        // 要执行的校验动作，可以写完整的 lambda 表达式
            // 根据路由划分模块，不同模块不同鉴权
            SaRouter.match("/user/**", r -> StpUtil.checkPermission("user"));
        })).addPathPatterns("/**");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // swagger配置
        registry
                .addResourceHandler("/swagger-ui/**")
                .addResourceLocations("classpath:/META-INF/resources/webjars/springfox-swagger-ui/")
                .resourceChain(false);
    }

    /**
     * cors 解决跨域
     *
     * @param registry
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowCredentials(true)
                .allowedOriginPatterns("*")
                .allowedHeaders("*")
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                .maxAge(3600);
    }


    /**
     * 重写字段检验
     *
     * @return
     */
    @Bean
    public Validator validator() {
        ValidatorFactory validatorFactory = Validation.byProvider(HibernateValidator.class)
                .configure()
                // 快速失败(Fail Fast)模式，设置为true即可，如果想验证全部，则设置为false或者取消配置即可
                .failFast(true)
                .buildValidatorFactory();

        return validatorFactory.getValidator();
    }

    /**
     * requestParam方式的校验
     *
     * @return
     */
    @Bean
    public MethodValidationPostProcessor methodValidationPostProcessor() {
        MethodValidationPostProcessor postProcessor = new MethodValidationPostProcessor();
        postProcessor.setValidator(validator());
        return postProcessor;
    }


    /**
     * 让spring的请求校验Validator使用我们上边的validator，让设置的failFast生效
     *
     * @return
     */
    @Override
    public org.springframework.validation.Validator getValidator() {
        return new SpringValidatorAdapter(validator());
    }

    /**
     * 重写 Sa-Token 框架内部算法策略
     */
    @PostConstruct
    public void rewriteSaStrategy() {
        // 重写 Token 生成策略
        SaStrategy.me.createToken = (loginId, loginType) -> {
            return UUID.randomUUID().toString(true);  // 随机60位长度字符串
        };
    }
}

