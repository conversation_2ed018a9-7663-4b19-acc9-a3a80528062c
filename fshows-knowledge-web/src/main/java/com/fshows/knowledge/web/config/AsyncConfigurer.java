package com.fshows.knowledge.web.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurerSupport;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2023/4/19 11:21
 * @copyright 2022 barm Inc. All rights reserved
 */
@Configuration
public class AsyncConfigurer extends AsyncConfigurerSupport {

    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setTaskDecorator(new TaskDecoratorForMdc());
        executor.initialize();
        return executor;
    }

}
