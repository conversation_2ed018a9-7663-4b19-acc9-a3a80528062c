package com.fshows.knowledge.web.domain.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询请求DTO
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryRequest {

    /**
     * 查询内容
     */
    private String query;

    /**
     * 最大结果数，默认5
     */
    private int maxResults = 5;

    /**
     * 最小相似度分数，默认0.7
     */
    private double minScore = 0.7;

    /**
     * 请求来源（不同数据来源要查询不同的向量库） 1 司南 app 2 浏览器插件
     */
    private int querySource;

}
