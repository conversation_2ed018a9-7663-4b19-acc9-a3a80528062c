/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.knowledge.web.interceptor;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.fshows.knowledge.web.domain.response.common.BaseResponse;
import com.huike.nova.common.constant.RedisPrefixConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.util.WebUtil;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @version UserInterceptor.java, v 0.1 2022-08-31 11:55 AM ruanzy
 */
public class UserInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);
        // 从请求头中取出token
        final String accessToken = ServletUtil.getHeaderIgnoreCase(request, "Access-Token");
        if (!StringUtils.isNotBlank(accessToken)) {
            //登录失效异常
            WebUtil.sendResponse(response, BaseResponse.failure(ErrorCodeEnum.LOGIN_INVALID));
            return false;
        }
        //缓存中获取
        final RBucket<String> bucket = redissonClient.getBucket(StrUtil.format(RedisPrefixConstant.KEY_LOGIN_INFO_ACCESS_TOKEN, accessToken));
        if (bucket.isExists()) {
            return true;
        }
        //登录失效异常
        WebUtil.sendResponse(response, BaseResponse.failure(ErrorCodeEnum.LOGIN_INVALID));
        return false;
    }
}