/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.fshows.knowledge.web.config;

import cn.hutool.core.date.SystemClock;
import com.huike.nova.common.constant.AsyncThreadConstant;
import com.huike.nova.common.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.RejectedExecutionHandler;

/**
 * 异步线程池配置
 *
 * <AUTHOR>
 * @version AsyncTaskExecutorConfig.java, v 0.1 2022-09-08 09:24 zhangling
 */
@Configuration
@Slf4j
public class AsyncTaskExecutorConfig {

    /**
     * 获取默认的任务丢弃策略
     * 默认策略：记录日志，直接丢弃任务
     *
     * @param threadPoolName 当前线程池名称，用于日志记录
     * @return
     */
    private static RejectedExecutionHandler getDefaultRejectedExecutionHandler(String threadPoolName) {
        return (r, executor) -> LogUtil.error(log, "【异步线程池】等待队列超过最大长度限制，拒绝任务！ 线程池名称={}，task={}", threadPoolName, r.toString());
    }

    /**
     * 获取默认的任务包装策略
     * 默认策略：catch住任务可能抛出的异常，避免异常信息的丢失
     *
     * @param threadPoolName 当前线程池名称，用于日志记录
     * @returnz
     */
    private TaskDecorator getDefaultTaskDecorator(String threadPoolName) {
        return runnable -> () -> {
            long currentTime = SystemClock.now();
            try {
                runnable.run();
            } catch (Exception e) {
                LogUtil.error(log, "【异步线程池】异步任务执行过程中发生异常！耗时={}ms, 线程池名称={}", e, (SystemClock.now() - currentTime), threadPoolName);
            }
        };
    }

    /**
     * 钉钉告警线程池
     *
     * @return 钉钉告警线程池
     */
    @Bean(AsyncThreadConstant.ALARM_TASK_EXECUTOR)
    public AsyncTaskExecutor alarmTaskExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setThreadNamePrefix("alarmTaskExecutor-");
        taskExecutor.setMaxPoolSize(1);
        taskExecutor.setCorePoolSize(1);
        taskExecutor.setKeepAliveSeconds(60);
        // 等待队列容量
        taskExecutor.setQueueCapacity(50);
        return setupExecutor(taskExecutor, AsyncThreadConstant.ALARM_TASK_EXECUTOR);
    }


    /**
     * 设置Executor
     *
     * @param taskExecutor   任务执行器
     * @param threadPoolName 线程池名称
     * @return 异步任务执行器
     */
    private static AsyncTaskExecutor setupExecutor(ThreadPoolTaskExecutor taskExecutor, String threadPoolName) {
        // 设置拒绝策略,当前策略：如等待队列已满则记录日志，并丢弃任务
        taskExecutor.setRejectedExecutionHandler(getDefaultRejectedExecutionHandler(threadPoolName));
        // 通过包装原有任务来捕获可能发生的异步任务抛出的异常
        taskExecutor.setTaskDecorator(new TaskDecoratorForMdc(threadPoolName));
        return taskExecutor;
    }

    /**
     * 导入商铺线程池
     *
     * @return 线程池对象
     */
    @Bean(AsyncThreadConstant.IMPORT_SHOP_EXECUTOR)
    public AsyncTaskExecutor importShopExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setThreadNamePrefix("importShopExecutor-");
        taskExecutor.setMaxPoolSize(20);
        taskExecutor.setCorePoolSize(5);
        taskExecutor.setKeepAliveSeconds(60);
        // 等待队列容量
        taskExecutor.setQueueCapacity(200);
        return setupExecutor(taskExecutor, AsyncThreadConstant.IMPORT_SHOP_EXECUTOR);
    }

    /**
     * 子母券变更线程池
     *
     * @return 线程池对象
     */
    @Bean(AsyncThreadConstant.PRODUCT_ALTERATION_NOTIFY_EXECUTOR)
    public AsyncTaskExecutor productAlterationExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setThreadNamePrefix("productAlterationExecutor-");
        taskExecutor.setMaxPoolSize(1);
        taskExecutor.setCorePoolSize(1);
        taskExecutor.setKeepAliveSeconds(60);
        return setupExecutor(taskExecutor, AsyncThreadConstant.PRODUCT_ALTERATION_NOTIFY_EXECUTOR);
    }
}