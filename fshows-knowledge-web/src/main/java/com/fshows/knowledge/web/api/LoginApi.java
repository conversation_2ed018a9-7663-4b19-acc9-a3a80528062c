package com.fshows.knowledge.web.api;

import com.fshows.knowledge.service.business.LoginService;
import com.fshows.knowledge.web.domain.mapper.LoginApiObjMapper;
import com.fshows.knowledge.web.domain.request.LoginRequest;
import com.fshows.knowledge.web.domain.response.LoginResponse;
import com.fshows.knowledge.web.domain.response.common.BaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 知识库管理系统/LoginApi
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Slf4j
@RestController
@RequestMapping("/knowledge")
@AllArgsConstructor
public class LoginApi {

    private LoginApiObjMapper loginApiObjMapper;
    private LoginService loginService;

    /**
     * 知识库登录
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/login")
    public BaseResponse<LoginResponse> login(@Validated @RequestBody LoginRequest request) {
        return BaseResponse.success(loginApiObjMapper.toLoginResponse(loginService.login(loginApiObjMapper.toLoginParam(request))));
    }

}