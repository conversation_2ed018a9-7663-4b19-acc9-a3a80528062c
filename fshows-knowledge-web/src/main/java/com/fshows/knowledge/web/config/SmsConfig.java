/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.knowledge.web.config;

import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.teaopenapi.models.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version SmsConfig.java, v 0.1 2022-09-28 3:27 PM ruanzy
 */
@Configuration
public class SmsConfig {

    /**
     * 短信客户端
     *
     * @return {@link Client }
     * @throws Exception 异常
     */
    @Bean
    public Client smsClient(@Value("${knowledge.aliyun.sms.ak}") String ak,
                            @Value("${knowledge.aliyun.sms.sk}") String sk) throws Exception {
        Config config = new Config()
                // 您的AccessKey ID
                .setAccessKeyId(ak)
                // 您的AccessKey Secret
                .setAccessKeySecret(sk);
        // 访问的域名
        config.endpoint = "dysmsapi.aliyuncs.com";
        return new Client(config);
    }
}