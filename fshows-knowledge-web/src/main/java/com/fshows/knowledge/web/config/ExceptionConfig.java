/*
 * ailike.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.knowledge.web.config;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.hutool.core.util.ObjectUtil;
import com.fshows.knowledge.web.domain.response.common.BaseResponse;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.spring.MyBatisSystemException;
import org.slf4j.MDC;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version ExceptionConfig.java, v 0.1 2022-09-08 09:24 zhangling
 */
@Slf4j
@ControllerAdvice
@RequiredArgsConstructor
public class ExceptionConfig {
    /**
     * 告警服务
     */

    /**
     * 获得TraceId
     *
     * @return 当前日志的TraceId
     */
    private String getTraceId(HttpServletResponse response) {
        String traceId = StringUtils.defaultIfEmpty(MDC.get(CommonConstant.TRACE_ID), response.getHeader(CommonConstant.LTB_TRACE_ID));
        return StringUtils.defaultString(traceId);
    }

    @ResponseBody
    @ExceptionHandler(value = Exception.class)
    public BaseResponse<Object> defaultExceptionHandler(HttpServletResponse response, Exception ex) {
        try {
            throw ex;
        } catch (NotLoginException e) {
            // 处理sa-token异常
            return BaseResponse.failure(ErrorCodeEnum.LOGIN_INVALID);
        } catch (NotRoleException e) {
            // 拦截：缺少角色异常
            return BaseResponse.failure(ErrorCodeEnum.LOGIN_INVALID, "角色权限不足");
        } catch (CommonException e) {
            LogUtil.warn(log, "ExceptionConfig.defaultExceptionHandler >> 异常兜底:业务异常, 错误: ", e);
             return BaseResponse.failure(e.getErrorCodeEnum(), e.getMsg());
        } catch (MethodArgumentNotValidException e) {
             return parseParamError(e)
                    .map(errMsg -> BaseResponse.failure(ErrorCodeEnum.PARAM_ERROR, errMsg))
                    .orElseGet(() -> BaseResponse.failure(ErrorCodeEnum.PARAM_ERROR));
        } catch (HttpMessageNotReadableException e) {
            LogUtil.error(log, "HttpMessageNotReadableException >> 全局异常 >> ex = {}", e);
            return BaseResponse.failure(ErrorCodeEnum.PARAM_ERROR, "参数不能为空");
        } catch (MyBatisSystemException e) {
            final Throwable cause = e.getCause();
            if (Objects.nonNull(cause)) {
                final Throwable commonException = cause.getCause();
                LogUtil.error(log, "ExceptionConfig.defaultExceptionHandler >> 全局异常 >> 未处理MyBatisSystemException异常:", ObjectUtil.defaultIfNull(cause, e));
                if (commonException instanceof CommonException) {
                    final CommonException exception = (CommonException) commonException;
                    return BaseResponse.failure(exception.getErrorCodeEnum(), exception.getMsg());
                }
            }
             return BaseResponse.failure(ErrorCodeEnum.SERVER_ERROR, "服务器繁忙，请稍后再试");
        } catch (Exception e) {
            LogUtil.error(log, "ExceptionConfig.defaultExceptionHandler >> 全局异常 >> 未处理异常: {}", e);
             return BaseResponse.failure(ErrorCodeEnum.SERVER_ERROR, "服务器繁忙，请稍后再试");
        }
    }

    private Optional<String> parseParamError(MethodArgumentNotValidException e) {
        Optional<String> result = Optional.empty();
        final FieldError fieldError = e.getFieldError();
        if (Objects.nonNull(fieldError)) {
            result = Optional.of(fieldError.getField() + "->" + fieldError.getDefaultMessage());
        }
        return result;
    }
}