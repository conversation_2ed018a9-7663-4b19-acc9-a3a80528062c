package com.fshows.knowledge.web.api;

import com.fshows.knowledge.service.business.FavoriteService;
import com.fshows.knowledge.service.domain.param.FavoriteListPageParam;
import com.fshows.knowledge.web.domain.mapper.FavoriteApiObjMapper;
import com.fshows.knowledge.web.domain.request.FavoriteCancelRequest;
import com.fshows.knowledge.web.domain.request.FavoriteListPageRequest;
import com.fshows.knowledge.web.domain.request.FavoriteSaveRequest;
import com.fshows.knowledge.web.domain.request.PageRequest;
import com.fshows.knowledge.web.domain.response.FavoriteListPageResponse;
import com.fshows.knowledge.web.domain.response.common.BaseResponse;
import com.fshows.knowledge.web.domain.response.common.PageResponse;
import com.huike.nova.common.metadata.PageParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 知识库管理系统/FavoriteApi
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Slf4j
@RestController
@RequestMapping("/knowledge")
@AllArgsConstructor
public class FavoriteApi {
    private FavoriteApiObjMapper favoriteApiObjMapper;
    private FavoriteService favoriteService;

    /**
     * 收藏问题
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/favorite/save")
    public BaseResponse<Boolean> favoriteSave(@Validated @RequestBody FavoriteSaveRequest request) {
        favoriteService.favoriteSave(favoriteApiObjMapper.toFavoriteSaveParam(request));
        return BaseResponse.success();
    }

    /**
     * 取消收藏
     *
     * @param request 参数
     * @return 结果
     */
    @PostMapping("/favorite/cancel")
    public BaseResponse<Boolean> favoriteCancel(@Validated @RequestBody FavoriteCancelRequest request) {
        favoriteService.favoriteCancel(favoriteApiObjMapper.toFavoriteCancelParam(request));
        return BaseResponse.success();
    }

    /**
     * 收藏分页列表
     *
     * @param pageRequest 入参
     * @return 出参
     */
    @PostMapping("/favorite/page-list")
    public BaseResponse<PageResponse<FavoriteListPageResponse>> shopPageList(@Validated @RequestBody PageRequest<FavoriteListPageRequest> pageRequest) {
        PageParam<FavoriteListPageParam> pageParam = favoriteApiObjMapper.toFavoriteListPageParam(pageRequest);
        return BaseResponse.success(favoriteApiObjMapper.toFavoriteListPageResponse(favoriteService.pageFavoriteList(pageParam)));
    }
}