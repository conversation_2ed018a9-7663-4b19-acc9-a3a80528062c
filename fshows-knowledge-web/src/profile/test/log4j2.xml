<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="300">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT" follow="true">
            <PatternLayout>
                <pattern>%-5p|%d{yyyy-MM-dd HH:mm:ss,SSS}|%X{TRACE_ID}|%t|%C{1}:%L|%m%n</pattern>
            </PatternLayout>
        </Console>
        <RollingFile name="RollingFile"
                     fileName="${sys:user.home}/logs/fshows-knowledge/fshows-knowledge.log"
                     filePattern="${sys:user.home}/logs/fshows-knowledge/fshows-knowledge.%d{yyyyMMdd}.log.gz"
                     append="true">
            <PatternLayout>
                <pattern>%-5p|%d{yyyy-MM-dd HH:mm:ss,SSS}|%X{TRACE_ID}|%t|%C{1}:%L|%m%n</pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingFile>
        <RollingFile name="ErrorRollingFile"
                     fileName="${sys:user.home}/logs/fshows-knowledge/fshows-knowledge-error.log"
                     filePattern="${sys:user.home}/logs/fshows-knowledge/fshows-knowledge-error.%d{yyyyMMdd}.log.gz"
                     append="true">
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout>
                <pattern>%-5p|%d{yyyy-MM-dd HH:mm:ss,SSS}|%X{TRACE_ID}|%t|%C{1}:%L|%m%n</pattern>
            </PatternLayout>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
        </RollingFile>

        <!--druid的日志记录追加器-->
        <RollingFile name="druidSqlRollingFile"
                     fileName="${sys:user.home}/logs/fshows-knowledge/druid-sql.log"
                     filePattern="${sys:user.home}/logs/fshows-knowledge/druid-sql.%d{yyyyMMdd}-%i.log.gz">
            <PatternLayout pattern="[%d{yyyy-MM-dd HH:mm:ss}] %-5level %L %M - %msg%xEx%n"/>
            <Policies>
                <SizeBasedTriggeringPolicy size="500 MB"/>
                <TimeBasedTriggeringPolicy/>
            </Policies>
        </RollingFile>

    </Appenders>
    <Loggers>
        <AsyncRoot level="INFO" includeLocation="true" additivity="false">
            <AppenderRef ref="RollingFile"/>
            <AppenderRef ref="Console"/>
            <AppenderRef ref="ErrorRollingFile"/>
        </AsyncRoot>

        <!--记录druid-sql的记录-->
        <logger name="druid.sql.Statement" level="debug" additivity="false">
            <appender-ref ref="Console"/>
            <appender-ref ref="druidSqlRollingFile"/>
        </logger>

    </Loggers>
</Configuration>