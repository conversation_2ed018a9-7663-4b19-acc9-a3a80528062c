//plugins {
//    id 'org.springframework.boot'
//    id 'io.spring.dependency-management'
//    id 'com.yimeibailing.gradle.plugin'
//}

// SpringBoot插件
apply plugin: 'org.springframework.boot'
// 医美百灵Dao生成插件的Gradle版本
apply plugin: 'com.yimeibailing.gradle.plugin'
// 环境切换脚本
apply from: '../scripts/env-profile.gradle'
description = '来团呗'

// 是否生成到老的DAO目录
def cfgExportToLegacyDaoFolder = Boolean.parseBoolean(System.getProperty("exportToLegacyPackageDaoFolder")) ?: false
def cfgTablePrefix = System.getProperty("tablePrefix")?: ""
println("是否导出DAO到com.huike.nova包中: $cfgExportToLegacyDaoFolder， 表前缀：$cfgTablePrefix")

// 定义SpringBoot的入口函数
// 配置可参考：https://www.baeldung.com/spring-boot-main-class
//springBoot {
//    mainClass = 'com.huike.nova.web.HuikeNovaWebApplication'
//}

bootJar {
    destinationDir = file("$projectDir/target/")
    clean.delete += destinationDir
}

jar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

// 医美百灵的mybatis-plus-gradle插件
ymbl {
    // 数据库配置
    dbConfig {
        // 数据库连接
        dbUrl = "********************************************************************************************"
        // 数据库用户名
        dbUserName = "fshows"
        // 数据库密码
        dbPassword = "Fshows12#\$"
        tablePrefix = cfgTablePrefix
        daoPackageName = cfgExportToLegacyDaoFolder ? "com.huike.nova" : "com.fshows.knowledge"
        daoModuleName = "huike-nova-dao"
        basedir = project.projectDir
    }
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-web'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    compileOnly 'org.springframework.boot:spring-boot-configuration-processor'
    implementation 'org.springframework.boot:spring-boot-starter-web'

    implementation project(":fshows-knowledge-service")
    // 数据库连接池
//    implementation 'com.alibaba:druid-spring-boot-starter:1.2.11'
    // 加密库
//    implementation 'com.github.ulisesbocchio:jasypt-spring-boot-starter:3.0.5'

    implementation 'org.yaml:snakeyaml:1.30'
//    implementation 'io.springfox:springfox-swagger2:3.0.0'
//    implementation 'io.springfox:springfox-swagger-ui:3.0.0'
    implementation 'org.springframework:spring-webmvc:5.3.22'
    implementation 'mysql:mysql-connector-java:8.0.30'
    implementation 'io.springfox:springfox-boot-starter:3.0.0'
    //nacos
    implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config:2021.0.4.0'
    implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap:3.1.9'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign:3.1.9'
}