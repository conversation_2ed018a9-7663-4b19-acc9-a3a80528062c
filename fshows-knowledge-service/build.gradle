plugins {
    id 'java-library'
}

group 'com.fshows.knowledge'
version '1.0.0'

dependencies {
    // 自带jar包引入
    api fileTree(include: ['*.jar'], dir: 'libs')

    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'

    implementation 'org.springframework:spring-web:5.3.22'
    implementation 'org.apache.tomcat.embed:tomcat-embed-core:9.0.65'

    implementation 'com.github.binarywang:weixin-java-miniapp:4.2.4.B'
    implementation 'com.github.binarywang:weixin-java-cp:4.2.4.B'

    implementation 'com.aliyun.openservices:ons-client:1.8.0.Final'

    implementation 'com.aliyun:aliyun-java-sdk-iot:7.49.0'
    implementation 'org.freemarker:freemarker:2.3.28'
    implementation 'commons-io:commons-io:2.5'
    implementation 'com.drewnoakes:metadata-extractor:2.18.0'
    implementation 'org:jaudiotagger:2.0.1'
    implementation 'com.alibaba.nls:nls-sdk-common:2.1.6'
    implementation 'org.springframework.boot:spring-boot-starter-websocket'
    implementation 'com.alipay.sdk:alipay-sdk-java:4.38.111.ALL'
    implementation 'com.fshows:fsframework-core:********'
    implementation("com.aliyun.oss:aliyun-sdk-oss:3.16.1")
    implementation("com.aliyun:alibaba-dingtalk-service-sdk:2.0.0")
    implementation 'com.dingtalk.open:app-stream-client:1.1.0'

    api 'org.springframework.cloud:spring-cloud-openfeign-core:3.1.9'
    api 'io.github.openfeign:feign-core:11.10'
    implementation 'io.github.openfeign:feign-okhttp:11.10'

    api project(":huike-nova-dao")

    implementation("com.unfbx:chatgpt-java:1.0.12") {
        exclude group: "org.jetbrains", module: "annotations"
    }
    


    // Swagger annotations
    implementation 'io.swagger.core.v3:swagger-annotations:2.2.8'
    
    // Validation
    implementation 'org.springframework.boot:spring-boot-starter-validation'
}

test {
    useJUnitPlatform()
}