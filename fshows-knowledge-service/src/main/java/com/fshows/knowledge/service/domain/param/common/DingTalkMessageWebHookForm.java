/**
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.fshows.knowledge.service.domain.param.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version DingTalkMessageWebHookForm.java, v 0.1 2022-03-15 11:58 上午 wangyi
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
public class DingTalkMessageWebHookForm {

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 钉钉webhook地址
     */
    private String webHookUrl;

    /**
     * 是否艾特所有人
     */
    private boolean atAll;
}
