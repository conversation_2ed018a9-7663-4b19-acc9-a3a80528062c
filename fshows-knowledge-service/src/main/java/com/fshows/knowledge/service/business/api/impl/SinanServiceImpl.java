package com.fshows.knowledge.service.business.api.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.fshows.knowledge.service.business.api.SinanService;
import com.fshows.knowledge.service.common.LoginUtil;
import com.fshows.knowledge.service.domain.model.CrmUserInfoModel;
import com.huike.nova.common.config.SysConfig;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * SinanServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Slf4j
@Service
@AllArgsConstructor
public class SinanServiceImpl implements SinanService {


    private SysConfig sysConfig;
    private static final String SINAN_TOKEN_VERIFY_URL = "/knowledge/token/info?deviceId={}&token={}";

    /**
     * 校验司南登态
     *
     * @param token    token
     * @param deviceId 社保Id
     * @return 结果
     */
    @Override
    public CrmUserInfoModel sinanTokenVerify(String token, String deviceId) {
        String postUrl = StrUtil.format("{}{}", sysConfig.getSinanHost(), StrUtil.format(SINAN_TOKEN_VERIFY_URL, deviceId, token));
        String content = HttpUtil.post(postUrl, "{}", 10000);
        LogUtil.info(log, "sinanTokenVerify >> 司南token验证 >> url={},content={}", postUrl, content);
        if (StringUtil.isEmpty(content)) {
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("token验证异常");
        }
        return JSON.parseObject(content, CrmUserInfoModel.class);
    }
}