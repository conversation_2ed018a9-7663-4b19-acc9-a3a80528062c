/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.knowledge.service.config;

import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.huike.nova.common.constant.AlipayConstant;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 支付宝开放平台配置
 *
 * <AUTHOR>
 * @version AlipayOpenApiConfig.java,
 */
@Configuration
public class AlipayOpenApiConfig {
    /**
     * 支付宝支付 json格式
     */
    private AlipayClient alipayClient = null;


    @Bean(name = "alipayGroupClient")
    public AlipayClient alipayGroupClient() {
        return new DefaultAlipayClient(
                "https://openapi.alipay.com/gateway.do",
                AlipayConstant.LTB_MALL_ALIPAY_APPID,
                AlipayConstant.LTB_MALL_ALIPAY_PRIVATE_KEY,
                "json", "GBK",
                AlipayConstant.ALIPAY_PUBLIC_KEY,
                "RSA2");
    }

}