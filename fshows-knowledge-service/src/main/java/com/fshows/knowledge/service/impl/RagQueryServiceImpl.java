package com.fshows.knowledge.service.impl;

import com.fshows.knowledge.service.RagQueryService;
import com.fshows.knowledge.service.dto.ChatResponse;
import com.fshows.knowledge.service.dto.QueryRequest;
import dev.langchain4j.community.model.dashscope.QwenChatModel;
import dev.langchain4j.community.model.dashscope.QwenEmbeddingModel;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import dev.langchain4j.store.embedding.EmbeddingStore;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * RAG查询服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
@AllArgsConstructor
public class RagQueryServiceImpl implements RagQueryService {

	private EmbeddingModel embeddingModel;
	private EmbeddingStoreFactory embeddingStoreFactory;
	private QwenChatModel chatModel;

	/**
	 * 初始化RAG组件
	 */
	@PostConstruct
	public void init() {
		log.info("RagAgent.init >> 开始初始化RAG组件");
		// 初始化Qwen嵌入模型
		String aliAiKey = System.getenv("ALI_AI_KEY");
		this.embeddingModel = QwenEmbeddingModel.builder()
				.apiKey(aliAiKey)
				.modelName("text-embedding-v4")
				.dimension(1024)
				.build();

		// 初始化Qwen聊天模型
		this.chatModel = QwenChatModel.builder()
				.apiKey(aliAiKey)
				.modelName("qwen-max")
				.build();

		log.info("RagAgent.init >> RAG组件初始化完成 >>");
	}

	@Override
	public ChatResponse processQuery(QueryRequest request) {
		try {
			log.info("开始处理RAG查询: query={}, source={}", request.getQuery(), request.getQuerySource());

			// 1. 获取对应的向量存储
			EmbeddingStore<TextSegment> embeddingStore = embeddingStoreFactory.getEmbeddingStore(request.getQuerySource());

			// 2. 将查询转换为向量
			Embedding queryEmbedding = embeddingModel.embed(request.getQuery()).content();

			// 构建搜索请求
			EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
					.queryEmbedding(queryEmbedding)
					.maxResults(10)
					.minScore(0.7)
					.build();

			// 执行搜索
			List<EmbeddingMatch<TextSegment>> matches = embeddingStore.search(searchRequest).matches();


			if (matches.isEmpty()) {
				log.warn("未找到相关文档: query={}", request.getQuery());
				return buildNoResultResponse(request);
			}

			// 4. 构建上下文
			// String context = buildContext(matches);

			// 5. 生成回答
			// String prompt = buildPrompt(request.getQuery(), context);
			// String answer = chatLanguageModel.generate(prompt);

			// 6. 构建响应
			return buildSuccessResponse(request, null, matches);

		} catch (Exception e) {
			log.error("RAG查询处理异常: query={}", request.getQuery(), e);
			return buildErrorResponse(request, e);
		}
	}

	/**
	 * 验证知识库访问权限
	 *
	 * @param querySource 查询来源
	 * @return 是否有权限访问
	 */
	@Override
	public boolean validateKnowledgeBaseAccess(Integer querySource) {
		return false;
	}


	/**
	 * 构建上下文信息
	 */
	private String buildContext(List<EmbeddingMatch<TextSegment>> matches) {
		return matches.stream()
				.map(match -> match.embedded().text())
				.collect(Collectors.joining("\n\n"));
	}

	/**
	 * 构建提示词
	 */
	private String buildPrompt(String query, String context) {
		return String.format(
				"基于以下上下文信息回答用户问题，如果上下文中没有相关信息，请说明无法找到相关信息。\n\n" +
						"上下文信息：\n%s\n\n" +
						"用户问题：%s\n\n" +
						"请提供准确、有用的回答：",
				context, query
		);
	}

	/**
	 * 构建成功响应
	 */
	private ChatResponse buildSuccessResponse(QueryRequest request, String answer, List<EmbeddingMatch<TextSegment>> matches) {
		List<ChatResponse.MatchResult> matchResults = matches.stream()
				.map(this::convertToMatchResult)
				.collect(Collectors.toList());

		return ChatResponse.builder()
				.success(true)
				.message("查询成功")
				.query(request.getQuery())
				.answer(matches.get(0).embedded().text())
				.messageId(UUID.randomUUID().toString())
				.matchResultList(matchResults)
				.build();
	}

	/**
	 * 构建无结果响应
	 */
	private ChatResponse buildNoResultResponse(QueryRequest request) {
		return ChatResponse.builder()
				.success(true)
				.message("未找到相关信息")
				.query(request.getQuery())
				.answer("抱歉，我没有找到与您问题相关的信息。请尝试换个方式提问或联系客服获取帮助。")
				.messageId(UUID.randomUUID().toString())
				.build();
	}

	/**
	 * 构建错误响应
	 */
	private ChatResponse buildErrorResponse(QueryRequest request, Exception e) {
		return ChatResponse.builder()
				.success(false)
				.message("网络繁忙，请稍后重试")
				.query(request.getQuery())
				.messageId(UUID.randomUUID().toString())
				.build();
	}

	/**
	 * 转换匹配结果
	 */
	private ChatResponse.MatchResult convertToMatchResult(EmbeddingMatch<TextSegment> match) {
		ChatResponse.MatchResult result = new ChatResponse.MatchResult();
		result.setText(match.embedded().text());
		result.setScore(match.score());

		// 从metadata中获取源信息
		TextSegment segment = match.embedded();
		if (segment.metadata() != null) {
			result.setSource(segment.metadata().getString("source"));
			result.setYuQueUrl(segment.metadata().getString("yuQueUrl"));
			result.setYuQueUrlAnchor(segment.metadata().getString("yuQueUrlAnchor"));
		}

		return result;
	}
}