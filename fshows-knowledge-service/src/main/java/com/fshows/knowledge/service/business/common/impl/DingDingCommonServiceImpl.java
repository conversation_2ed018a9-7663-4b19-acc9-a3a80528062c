package com.fshows.knowledge.service.business.common.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.annimon.stream.function.Supplier;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.fshows.knowledge.service.business.common.DingDingCommonService;
import com.fshows.knowledge.service.domain.dto.DingTalkNotifyMessageDTO;
import com.fshows.knowledge.service.domain.param.common.DingTalkMessageWebHookForm;
import com.fshows.knowledge.service.domain.param.common.DingtalkMessageForm;
import com.google.common.base.Splitter;
import com.huike.nova.common.config.SysConfig;
import com.huike.nova.common.constant.AsyncThreadConstant;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.DingTalkConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.SystemConstants;
import com.huike.nova.common.util.DingTalkUtil;
import com.huike.nova.common.util.LogUtil;
import com.taobao.api.ApiException;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.web.util.UriUtils;

import java.lang.reflect.UndeclaredThrowableException;
import java.nio.charset.StandardCharsets;

/**
 * DingDingCommonServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/19
 */
@Slf4j
@Service
@AllArgsConstructor
public class DingDingCommonServiceImpl implements DingDingCommonService {

    private SysConfig sysConfig;

    /**
     * 获得日志TRACE_id
     *
     * @return TRACE_ID或者空字符串
     */
    private String getTraceId() {
        return Supplier.Util.safe(() -> MDC.get(CommonConstant.TRACE_ID), StringPool.EMPTY).get();
    }




    @Override
    public void sendMessageDiscardEnv(String scene, String message, String content) {
        String token = sysConfig.getDingTalkDefaultToken();
        String secret = sysConfig.getDingTalkDefaultSecret();
        if (StringUtils.isAnyBlank(token, secret)) {
            return;
        }
        LogUtil.warn(log, "DingDingCommonServiceImpl.sendMessage >> " +
                "知识库服务WebHook通知, scene={}, message={}, content={}", scene, message, content);
        val messageDTO = new DingTalkNotifyMessageDTO()
                .setScene(scene)
                .setKeyword(getTraceId())
                .setMessage(message)
                .setBody(content);
        JSONObject notifyObj = buildDingTalkNotifyJson(messageDTO, false);
        DingTalkUtil.sendAlarm(token, secret, notifyObj.toString());
    }

    /**
     * 发送钉钉通知消息结果
     *
     * @param scene   场景
     * @param message 消息内容
     * @param body    消息通知
     */
    @Async(AsyncThreadConstant.ALARM_TASK_EXECUTOR)
    @Override
    public void sendCallbackNotificationMessage(String scene, String message, String body) {
        if (!sysConfig.isProd()) {
            return;
        }
        val current = new DingTalkNotifyMessageDTO()
                .setScene(scene)
                .setMessage(message)
                .setKeyword(getTraceId())
                .setBody(body);
        DingTalkUtil.sendAlarm(sysConfig.getDingTalkDefaultToken(), sysConfig.getDingTalkDefaultSecret(),
                buildDingTalkNotifyJson(current, false).toJSONString());
    }

    /**
     * 发送钉钉警报
     *
     * @param scene     场景
     * @param traceId   tranceId
     * @param throwable 异常
     */
    @Override
    @Async(AsyncThreadConstant.ALARM_TASK_EXECUTOR)
    public void sendGlobalErrorAlarmMessage(String scene, String traceId, Throwable throwable) {
        // 只有PROD环境的才进行上报
        if (!sysConfig.isProd()) {
            return;
        }
        // 获得StackTrace
        try {
            Throwable error = (throwable instanceof UndeclaredThrowableException) ?
                    ((UndeclaredThrowableException) (throwable)).getUndeclaredThrowable() : throwable;
            val stackTraces = error.getStackTrace();
            String errorLocation = "未定义";
            for (val stackTrace : stackTraces) {
                if (stackTrace.getClassName().startsWith(SystemConstants.HUIKE_NOVA_PAGE_PREFIX)) {
                    errorLocation = StrUtil.format("{}.{}() [{}:{}]", stackTrace.getClassName(), stackTrace.getMethodName(), stackTrace.getFileName(), stackTrace.getLineNumber());
                    break;
                }
            }
            val current = new DingTalkNotifyMessageDTO()
                    .setScene(scene).setKeyword(traceId).setMessage(errorLocation).setBody(error.toString());
//            DingTalkUtil.sendAlarm(sysConfig.getOpenCouponToken(), buildDingTalkNotifyJson(current, true).toJSONString());
        } catch (Throwable t) {
            LogUtil.info(log, "DingDingCommonServiceImpl.sendGlobalErrorAlarmMessage >> 发送钉钉警报失败", t);
        }
    }

    /**
     * 组合钉钉通知JSON
     *
     * @param pojo 通知消息体
     * @return 通知Json
     */
    private JSONObject buildDingTalkNotifyJson(@NonNull DingTalkNotifyMessageDTO pojo, boolean important) {
        val json = new JSONObject();
        json.put("msgtype", "actionCard");
        // 动作卡片
        val actionCard = new JSONObject();
        actionCard.put("title", "知识库");
        String logo = important ? "![](https://cp.51fubei.com/sundry/ltb-warning.jpg)\n" : StringPool.EMPTY;
        actionCard.put("text", logo + "#### 【知识库】业务报警\n" +
                "##### 场景：" + pojo.getScene() + "\n" +
                "##### 描述：" + pojo.getMessage() + "\n" +
                "> " + pojo.getBody().replace("\\\"", "'")
        );
        actionCard.put("btnOrientation", "1");
        // 日志查看

        val buttonJson = new JSONObject();
        buttonJson.put("title", "查看日志");
        val keyword = StringUtils.isNotBlank(pojo.getKeyword()) ? pojo.getKeyword() : pojo.getMessage();
        buttonJson.put("actionURL", getSlsUrl(keyword));
        val buttonJsonList = new JSONArray(1);
        buttonJsonList.add(buttonJson);
        actionCard.put("btns", buttonJsonList);
        json.put("actionCard", actionCard);
        return json;
    }

    /**
     * 佣金的钉钉告警
     *
     * @param dingtalkMessageForm 参数
     */
    @Override
    public void sendCommissionDingTalkMessage(DingtalkMessageForm dingtalkMessageForm) {
        DingTalkMessageWebHookForm form = new DingTalkMessageWebHookForm();
        form.setTitle(dingtalkMessageForm.getTitle());
        form.setContent("## " + dingtalkMessageForm.getContent().replace("\n", "\n > - "));
        form.setWebHookUrl(DingTalkConstant.COMMISSION_WEB_HOOK_URL);
        if (dingtalkMessageForm.getTitle().contains(DingTalkConstant.ALARM) || form.getContent().contains(DingTalkConstant.ALARM)) {
            form.setAtAll(true);
        }
        this.sendDingDingWebHookMessage(form);
    }

    /**
     * 授权服务商群钉钉告警
     *
     * @param dingtalkMessageForm 参数
     */
    @Override
    public void sendServiceProviderDingTalkMessage(DingtalkMessageForm dingtalkMessageForm) {
        DingTalkMessageWebHookForm form = new DingTalkMessageWebHookForm();
        form.setTitle(dingtalkMessageForm.getTitle());
        form.setContent("## " + dingtalkMessageForm.getContent().replace("\n", "\n > - "));
        form.setWebHookUrl(DingTalkConstant.SERVICE_PROVIDER_DING_TALK_URL);
        if (StringUtils.isNotBlank(dingtalkMessageForm.getWebHookUrl())) {
            form.setWebHookUrl(dingtalkMessageForm.getWebHookUrl());
        }
        if (dingtalkMessageForm.getTitle().contains(DingTalkConstant.ALARM) || form.getContent().contains(DingTalkConstant.ALARM)) {
            form.setAtAll(true);
        }
        this.sendDingDingWebHookMessage(form);
    }

    @Override
    public void sendDingMessage(DingTalkMessageWebHookForm form) {
        this.sendDingDingWebHookMessage(form);
    }

    /**
     * webhook推送钉钉通知
     *
     * @param form 参数
     */
    public void sendDingDingWebHookMessage(DingTalkMessageWebHookForm form) {
        DingTalkClient client = new DefaultDingTalkClient(form.getWebHookUrl());
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setIsAtAll(form.isAtAll());
        request.setAt(at);
        request.setMsgtype("markdown");
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle(form.getTitle());
        markdown.setText(form.getContent());
        request.setMarkdown(markdown);
        try {
            OapiRobotSendResponse response = client.execute(request);
            JSONObject.toJSONString(response);
        } catch (ApiException e) {
            LogUtil.error(log, "sendDingDingWebHookMessage === 发送钉钉通知失败 form:{}", form, e);
            JSONObject.toJSONString(e);
        }
    }


    @SuppressWarnings("VulnerableCodeUsages")
    @Override
    public String getSlsUrl(String keyword) {
        val sb = new StringBuilder("*");
        if (StringUtils.isNotBlank(keyword)) {
            val queryString = Splitter.on(",").omitEmptyStrings().trimResults()
                    .splitToList(keyword).stream().findFirst().orElse(StringPool.EMPTY);
            if (StringUtils.isNotBlank(queryString)) {
                sb.append(StringPool.SPACE).append(" AND ").append(queryString);
            }
        }
        // 组合Url
        val url = UriComponentsBuilder.fromUriString("https://sls.console.aliyun.com/lognext/project/fshows-ecs-pro/logsearch/fshows-gosh")
                .queryParam("encode", "base64")
                .queryParam("queryTimeType", 5)
                .queryParam("queryString", Base64.encode(sb.toString()))
                .build().toUriString();
        return CommonConstant.DING_TALK_URL_SCHEME_PREFIX + UriUtils.encode(url, StandardCharsets.UTF_8);
    }
}