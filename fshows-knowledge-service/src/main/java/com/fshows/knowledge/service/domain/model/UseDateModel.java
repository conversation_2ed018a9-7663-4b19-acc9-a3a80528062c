package com.fshows.knowledge.service.domain.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022年10月26日 11:29
 */
@Data
public class UseDateModel {

    /**
     * 使用日期类型:1、指定日期;2、指定天数
     * USE_DATE-use_date_type
     */
    @JSONField(name = "use_date_type")
    private Integer useDateType;

    /**
     * 使用开始日期
     * USE_DATE-day_duration-use_start_date
     */
    @JSONField(name = "use_start_date")
    private String useStartDate;

    /**
     * 使用结束日期
     * USE_DATE-day_duration-use_end_date
     */
    @JSONField(name = "use_end_date")
    private String useEndDate;

    /**
     * 可使用天数,最大不能超过360天
     * USE_DATE-day_duration
     */
    @JSONField(name = "day_duration")
    private Integer dayDuration;

}
