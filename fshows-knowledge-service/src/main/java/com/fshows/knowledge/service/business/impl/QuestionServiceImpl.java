package com.fshows.knowledge.service.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fshows.knowledge.dao.entity.BotChannelConfigDO;
import com.fshows.knowledge.dao.entity.BotKnowledgeDO;
import com.fshows.knowledge.dao.entity.BotUsersDO;
import com.fshows.knowledge.dao.repository.BotChannelConfigDAO;
import com.fshows.knowledge.dao.repository.BotKnowledgeDAO;
import com.fshows.knowledge.dao.repository.BotSourceDataDAO;
import com.fshows.knowledge.dao.repository.BotUsersDAO;
import com.fshows.knowledge.service.business.QuestionService;
import com.fshows.knowledge.service.common.LoginUtil;
import com.fshows.knowledge.service.domain.model.GreetingModel;
import com.fshows.knowledge.service.domain.model.LoginModel;
import com.fshows.knowledge.service.domain.param.QuestionMatchParam;
import com.huike.nova.common.config.SysConfig;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * QuestionServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Slf4j
@Service
@AllArgsConstructor
public class QuestionServiceImpl implements QuestionService {

    private BotUsersDAO botUsersDAO;
    private BotChannelConfigDAO botChannelConfigDAO;
    private BotSourceDataDAO botSourceDataDAO;
    private BotKnowledgeDAO botKnowledgeDAO;
    private SysConfig sysConfig;

    /**
     * 获取推荐问题和问候语
     *
     * @return 结果
     */
    @Override
    public GreetingModel getGreeting() {
        LoginModel loginBasicInfo = LoginUtil.getLoginBasicInfo();
        BotUsersDO usersDO = botUsersDAO.getByUserName(loginBasicInfo.getUserName());
        if (ObjectUtil.isNull(usersDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("用户不存在");
        }
        BotChannelConfigDO channelConfigDO = botChannelConfigDAO.getBySource(usersDO.getUserType());
        // 获取问候语
        GreetingModel model = new GreetingModel();
        if (ObjectUtil.isNotNull(channelConfigDO)) {
            model.setGuideTitle(channelConfigDO.getGuideTitle());
            model.setOpeningGreeting(channelConfigDO.getOpeningGreeting());
            model.setAnswerStartText(channelConfigDO.getAnswerStartText());
            model.setAnswerEndText(channelConfigDO.getAnswerEndText());
        }
        // 获取推荐问题
        String recommendQuestion = sysConfig.getRecommendQuestion();
        if (StringUtil.isNotEmpty(recommendQuestion)) {
            model.setRecommendQuestionList(Arrays.asList(recommendQuestion.split(",")));
        }
        return model;
    }

    /**
     * 匹配问题
     *
     * @param param 参数
     * @return 结果
     */
    @Override
    public List<String> questionMatch(QuestionMatchParam param) {
        //  获取登录态
        LoginModel loginBasicInfo = LoginUtil.getLoginBasicInfo();
        BotUsersDO usersDO = botUsersDAO.getByUserName(loginBasicInfo.getUserName());
        if (ObjectUtil.isNull(usersDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("用户不存在");
        }
        // 根据用户类型获取所属知识库
        BotKnowledgeDO knowledgeDO = botKnowledgeDAO.getBySource(usersDO.getUserType());
        if (ObjectUtil.isNull(knowledgeDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("用户所属知识库配置不存在");
        }
        // 模糊匹配
        List<String> list = botSourceDataDAO.findQuestionMatch(knowledgeDO.getKnowledgeId(), param.getContent(), param.getLimit());
        if (CollectionUtil.isEmpty(list)) {
            return CollectionUtil.newArrayList();
        }
        return list;
    }
}