/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.knowledge.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fshows.knowledge.service.business.YqService;
import com.huike.nova.common.util.StringUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 结算对账脚本（支持重跑）
 *
 * <AUTHOR>
 * @version CreateSettleFormJobHandle.java, v 0.1 2024-07-08 4:42 PM ruanzy
 */
@Component
@Slf4j
@JobHandler("bookArticleDownloadJobHandle")
@AllArgsConstructor
public class BookArticleDownloadJobHandle extends IJobHandler {

    private YqService yqService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("BookArticleDownloadJobHandle >> 语雀知识库下载文章执行开始：time = {}", DateUtil.now());
        String bookId = StringUtils.EMPTY;
        String articleId = StringUtils.EMPTY;
        if (StringUtils.isNotBlank(param)) {
            JSONObject jsonObject = JSON.parseObject(param);
            bookId = jsonObject.get("bookId").toString();
            articleId = jsonObject.get("articleId").toString();
        }
        yqService.bookArticleDownload(bookId, articleId);
        XxlJobLogger.log("BookArticleDownloadJobHandle >> 语雀知识库下载文章执行结束：time = {}", DateUtil.now());
        return ReturnT.SUCCESS;
    }
}