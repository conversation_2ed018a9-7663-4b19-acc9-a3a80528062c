/*
 * ailike.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.knowledge.service.domain.mapper;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.knowledge.dao.domain.param.FavoriteListPageParamDTO;
import com.fshows.knowledge.dao.domain.result.FavoriteListPageResultDTO;
import com.fshows.knowledge.service.domain.model.FavoriteListPageModel;
import com.fshows.knowledge.service.domain.param.FavoriteListPageParam;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version componentModel.java
 */
@Mapper(componentModel = "spring")
public interface FavoriteServiceObjMapper {

    /**
     * 转换
     *
     * @param request 入参
     * @return 出参
     */
    PageParam<FavoriteListPageParamDTO> toFavoriteListPageParamDTO(PageParam<FavoriteListPageParam> request);


    /**
     * 转换
     *
     * @param page 入参
     * @return 出参
     */
    PageResult<FavoriteListPageModel> toFavoriteListPageModel(Page<FavoriteListPageResultDTO> page);


}