package com.fshows.knowledge.service.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * mq配置
 *
 * <AUTHOR>
 * @version MQConfig.java, v 0.1 2022-03-10 4:49 下午 mayucong
 */
@Data
@Configuration
public class MQConfig {

    /**
     * mq 签名认证信息
     */
    @Value("${mq.access-key}")
    private String accesskey;
    @Value("${mq.secret-key}")
    private String secretkey;

    /**
     * MQ namesrv 地址
     */
    @Value("${mq.namesrv.address}")
    private String mqNamesrvAddress;

    /**
     * 通用topicId
     */
    @Value("${mq.common.topic-id}")
    private String commonTopicId;


    // /**
    //  * 通用导出业务
    //  */
    // @Value("${mq.export.common.gid}")
    // private String exportCommonGid;
    // @Value("${mq.export.common.tag}")
    // private String exportCommonTag;
    // @Value("${mq.export.common.prefix}")
    // private String exportCommonPrefix;



}
