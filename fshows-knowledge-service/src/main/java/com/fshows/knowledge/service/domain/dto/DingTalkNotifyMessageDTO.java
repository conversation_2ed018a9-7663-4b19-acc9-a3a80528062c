package com.fshows.knowledge.service.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 钉钉通知消息
 *
 * <AUTHOR> (<EMAIL>)
 * @version DingTalkNotifyMessagePojo.java, v1.0 05/23/2023 19:04 John Exp$
 */
@Data
@Accessors(chain = true)
public class DingTalkNotifyMessageDTO {
    /**
     * 场景
     */
    private String scene;

    /**
     * 消息
     */
    private String message;

    /**
     * body内容
     */
    private String body;

    /**
     * SLS关键字
     */
    private String keyword;
}
