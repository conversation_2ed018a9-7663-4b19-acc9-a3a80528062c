package com.fshows.knowledge.service.business.api.impl;

import com.alibaba.fastjson.JSONObject;
import com.fshows.knowledge.service.business.api.YqOpenService;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * 语雀开放接口服务实现类
 *
 * <p>负责调用语雀API获取文档导出链接，支持markdown格式导出</p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Slf4j
@Service
@AllArgsConstructor
public class YqOpenServiceImpl implements YqOpenService {

    private static final OkHttpClient okHttpClient = new OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .build();

    /**
     * 语雀文档导出API URL模板
     */
    private static final String YUQUE_EXPORT_URL_TEMPLATE = "https://fshows.yuque.com/api/docs/%s/export";

    /**
     * 下载语雀文章，获取导出URL
     *
     * <p>调用语雀导出API，解析响应获取文档的下载链接</p>
     *
     * @param docId 文档ID，语雀文档的唯一标识符
     * @param cookie cookie
     * @param csrfToken csrfToken
     * @return 导出文章的下载URL，可直接用于下载markdown文件（需要登录态）
     * @throws RuntimeException 当请求失败或响应格式异常时抛出
     */
    @Override
    public String downloadYuQueArticle(String docId, String cookie, String csrfToken) {
        // 构建请求URL
        String url = String.format(YUQUE_EXPORT_URL_TEMPLATE, docId);
        LogUtil.info(log, "downloadYuQueArticle >> 开始请求语雀文档导出，docId: {}, url: {}", docId, url);

        // 构建请求体，指定导出格式和选项
        JSONObject requestBody = new JSONObject();
        requestBody.put("type", "markdown");
        requestBody.put("force", 0);
        requestBody.put("options", "{\"latexType\":1,\"enableAnchor\":1}");

        // 创建请求体
        RequestBody body = RequestBody.create(
            requestBody.toJSONString(),
            MediaType.parse("application/json"));

        // 构建HTTP请求
        Request request = new Request.Builder()
            .url(url).post(body)
            .addHeader("Accept", "application/json")
            .addHeader("Content-Type", "application/json")
            .addHeader("Cookie", cookie)
            .addHeader("x-csrf-token", csrfToken)
            .build();
        
        try (Response response = okHttpClient.newCall(request).execute()) {
            // 检查HTTP响应状态
            if (!response.isSuccessful()) {
                LogUtil.error(log, "downloadYuQueArticle >> 语雀文档导出请求失败，HTTP状态码: {}, docId: {}", response.code(), docId);
                throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("语雀文档导出请求失败: HTTP " + response.code());
            }

            // 获取响应体
            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                LogUtil.error(log, "downloadYuQueArticle >> 语雀文档导出响应体为空，docId: {}", docId);
                throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("语雀文档导出响应体为空");
            }

            // 解析JSON响应
            String responseContent = responseBody.string();
            LogUtil.info(log, "downloadYuQueArticle >> 语雀文档导出响应内容: {}", responseContent);

            JSONObject responseJson = JSONObject.parseObject(responseContent);
            if (responseJson == null) {
                LogUtil.error(log, "downloadYuQueArticle >> 语雀文档导出响应JSON解析失败，docId: {}", docId);
                throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("语雀文档导出响应JSON解析失败");
            }

            // 获取data字段
            JSONObject data = responseJson.getJSONObject("data");
            if (data == null) {
                LogUtil.error(log, "downloadYuQueArticle >> 语雀文档导出响应缺少data字段，docId: {}", docId);
                throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("语雀文档导出响应缺少data字段");
            }

            // 检查导出状态
            String state = data.getString("state");
            if (!"success".equals(state)) {
                LogUtil.error(log, "downloadYuQueArticle >> 语雀文档导出状态异常，state: {}, docId: {}", state, docId);
                throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("语雀文档导出状态异常: " + state);
            }

            // 获取下载URL
            String downloadUrl = data.getString("url");
            if (downloadUrl == null || downloadUrl.trim().isEmpty()) {
                LogUtil.error(log, "downloadYuQueArticle >> 语雀文档导出URL为空，docId: {}", docId);
                throw new CommonException(ErrorCodeEnum.ORDER_ERROR).detailMessage("语雀文档导出URL为空");
            }

            LogUtil.info(log, "downloadYuQueArticle >> 语雀文档导出成功，docId: {}, downloadUrl: {}", docId, downloadUrl);
            return downloadUrl;
            
        } catch (IOException e) {
            LogUtil.error(log, "downloadYuQueArticle >> 语雀文档导出网络异常，docId: {}", docId);
            throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("语雀文档导出网络异常");
        }catch (Exception e){
            LogUtil.error(log, "downloadYuQueArticle >> 语雀文档导出异常，docId: {}", docId);
            throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("语雀文档导出异常");
        }
    }

    /**
     * 获取语雀文章内容
     *
     * <p>通过下载URL获取语雀文档的markdown内容</p>
     *
     * @param downloadUrl 文章下载URL，通过downloadYuQueArticle方法获取
     * @param cookie cookie
     * @param csrfToken csrfToken
     * @return 文章的markdown内容
     * @throws RuntimeException 当请求失败或响应格式异常时抛出
     */
    @Override
    public String getYuQueArticleContent(String downloadUrl, String cookie, String csrfToken) {
        LogUtil.info(log, "getYuQueArticleContent >> 开始获取语雀文章内容，downloadUrl: {}", downloadUrl);

        // 构建HTTP请求
        Request request = new Request.Builder()
            .url(downloadUrl)
            .get()
            .addHeader("x-csrf-token", csrfToken)
            .addHeader("Cookie", cookie)
            .build();

        try (Response response = okHttpClient.newCall(request).execute()) {
            // 检查HTTP响应状态
            if (!response.isSuccessful()) {
                LogUtil.error(log, "getYuQueArticleContent >> 获取语雀文章内容请求失败，HTTP状态码: {}, downloadUrl: {}", response.code(), downloadUrl);
                throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("获取语雀文章内容请求失败: HTTP " + response.code());
            }

            // 获取响应体
            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                LogUtil.error(log, "getYuQueArticleContent >> 语雀文章内容响应体为空，downloadUrl: {}", downloadUrl);
                throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("语雀文章内容响应体为空");
            }

            // 获取文章内容
            String articleContent = responseBody.string();
            LogUtil.info(log, "getYuQueArticleContent >> 语雀文章内容获取成功，内容长度: {}, downloadUrl: {}",
		            articleContent.length(), downloadUrl);
            
            return articleContent;
            
        } catch (IOException e) {
            LogUtil.error(log, "getYuQueArticleContent >> 获取语雀文章内容网络异常，downloadUrl: {}, error: {}", downloadUrl, e.getMessage());
            throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("获取语雀文章内容网络异常");
        } catch (Exception e) {
            LogUtil.error(log, "getYuQueArticleContent >> 获取语雀文章内容异常，downloadUrl: {}, error: {}", downloadUrl, e.getMessage());
            throw new CommonException(ErrorCodeEnum.EXTERNAL_ERROR).detailMessage("获取语雀文章内容异常");
        }
    }
}
