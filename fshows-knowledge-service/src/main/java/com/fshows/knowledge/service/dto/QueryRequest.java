package com.fshows.knowledge.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 查询请求DTO
 * 
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "RAG查询请求参数")
public class QueryRequest {

    /**
     * 查询内容
     */
    @NotBlank(message = "查询内容不能为空")
    @Schema(description = "用户查询问题", example = "如何使用RAG技术？", required = true)
    private String query;

    /**
     * 最大结果数，默认5
     */
    @Min(value = 1, message = "最大结果数不能小于1")
    @Max(value = 20, message = "最大结果数不能大于20")
    @Schema(description = "最大检索结果数", example = "5", defaultValue = "5")
    private int maxResults = 5;

    /**
     * 最小相似度分数，默认0.7
     */
    @Min(value = 0, message = "最小相似度分数不能小于0")
    @Max(value = 1, message = "最小相似度分数不能大于1")
    @Schema(description = "最小相似度阈值", example = "0.7", defaultValue = "0.7")
    private double minScore = 0.7;

    /**
     * 请求来源（不同数据来源要查询不同的向量库）
     * 1 司南 app 2 浏览器插件
     */
    @NotNull(message = "请求来源不能为空")
    @Min(value = 1, message = "请求来源值无效")
    @Max(value = 2, message = "请求来源值无效")
    @Schema(description = "请求来源类型", example = "1", allowableValues = {"1", "2"})
    private Integer querySource;
}