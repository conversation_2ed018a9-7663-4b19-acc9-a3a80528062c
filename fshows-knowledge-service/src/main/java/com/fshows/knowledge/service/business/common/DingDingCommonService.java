package com.fshows.knowledge.service.business.common;

import com.fshows.knowledge.service.domain.param.common.DingTalkMessageWebHookForm;
import com.fshows.knowledge.service.domain.param.common.DingtalkMessageForm;

/**
 * DingDingCommonService
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/19
 */
public interface DingDingCommonService {

    /**
     * 发送忽略环境的钉钉消息
     *
     * @param scene 场景
     * @param message 消息内容
     * @param json JSON数据
     */
    void sendMessageDiscardEnv(String scene, String message, String json);

    /**
     * 发送钉钉回调通知结果
     *
     * @param scene   场景
     * @param message 消息内容
     */
    void sendCallbackNotificationMessage(String scene, String message, String body);

    /**
     * 发送报警信息
     *
     * @param scene     场景
     * @param traceId   traceId
     * @param throwable 内容
     */
    void sendGlobalErrorAlarmMessage(String scene, String traceId, Throwable throwable);

    /**
     * 根据关键词获得SLS的地址
     *
     * @param keyword 关键词
     * @return SLS地址
     */
    String getSlsUrl(String keyword);

    /**
     * 佣金的钉钉告警
     *
     * @param dingtalkMessageForm
     */
    void sendCommissionDingTalkMessage(DingtalkMessageForm dingtalkMessageForm);

    /**
     * 授权服务商群钉钉告警
     *
     * @param dingtalkMessageForm
     */
    void sendServiceProviderDingTalkMessage(DingtalkMessageForm dingtalkMessageForm);


    /**
     * 发送钉钉消息
     * @param form
     * @return {@link String}
     */
    void sendDingMessage(DingTalkMessageWebHookForm form);
}