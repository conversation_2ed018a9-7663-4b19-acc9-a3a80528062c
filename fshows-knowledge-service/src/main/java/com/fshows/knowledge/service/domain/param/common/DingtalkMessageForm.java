/**
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.fshows.knowledge.service.domain.param.common;

import com.huike.nova.common.constant.DingTalkConstant;
import lombok.Data;

/**
 * <AUTHOR>
 * @version DingtalkMessageForm.java, v 0.1 2022-03-15 11:58 上午 mayucong
 */
@Data
public class DingtalkMessageForm {

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 机器人地址
     */
    private String webHookUrl;

    /**
     * 提示信息
     *
     * @param content
     * @return
     */
    public static DingtalkMessageForm noticeFormat(String content, String webHookUrl) {
        DingtalkMessageForm dingtalkMessageForm = new DingtalkMessageForm();
        dingtalkMessageForm.setTitle(DingTalkConstant.NOTICE);
        dingtalkMessageForm.setContent(content);
        dingtalkMessageForm.setWebHookUrl(webHookUrl);
        return dingtalkMessageForm;
    }

    /**
     * 告警信息
     *
     * @param content
     * @return
     */
    public static DingtalkMessageForm alarmFormat(String content, String webHookUrl) {
        DingtalkMessageForm dingtalkMessageForm = new DingtalkMessageForm();
        dingtalkMessageForm.setTitle(DingTalkConstant.ALARM);
        dingtalkMessageForm.setContent(content);
        dingtalkMessageForm.setWebHookUrl(webHookUrl);
        return dingtalkMessageForm;
    }
}
