/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.fshows.knowledge.service.config;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.huike.nova.common.config.SysConfig;
import com.huike.nova.common.exception.CommonException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version RetryerConfig.java, v 0.1 2023-09-01 2:41 PM ruanzy
 */
@Configuration
public class RetryerConfig {

    public RetryerConfig(SysConfig sysConfig) {
        this.sysConfig = sysConfig;
    }

    private final SysConfig sysConfig;

    /**
     * 异常重试器
     *
     * @return
     */
    @Bean(value = "exceptionRetryer")
    public Retryer<Object> exceptionRetryer() {
        // 重试机制
        return RetryerBuilder.newBuilder()
                // retryIf 重试条件
                .retryIfException()
                .retryIfRuntimeException()
                .retryIfExceptionOfType(CommonException.class)
                // 等待策略：每次请求间隔1s
                .withWaitStrategy(WaitStrategies.fixedWait(1, TimeUnit.SECONDS))
                // 停止策略 : 尝试请求3次
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))
                .build();
    }
}