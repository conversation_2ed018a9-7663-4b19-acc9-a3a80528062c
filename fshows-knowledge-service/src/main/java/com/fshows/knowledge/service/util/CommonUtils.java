/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.knowledge.service.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.crypto.SecureUtil;
import com.fshows.fsframework.core.constants.StringPool;
import com.fshows.fsframework.core.utils.FsDateUtil;
import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.fsframework.core.utils.Md5Util;
import com.huike.nova.common.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.util.*;

/**
 * <AUTHOR>
 * @version CommonUtils.java, v 0.1 2022-08-01 09:53 jiangwz
 */
@Slf4j
public class CommonUtils {

    /**
     * 随机字符串范围
     */
    public static final String ALL_CHAR = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    /**
     * 盐长度
     */
    public static final Integer SALT_LENGHT = 18;

    /**
     * 盐长度
     */
    public static final Integer BLOC_ACCOUNT_SALT_LENGTH = 8;

    /**
     * 如果原字符串为空，返回默认defaultStr
     *
     * @param obj
     * @param defaultStr
     * @return
     */
    public static String getDefaultIfBlank(Object obj, String defaultStr) {
        if (StringUtils.isEmpty(defaultStr)) {
            defaultStr = StringPool.EMPTY;
        }
        if (obj == null) {
            return defaultStr;
        }
        String tmp = obj.toString();
        return StringUtils.defaultIfBlank(tmp, defaultStr);
    }


    /**
     * 随机字符串，用于获取MD5salt
     *
     * @param length
     * @return
     */
    public static String getRandomString(int length) {
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(62);
            sb.append(ALL_CHAR.charAt(number));
        }
        return sb.toString();
    }


    /**
     * 随机字符串，用于获取MD5salt
     *
     * @param bytes
     * @return
     */
    public static String betyToStringWithUTF8(byte[] bytes) {
        try {
            String body = new String(bytes, StringPool.UTF_8);
            return body;
        } catch (UnsupportedEncodingException e) {
            LogUtil.error(log, "字符串转化失败！", e);
        }
        return null;
    }

    /**
     * 时间戳转时间
     *
     * @param timestamp
     * @return
     */
    public static Date timestampToDate(Long timestamp) {
        if (timestamp == null || timestamp == 0) {
            return null;
        }
        return new Date(timestamp);
    }

    /**
     * 对象值为空时赋值
     *
     * @param t
     * @param defaultString
     * @param <T>
     * @return
     */
    public static <T> void setDefaultStringValue(T t, String defaultString) {
        try {
            for (Field f : t.getClass().getDeclaredFields()) {
                f.setAccessible(true);
                if (String.class == f.getType()) {
                    if (null == f.get(t)) {
                        // null转空字符串
                        f.set(t, defaultString);
                    } else {
                        // 不为null则去空格
                        f.set(t, StringUtils.trimToEmpty(f.get(t).toString()));
                    }
                }
            }
        } catch (IllegalAccessException e) {
            LogUtil.error(log, "设置对象参数默认值异常, Ex={}", ExceptionUtils.getStackTrace(e));
        }
    }

    public static <T> void setDefaultNullValue(T t, T newT) {
        try {
            Field[] fs = t.getClass().getDeclaredFields();
            for (Field f : fs) {
                f.setAccessible(true);
                Object v1 = f.get(newT);
                Object v2 = f.get(t);
                if (null == v1 && v2 != null) {
                    f.set(newT, v2);
                }
            }
        } catch (Exception e) {
            LogUtil.error(log, "设置对象参数默认值异常, Ex={}", ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * 获取日0点时间戳方法（秒单位）
     *
     * @param days 0 今天0点 -1 昨天0点  1明天0点 2后天0点
     * @return
     */
    public static int getZeroTime(int days) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, days);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date zero = calendar.getTime();
        return (int) (zero.getTime() / 1000);
    }

    /**
     * 获取今日某时、某分、某秒时间
     *
     * @param hours
     * @param minute
     * @param second
     * @return
     */
    public static Date getTodayTime(int hours, int minute, int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, hours);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        return calendar.getTime();
    }

    /**
     * @param nowTime   当前时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     * <AUTHOR>   判断当前时间在时间区间内
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        return date.after(begin) && date.before(end);
    }

    public static void main(String[] args) {
//        System.out.println( getZeroTime(2));

//        System.out.println(isEffectiveDate(new Date(), getTodayTime(12, 0, 0), getTodayTime(13, 0, 0)));
//        System.out.println(isEffectiveDate(new Date(), getTodayTime(11, 0, 0), getTodayTime(13, 0, 0)));

//        System.out.println(dealStoreIds(Arrays.asList(1111, 2222), 11));

        System.out.println(createOnlyId("G"));
    }

    /**
     * 手机号码前三后四脱敏
     *
     * @param mobile
     * @return
     */
    public static String mobileEncrypt(String mobile) {
        if (StringUtils.isEmpty(mobile) || (mobile.length() != 11)) {
            return mobile;
        }
        return mobile.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }


    /**
     * 处理门店列表
     *
     * @param storeIds
     * @return
     */
    public static String dealStoreIds(List<Integer> storeIds) {
        return dealStoreIds(storeIds, 300);
    }

    /**
     * 处理门店列表
     *
     * @param storeIds
     * @return
     */
    public static String dealStoreIds(List<Integer> storeIds, int length) {
        //1.空处理
        if (storeIds == null || storeIds.isEmpty()) {
            return "";
        }
        //2.集合拼接成字符串
        String storeIdStr = StringUtils.join(storeIds.toArray(), StringPool.COMMA);
        //3.数据库长度限制, 超过长度，则按照最大去截取
        if (storeIdStr.length() > length) {
            try {
                String tempStoreIdStr = storeIdStr.substring(0, length);
                int lastCommaIndex = tempStoreIdStr.lastIndexOf(StringPool.COMMA);
                if (lastCommaIndex != -1) {
                    return tempStoreIdStr.substring(0, lastCommaIndex);
                }
            } catch (Exception e) {
                LogUtil.error(log, "员工处理门店失败！", e);
            }
            return "";
        } else {
            return storeIdStr;
        }
    }

    public static boolean storeIdsLengthOverLimit(List<Integer> storeIds, int length) {
        if (storeIds == null || storeIds.isEmpty()) {
            return false;
        }
        String storeIdStr = StringUtils.join(storeIds.toArray(), StringPool.COMMA);
        return storeIdStr.length() > length;
    }

    /**
     * 生成ApplyId
     *
     * @param uid        商户id
     * @param merchantNo 商编
     * @return String
     */
    public static String generateApplyId(Integer uid, String merchantNo) {
        final StringBuilder stringBuilder = new StringBuilder(uid.toString());
        if (StringUtils.isNotBlank(merchantNo)) {
            stringBuilder.append(merchantNo);
        }
        stringBuilder.append(FsDateUtil.getCurrentSecond().toString());
        return SecureUtil.md5(stringBuilder.toString());
    }

    /**
     * 生成日志id
     *
     * @return String
     */
    public static String generateLogId() {
        return Md5Util.sign(UUID.randomUUID().toString());
    }


    public static String createOnlyId(String business) {
        String randomNumeric = RandomStringUtils.randomNumeric(6);
        String timeStr = FsDateUtil.getReqDateyyyyMMddHHmmss(new Date());
        return String.format("%s%s%s", business, timeStr, randomNumeric);
    }



    /**
     * 万达通过完整路径获取层级名称(只适用于2,3级)
     *
     * @param fullPath 完整路径
     * @param hierarchy 层级
     * @return
     */
    public static String getHierarchyName(String fullPath, Integer hierarchy) {
        String hierarchyName = "";
        if (fullPath == null || !fullPath.contains(com.huike.nova.common.constant.StringPool.SLASH)) {
            return hierarchyName;
        }
        // 去掉前后斜杠
//        if (fullPath.endsWith(com.huike.nova.common.constant.StringPool.SLASH)) {
//            fullPath = fullPath.replaceAll("^/|/$", com.huike.nova.common.constant.StringPool.EMPTY);
//        }

        // 去掉末尾的斜杠
        if (fullPath.endsWith(com.huike.nova.common.constant.StringPool.SLASH)) {
            fullPath = fullPath.substring(0, fullPath.length() - 1);
        }
        // 去掉最前端的斜杠
        if (fullPath.startsWith(com.huike.nova.common.constant.StringPool.SLASH)) {
            fullPath = fullPath.substring(1);
        }
        String[] hierarchyNames = fullPath.split("/");
        if (hierarchyNames.length < hierarchy) {
            return hierarchyName;
        }
        hierarchyName = hierarchyNames[hierarchy-1];
        if (hierarchyName.contains(CommonConstant.SQUARE_SIGN)) {
            return StringUtils.EMPTY;
        }
        return hierarchyName;
    }

    /**
     * 万达通过完整路径获取层级名称集合(只适用于2,3级)
     *
     * @param fullPaths 完整路径
     * @param hierarchy 层级
     * @return
     */
    public static List<String> getHierarchyNames(List<String> fullPaths, Integer hierarchy) {
        if (CollectionUtil.isEmpty(fullPaths)){
            return new ArrayList<>();
        }
        List<String> result = new ArrayList<>();
        for (String item : fullPaths) {
            String hierarchyName = "";
            if (item == null || !item.contains(com.huike.nova.common.constant.StringPool.SLASH)) {
                continue;
            }
            // 去掉末尾的斜杠
            if (item.endsWith(com.huike.nova.common.constant.StringPool.SLASH)) {
                item = item.substring(0, item.length() - 1);
            }
            // 去掉最前端的斜杠
            if (item.startsWith(com.huike.nova.common.constant.StringPool.SLASH)) {
                item = item.substring(1);
            }
            // 找到最后一个斜杠的位置
            int lastSlashIndex = item.lastIndexOf(com.huike.nova.common.constant.StringPool.SLASH);
            if (lastSlashIndex != -1) {
                // 返回去掉最后一个层级后的字符串
                item =  item.substring(0, lastSlashIndex + 1);
            }
            String[] hierarchyNames = item.split("/");
            if (hierarchyNames.length < hierarchy) {
                result.add(hierarchyName);
                continue;
            }
            hierarchyName = hierarchyNames[hierarchy-1];
            if (hierarchyName.contains(CommonConstant.SQUARE_SIGN)) {
                result.add(StringUtils.EMPTY);
                continue;
            }
            result.add(hierarchyName);
        }
        return result;
    }



}