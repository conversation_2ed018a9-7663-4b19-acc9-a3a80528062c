package com.fshows.knowledge.service.config;

import cn.hutool.core.date.SystemClock;
import com.huike.nova.common.util.LogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.AsyncTaskExecutor;
import org.springframework.core.task.TaskDecorator;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.RejectedExecutionHandler;

/**
 * 线程池配置
 *
 * <AUTHOR> (<EMAIL>)
 * @version ThreadPoolConfig.java, v1.0 11/13/2023 15:05 John Exp$
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {

    /**
     * 告警线程池
     *
     * @return 异步任务执行对象
     */
    public AsyncTaskExecutor alarmTaskExecutor() {
        String threadPoolName = "alarmTaskExecutor";
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 获取当前CPU的核数
        executor.setThreadNamePrefix("alarmTaskExecutor-");
        // 设置拒绝策略,当前策略：如等待队列已满则记录日志，并丢弃任务
        executor.setRejectedExecutionHandler(getDefaultRejectedExecutionHandler(threadPoolName));
        // 通过包装原有任务来捕获可能发生的异步任务抛出的异常
        executor.setTaskDecorator(getDefaultTaskDecorator(threadPoolName));
        return executor;
    }

    /**
     * 获取默认的任务丢弃策略
     * 默认策略：记录日志，直接丢弃任务
     *
     * @param threadPoolName 当前线程池名称，用于日志记录
     * @return 拒绝执行的回调
     */
    private RejectedExecutionHandler getDefaultRejectedExecutionHandler(String threadPoolName) {
        return (r, executor) -> LogUtil.error(log, "【异步线程池】等待队列超过最大长度限制，拒绝任务！线程池名称={}，task={}", threadPoolName, r.toString());
    }

    /**
     * 获取默认的任务包装策略
     * 默认策略：catch住任务可能抛出的异常，避免异常信息的丢失
     *
     * @param threadPoolName 当前线程池名称，用于日志记录
     * @return 兜底对象
     */
    private TaskDecorator getDefaultTaskDecorator(String threadPoolName) {
        return runnable -> () -> {
            long currentTime = SystemClock.now();
            try {
                runnable.run();
            } catch (Exception e) {
                LogUtil.error(log, "【异步线程池】异步任务执行过程中发生异常！耗时={}ms, 线程池名称={}", e, (SystemClock.now() - currentTime), threadPoolName);
            }
        };
    }
}
