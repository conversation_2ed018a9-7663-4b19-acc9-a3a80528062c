package com.fshows.knowledge.service.business.api.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.fshows.knowledge.dao.entity.BotArticleDO;
import com.fshows.knowledge.dao.entity.BotKnowledgeDO;
import com.fshows.knowledge.dao.entity.BotSourceDataDO;
import com.fshows.knowledge.dao.repository.BotArticleDAO;
import com.fshows.knowledge.dao.repository.BotKnowledgeDAO;
import com.fshows.knowledge.dao.repository.BotSourceDataDAO;
import com.fshows.knowledge.service.business.api.VectorService;
import com.fshows.knowledge.service.business.api.YqOpenService;
import com.fshows.knowledge.service.domain.model.H3ParagraphModel;
import com.huike.nova.common.config.SysConfig;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.util.LogUtil;
import dev.langchain4j.community.model.dashscope.QwenEmbeddingModel;
import dev.langchain4j.data.document.Metadata;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 数据向量 service
 */
@Slf4j
@Service
@AllArgsConstructor
public class VectorServiceImpl implements VectorService {

	private final BotArticleDAO botArticleDAO;
	private final BotKnowledgeDAO botKnowledgeDAO;
	private static final String TEST_CONTENT = "<h3 id=\"B7FZv\">没有小票/交易凭证凭证怎么办？</h3>\n" +
			"只要是真实，且能展示商家售卖的商品或服务的凭证都可以，其中发票、购物小票、收银系统截图是接受度最好的，或者像价目表、进出库单据只能做辅助资料。但最终审核结果还是以上游机构为准。 \n" +
			"\n" +
			"<h3 id=\"ji62d\">调单商户时效期到了会不会关闭商户账号 ，关闭交易权限关闭，资金会不会冻结等商户不申诉可以吗？</h3>\n" +
			"如想继续使用付呗，建议尽量申诉，相同的证件资料重新开户仍有概率被通道方风控。\n" +
			"\n" +
			"<h3 id=\"dPMKW\">没有法人没有营业执照怎么办？营业执照注销了怎么办？</h3>\n" +
			"①没有营业执照，可以用门店租赁合同代替；  \n" +
			"②营业执照注销了，烦请提供营业执照注销证明。\n" +
			"\n" +
			"<h3 id=\"DMV7d\">银行卡挂失了/银行卡已补办/结算卡丢失/提供不了证明怎么办？</h3>\n" +
			"商户的结算卡已丢失/注销，邮件中文字备注说明银行卡丢失/注销情况，提供挂失证明/注销，然后提供补办之后的结算卡信息（手持结算卡/结算卡正反面照片）\n" +
			"\n" +
			"<h3 id=\"DDhma\">商户申诉邮件多久回复，什么时候审核通过？</h3>\n" +
			"商户提供申诉资料，邮件是3-7个工作日回复，审核结果以通道方审核为准，审核有结果之后邮件/工单状态更新。\n" +
			"\n" +
			"<h3 id=\"sEx1x\">经营门店拆迁了，没有办法提供资料，没有营业执照了？</h3>\n" +
			"提供拆迁的部分照片以证明商户说明的实际情况，可提供签约的租赁合同（补签合同也可以），营业执照如果注销了，提供营业执照注销证明。\n" +
			"\n" +
			"<h3 id=\"MfsB5\">门店已关闭了或者没有转让、生意不好直接关闭，房子还给房东了，怎么办？</h3>\n" +
			" ①需要商户提供门店转让协议（可补签），并提供转让之后门店门头照（门牌号），原门店地图定位截图；  \n" +
			" ②需要提供商户到期房屋租赁合同（可补签），并提供转让后门店门头照（门牌号），原门店地图定位截图。\n" +
			"\n" +
			"<h3 id=\"HPXse\">门店就是没有门头照，没有门牌号，没有办法提供怎么办？</h3>\n" +
			"没有门头门牌邮件中文字说明情况，并提供没有门头照照片，能够体现出来确实是没有门头的情况即可 。\n" +
			"\n" +
			"<h3 id=\"oX8yd\">没有价目表怎么办？</h3>\n" +
			"①商户可到打印店里，按照店里的价格打印一份对应商品售卖价格表；  \n" +
			"②商户可自己在电脑上，用文档的格式，制作一份价目表，截图提供也可以；  \n" +
			"③可以拍货架上的售卖商品以及价格码牌的照片提供3张即可。\n" +
			"\n" +
			"<h3 id=\"FYD9M\">小微商户没有营业执照/门店，怎么提供资料？</h3>\n" +
			"小微商户可以用文字说明小微进件没有营业执照情况，需提供对应摊位不同角度的环境照，收款方式照片，售卖价目表，并录制整个摊位不同方向视频资料，证明整个真实交易场景现象。\n" +
			"\n" +
			"<h3 id=\"oZrq7\">关于涉嫌投诉的商户需“提供投诉订单已处理凭证”，怎么操作处理？</h3>\n" +
			"![](https://cdn.nlark.com/yuque/0/2021/png/1276905/1626863603922-a4732cd6-3852-4da1-b2e6-c4c7f97f2c0c.png?x-oss-process=image%2Fformat%2Cwebp)\n" +
			"\n" +
			"需消费者在原投诉订单下面评论：商户/商家已处理，已和商户协商处理完成 或 需要撤诉 / 手误点错了 / 误投诉等字样提交后，在截图提供（可以算作已处理的凭证）。  \n" +
			"如上图显示消费者提供原投诉订单已留言处理完成凭证。（咨询腾讯官方协助联系消费者） 或者原路退款。\n" +
			"\n" +
			"<h3 id=\"zCFtB\">什么是风控？</h3>\n" +
			"由监管机构发起的风险控制行为，减少因风险事件发生所带来的损失。\n" +
			"\n" +
			"<h3 id=\"ZZmTD\">风控是由谁发起的？</h3>\n" +
			"我司属于技术服务性公司，风控主要是监管机构、微信、支付宝、银联发起的，我司配合执行。\n" +
			"\n" +
			"<h3 id=\"Zcyxo\">监管机构是谁?</h3>\n" +
			"您好，资金清算是有牌照的支付机构-XX（查询对应的通道）负责，如要正常结算出款，请按要求提供对应的申诉资料。\n" +
			"\n" +
			"<h3 id=\"NXNHt\">商户被风控后我的资金还能拿回来吗？</h3>\n" +
			"您按照要求提供相关资料，相关资料上传监管机构审核通过后就可以结算，具体时间以监管机构为准。\n" +
			"\n" +
			"<h3 id=\"tMb7W\">冻结我资金 我要报警告你们？</h3>\n" +
			"我司作为技术服务商，提供我们的产品技术服务于广大代理商、商户和消费者，但我司不涉及商户资金结算业务。资金的延迟结算是有支付牌照的监管机构的工作，监管机构是有权也有责任对风险商户采取措施包括但不限于关闭交易通道，延迟结算资金。如您需通过其他渠道反馈问题，我们会积极配合工作，如报警的话麻烦请先提供一下立案文件和警官证件、警官的联系方式和姓名，我们会在通过杭州网警审核后积极配合处理请您放心。\n" +
			"\n" +
			"<h3 id=\"zZB84\">付呗有没有风控部门？</h3>\n" +
			"商户风控是由监管机构发起，付呗的风控部门主要是跟上级部门做对接工作。\n" +
			"\n" +
			"<h3 id=\"WEzQ7\">风控的规则是什么？</h3>\n" +
			"非常抱歉因商户风控是监管机构发起，所以我司并不清楚监管机构具体的风控规则。正常商户在符合国家要求和监管机构规定的情况下一般不会被发起风控，如碰到误判可提交资料申请。\n" +
			"\n" +
			"<h3 id=\"xFRiX\">我联系了支付宝官方客服都说我没有被风控，为什么你们说是支付宝风控</h3>\n" +
			"①支付宝官方客服只处理支付宝直连商户，涉及个人消费者支付宝账号。我司为银行间联，是以商户为载体在支付宝端进件，非个人消费者，故支付宝官方客服无法核实风险商户。  \n" +
			"②还有一种情况是：商户在支付宝“商家安全助手”小程序中，自行申诉时，显示状态正常。但我司后台的支付宝工单仍是限制中/驳回状态。这种支付宝那边的答复是：风险较高，限制了商户端的申诉\n" +
			"\n" +
			"<h3 id=\"g5HxG\">警方需要联系我们调取客户资料怎么对接？</h3>\n" +
			"您好，由于我司属地网警大队要求，相关调单流程如下：\n" +
			"\n" +
			"凡向我司调证的需要向杭州公安外协部门沟通开具《工作联系单》/《介绍信》，具体以杭州公安外协部门的要求为准；《工作联系单》/《介绍信》开具请联系：杭州网警外协，电话0571-89591269（工作日工作时间可正常联系）；\n" +
			"\n" +
			"相关资料开具完成后，请将各类资料发送至风控申诉邮箱号：<EMAIL>\n" +
			"\n" +
			"<h3 id=\"v2NZC\">微信交易停滞工单怎么自行申诉</h3>\n" +
			"商户通过“微信支付商家助手”小程序->“风险处理”->点击“交易停滞”违约单据，点击“申请解除处罚”进行\n" +
			"\n" +
			"<h3 id=\"qZIXs\">商户要注销账号， 过了180天风险期了 申诉资料没有办法提供了 不开店了 没有门店照片，怎么办？</h3>\n" +
			"您好，可以注销，提供下注销声明，并且注销后不支持重新进件\n" +
			"\n" +
			"<h3 id=\"DGWBb\">支付宝自行申诉的流程</h3>\n" +
			"支付宝APP里面搜索商家安全服务，商户点击“扫码检测”唤起⼿机扫码功能，然后扫付呗收款二维码，扫码后账户状态显示为“正常”的截图。\n" +
			"\n" +
			"<h3 id=\"RKVe7\">维持原判客户还要怎么申诉？</h3>\n" +
			"微信维持原判：除非有更全面的资料能够申请，否则审核结果不变，不支持再次申诉。  \n" +
			"支付宝维持原判：官方审核结果为准 。  \n" +
			"付呗维持原因：不支持再次申请。 \n" +
			"\n" +
			"<h3 id=\"gRRTY\">客户在微信官方解除限制了 还需要付呗app申诉吗？ </h3>\n" +
			"违规状态的需要用申诉成功的截图提交至机构申诉。仅仅是拦截可自行意愿。\n" +
			"\n" +
			"<h3 id=\"K003u\">走清退后是我们这边都不能进件 还是只是机构 后面可以进其他通道？</h3>\n" +
			"机构不支持再次进件，黑名单不解除。\n" +
			"\n" +
			"<h3 id=\"Fa2lP\">乐刷的沉默商户是啥意思</h3>\n" +
			"沉默商户是长期无交易（一般是一年）被乐刷暂停交易的。支持重新开户\n" +
			"\n" +
			"<h3 id=\"BQ3WN\">商户申诉的时候没有提供监控视频，被驳回了，这种情况商户提供不了监控视频，要怎么处理</h3>\n" +
			"监控视频一般是云闪付高风险工单的必要资料。实在提供不了的可以补充情况说明，比如未安装监控、监控损坏，但需要明确告知，缺少监控视频会影响工单审核时效和通过率\n" +
			"\n" +
			"<h3 id=\"EkzKg\">商户工单申诉法人出不了面拍照，可以用什么代替</h3>\n" +
			"手持证件资料的是结算人，如果是法人结算的，除某些特殊情况外，都需要手持拍照。比如：政府人员、在境外，这种可以写情况说明\n" +
			"\n" +
			"<h3 id=\"i6goi\">商户微信支付宝都暂停使用了，能不能重新开个户正常使用呢</h3>\n" +
			"微信违规的商户，及时重新开户也是会被微信关联到，支付宝的也有入网限制，所以一般不建议这样操作\n" +
			"\n" +
			"<h3 id=\"Qw53p\">商户如果是已经给消费者退款了，消费者不撤诉，还可以怎样申诉 </h3>\n" +
			"投诉撤诉凭证仅支持原路退款或消费者在微信后台留言撤诉凭证；  若在此前提下消费者再次投诉，可以提供退款凭证以及双方沟通协商处理的记录，会根据实际情况判定； \n" +
			"\n" +
			"<h3 id=\"kpCrX\">微信交易拦截无申诉入口的处理，若有商户出现拦截 不支持申诉情况,怎么办？</h3>\n" +
			"您好，改拦截是微信官方的报错，暂无法工单，但商户可根据微信官方二维码进行申诉处理。\n" +
			"\n" +
			"![](https://cdn.nlark.com/yuque/0/2025/png/1276905/1745823188352-beb1f2cf-0acb-4898-8c69-92b12936039e.png?x-oss-process=image%2Fformat%2Cwebp)\n" +
			"\n" +
			"<h3 id=\"kIx3I\">微信小程序投诉客户其他资料提供不了 怎么办</h3>\n" +
			"只认全额退款和消费者的撤诉留言，其他的资料都不认\n" +
			"\n" +
			"<h3 id=\"gIhiH\">微信小程序投诉已经处理了 怎么还不能结算</h3>\n" +
			"小程序投诉导致关闭交易与结算权限的，工单申诉通过后7个工作日内无新增小程序投诉，可恢复结算权限；如还有其他工单也请及时处理   这是我的文档内容  我现在想通过h3标签分段  分为 List<String> 给我对应的 java 方案和正则";
	private final TransactionTemplate transactionTemplate;
	private SysConfig sysConfig;
	private YqOpenService yqOpenService;
	private final BotSourceDataDAO botSourceDataDAO;

	/**
	 * 根据 h3 标签分割文章
	 *
	 * @param html
	 * @return
	 */
	public static List<String> splitByH3Tags(String html) {
		List<String> segments = new ArrayList<>();
		// 正则表达式匹配<h3>标签及其内容，直到下一个<h3>标签前
		Pattern pattern = Pattern.compile(
				"(<h3 id=\"[^\"]+\">.*?</h3>)(.*?)(?=<h3 id=\"[^\"]+\">|$)",
				Pattern.DOTALL
		);
		Matcher matcher = pattern.matcher(html);

		while (matcher.find()) {
			// 组合<h3>标签和其内容
			String segment = matcher.group(1) + matcher.group(2);
			segments.add(segment.trim());
		}

		return segments;
	}

	/**
	 * 解析HTML文本，提取h3标签及其内容
	 *
	 * @param htmlText 包含h3标签的HTML文本
	 * @return H3Paragraph对象列表，包含每个段落的id、h3标签和内容
	 */
	public static List<H3ParagraphModel> parseH3Paragraphs(String htmlText) {
		List<H3ParagraphModel> result = new ArrayList<>();

		if (htmlText == null || htmlText.trim().isEmpty()) {
			return result;
		}

		// 正则表达式匹配h3标签及其后面的内容，直到下一个h3标签前
		String regex = "(<h3 id=\"([^\"]+)\">([^<]+)</h3>)([^<]*(?:<(?!/h3>)[^<]*)*)(?=<h3 id=\"[^\"]+\">|$)";
		Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
		Matcher matcher = pattern.matcher(htmlText);

		// 遍历所有匹配项
		while (matcher.find()) {
			String h3Tag = matcher.group(1); // 完整的h3标签
			String id = matcher.group(2); // h3标签的id
			String h3Content = matcher.group(3); // h3标签内的文本
			String contentAfterH3 = matcher.group(4).trim(); // h3标签后的内容

			// 构建完整的段落
			String entireParagraph = h3Tag + contentAfterH3;

			// 构建不包含h3标签的内容
			String contentWithoutHTag = h3Content + "\n" + contentAfterH3;

			// 创建H3Paragraph对象并添加到结果列表
			result.add(new H3ParagraphModel(entireParagraph, id, contentWithoutHTag, h3Content));
		}

		return result;
	}

	/**
	 * 文档下载 and 获取 测试
	 *
	 * @param args
	 */
	// public static void main(String[] args) {
	// 	System.out.println("开始");
	// 	String url = "https://fshows.yuque.com/rqxish/rgu3dn/ka7tmmtvn18sab8t/markdown?attachment=true&latexcode=false&anchor=true&linebreak=false";
	// 	// 加载文档
	// 	// Document document = FileSystemDocumentLoader.loadDocument("");
	// 	Document document = UrlDocumentLoader.load(url, new TextDocumentParser());
	// 	System.out.println("结束");
	// }


	/**
	 * 测试 h3 标签分割
	 *
	 * @param args
	 */
	// public static void main(String[] args) {
	//
	// 	List<String> segments = splitByH3Tags(TEST_CONTENT);
	//
	// 	// 输出结果
	// 	for (int i = 0; i < segments.size(); i++) {
	// 		System.out.println("Segment " + (i + 1) + ":");
	// 		System.out.println(segments.get(i));
	// 		System.out.println("----------------------");
	// 	}
	// }

	/**
	 * 测试 h3 标签分割（完整）
	 */
	public static void main(String[] args) {
		// 解析HTML文本
		List<H3ParagraphModel> paragraphs = parseH3Paragraphs(TEST_CONTENT);

		// 输出结果
		for (H3ParagraphModel paragraph : paragraphs) {
			System.out.println("完整段落:");
			System.out.println(paragraph.getEntireParagraph());
			System.out.println("ID: " + paragraph.getId());
			System.out.println("去除了h3标签的内容:");
			System.out.println(paragraph.getContentWithoutHTag());
			System.out.println("----------------------");
		}
	}

	/**
	 * 搜索文章数据
	 *
	 * @return 文章列表
	 */
	private List<BotArticleDO> searchArticleData() {
		DateTime updateTime = DateUtil.offsetDay(new Date(), sysConfig.getDingtalkArticleDataSearchDay());
		return botArticleDAO.findByUpdateTimeAndProcessStatus(updateTime, CommonConstant.ONE);
	}

	/**
	 * 定时向量数据脚本
	 * <p>
	 * 该方法用于定时处理语雀文章数据，将文章内容转换为向量数据并存储到数据库中。
	 * 主要流程包括：查询待处理文章、下载文章内容、解析H3段落、生成向量嵌入、更新数据库。
	 * </p>
	 *
	 * @param content 预留参数，当前未使用
	 * @throws RuntimeException 当文章处理过程中发生异常时抛出
	 * @see BotArticleDO 文章数据对象
	 * @see BotKnowledgeDO 知识库数据对象
	 * @see BotSourceDataDO 源数据对象
	 * @see H3ParagraphModel H3段落模型
	 */
	@Override
	public void timingVectorData(String content) {
		LogUtil.info(log, "VectorServiceImpl.timingVectorData >> 定时向量数据脚本开始 >> param = {}", content);

		try {
			// 查询待处理的文章数据
			List<BotArticleDO> articleDOList = searchArticleData();
			LogUtil.info(log, "VectorServiceImpl.timingVectorData >> 查询文章数据完成 >> articleDOList.size = {}", articleDOList.size());

			// 批量处理文章数据
			processArticleList(articleDOList);

			LogUtil.info(log, "VectorServiceImpl.timingVectorData >> 处理全部文章完成 >> articleDOList.size = {}", articleDOList.size());
		} catch (Exception e) {
			LogUtil.error(log, "VectorServiceImpl.timingVectorData >> 定时向量数据脚本执行异常", e);
			throw new RuntimeException("定时向量数据脚本执行失败", e);
		}
	}

	/**
	 * 批量处理文章列表
	 * <p>
	 * 遍历文章列表，逐个处理每篇文章的向量化操作
	 * </p>
	 *
	 * @param articleDOList 待处理的文章列表
	 * @throws RuntimeException 当文章处理过程中发生异常时抛出
	 */
	private void processArticleList(List<BotArticleDO> articleDOList) {
		for (BotArticleDO articleDO : articleDOList) {
			try {
				LogUtil.info(log, "VectorServiceImpl.processArticleList >> 开始处理文章 >> articleId = {}", articleDO.getArticleId());

				// 处理单篇文章
				processSingleArticle(articleDO);

				LogUtil.info(log, "VectorServiceImpl.processArticleList >> 处理文章完成 >> articleId = {}", articleDO.getArticleId());
			} catch (Exception e) {
				LogUtil.error(log, "VectorServiceImpl.processArticleList >> 处理文章异常 >> articleId = {}", articleDO.getArticleId(), e);
				// 单篇文章处理失败不影响其他文章处理，继续处理下一篇
			}
		}
	}

	/**
	 * 处理单篇文章的向量化操作
	 * <p>
	 * 包括：验证知识库、下载文章内容、解析段落、生成向量、更新数据库
	 * </p>
	 *
	 * @param articleDO 待处理的文章对象
	 * @throws RuntimeException 当文章处理过程中发生异常时抛出
	 */
	private void processSingleArticle(BotArticleDO articleDO) {
		// 验证知识库是否存在
		BotKnowledgeDO knowledgeDO = validateKnowledgeBase(articleDO);
		if (knowledgeDO == null) {
			LogUtil.warn(log, "VectorServiceImpl.processSingleArticle >> 知识库不存在，跳过处理 >> articleId = {}", articleDO.getArticleId());
			return;
		}

		// 下载文章内容
		String articleContent = downloadArticleContent(articleDO, knowledgeDO);
		if (StringUtils.isBlank(articleContent)) {
			LogUtil.warn(log, "VectorServiceImpl.processSingleArticle >> 语雀文章内容获取失败，跳过处理 >> articleId = {}", articleDO.getArticleId());
			return;
		}

		// 创建向量模型和存储（后续可作为单例）
		QwenEmbeddingModel embeddingModel = createEmbeddingModel();
		InMemoryEmbeddingStore<TextSegment> embeddingStore = createEmbeddingStore();

		// 处理文章段落并生成向量数据
		processArticleParagraphs(articleDO, knowledgeDO, articleContent, embeddingModel, embeddingStore);
	}

	/**
	 * 验证知识库是否存在
	 * <p>
	 * 根据文章的bookId查询对应的知识库信息
	 * </p>
	 *
	 * @param articleDO 文章对象
	 * @return 知识库对象，如果不存在则返回null
	 */
	private BotKnowledgeDO validateKnowledgeBase(BotArticleDO articleDO) {
		BotKnowledgeDO knowledgeDO = botKnowledgeDAO.getByBookId(articleDO.getBookId());
		if (knowledgeDO == null) {
			LogUtil.warn(log, "VectorServiceImpl.validateKnowledgeBase >> 知识库不存在 >> bookId = {}", articleDO.getBookId());
		}
		return knowledgeDO;
	}

	/**
	 * 下载文章内容
	 * <p>
	 * 通过语雀开放服务下载文章的完整内容
	 * </p>
	 *
	 * @param articleDO   文章对象
	 * @param knowledgeDO 知识库对象
	 * @return 文章内容，下载失败返回空字符串
	 */
	private String downloadArticleContent(BotArticleDO articleDO, BotKnowledgeDO knowledgeDO) {
		// 获取文章下载链接
		String downloadedUrl = yqOpenService.downloadYuQueArticle(
				articleDO.getArticleId(),
				knowledgeDO.getCookie(),
				knowledgeDO.getXCsrfToken()
		);

		if (StringUtils.isBlank(downloadedUrl)) {
			LogUtil.warn(log, "VectorServiceImpl.downloadArticleContent >> 语雀文章下载链接获取失败 >> articleId = {}", articleDO.getArticleId());
			return "";
		}

		// 获取文章内容（通过url中anchor控制是否返回H标签内容）
		String articleContent = yqOpenService.getYuQueArticleContent(
				downloadedUrl,
				knowledgeDO.getCookie(),
				knowledgeDO.getXCsrfToken()
		);

		if (StringUtils.isBlank(articleContent)) {
			LogUtil.warn(log, "VectorServiceImpl.downloadArticleContent >> 语雀文章内容获取失败 >> articleId = {}", articleDO.getArticleId());
		}

		return articleContent;
	}

	/**
	 * 创建向量嵌入模型
	 * <p>
	 * 使用阿里云通义千问的文本嵌入模型，配置API密钥和模型参数
	 * </p>
	 *
	 * @return 配置好的向量嵌入模型
	 * @throws RuntimeException 当API密钥未配置时抛出
	 */
	private QwenEmbeddingModel createEmbeddingModel() {
		String apiKey = System.getenv("ALI_AI_KEY");
		if (StringUtils.isBlank(apiKey)) {
			LogUtil.error(log, "VectorServiceImpl.createEmbeddingModel >> 阿里云AI API密钥未配置");
			throw new RuntimeException("阿里云AI API密钥未配置，请检查环境变量ALI_AI_KEY");
		}

		return QwenEmbeddingModel.builder()
				.apiKey(apiKey)
				// 使用通义千问文本嵌入模型v4
				.modelName("text-embedding-v4")
				// 向量维度设置为1024
				.dimension(1024)
				.build();
	}

	/**
	 * 创建内存向量存储
	 * <p>
	 * 创建用于临时存储文本段向量的内存存储实例
	 * </p>
	 *
	 * @return 内存向量存储实例
	 */
	private InMemoryEmbeddingStore<TextSegment> createEmbeddingStore() {
		return new InMemoryEmbeddingStore<>();
	}

	/**
	 * 处理文章段落并生成向量数据
	 * <p>
	 * 解析文章内容为H3段落，为每个段落生成向量嵌入，并更新数据库
	 * </p>
	 *
	 * @param articleDO      文章对象
	 * @param knowledgeDO    知识库对象
	 * @param articleContent 文章内容
	 * @param embeddingModel 向量嵌入模型
	 * @param embeddingStore 向量存储
	 */
	private void processArticleParagraphs(BotArticleDO articleDO, BotKnowledgeDO knowledgeDO,
			String articleContent, QwenEmbeddingModel embeddingModel,
			InMemoryEmbeddingStore<TextSegment> embeddingStore) {

		// 查询文章下所有现有元数据
		List<BotSourceDataDO> sourceDataDOList = botSourceDataDAO.findListBySourceDataId(articleDO.getArticleId());

		// 构建数据处理上下文
		ArticleProcessContext context = buildProcessContext(sourceDataDOList);

		// 根据H3标签分割文章内容
		List<H3ParagraphModel> articleParagraphsList = parseH3Paragraphs(articleContent);
		LogUtil.info(log, "VectorServiceImpl.processArticleParagraphs >> 解析文章段落完成 >> articleId = {}, paragraphCount = {}",
				articleDO.getArticleId(), articleParagraphsList.size());

		// 处理每个段落
		for (H3ParagraphModel paragraphModel : articleParagraphsList) {
			processSingleParagraph(articleDO, knowledgeDO, paragraphModel, embeddingModel, embeddingStore, context);
		}

		// 批量更新数据库
		updateDatabaseInTransaction(context);
	}

	/**
	 * 构建文章处理上下文
	 * <p>
	 * 根据现有的源数据构建处理上下文，包含锚点列表、数据映射和操作列表
	 * </p>
	 *
	 * @param sourceDataDOList 现有的源数据列表
	 * @return 文章处理上下文
	 */
	private ArticleProcessContext buildProcessContext(List<BotSourceDataDO> sourceDataDOList) {
		ArticleProcessContext context = new ArticleProcessContext();

		// 构建锚点列表（可变列表，用于删除操作）
		context.anchorList = new ArrayList<>(sourceDataDOList.stream()
				.map(BotSourceDataDO::getAnchor)
				.toList());

		// 构建元数据映射（锚点 -> 数据对象）
		context.sourceDataDOMap = sourceDataDOList.stream()
				.collect(Collectors.toMap(BotSourceDataDO::getAnchor, item -> item));

		// 初始化操作列表
		context.updateSourceDataDOList = new ArrayList<>();
		context.insertSourceDataDOList = new ArrayList<>();

		return context;
	}

	/**
	 * 处理单个段落
	 * <p>
	 * 为段落生成向量嵌入，并根据是否已存在决定更新或新增数据
	 * </p>
	 *
	 * @param articleDO      文章对象
	 * @param knowledgeDO    知识库对象
	 * @param paragraphModel 段落模型
	 * @param embeddingModel 向量嵌入模型
	 * @param embeddingStore 向量存储
	 * @param context        处理上下文
	 */
	private void processSingleParagraph(BotArticleDO articleDO, BotKnowledgeDO knowledgeDO,
			H3ParagraphModel paragraphModel, QwenEmbeddingModel embeddingModel,
			InMemoryEmbeddingStore<TextSegment> embeddingStore, ArticleProcessContext context) {

		String anchor = paragraphModel.getId();

		// 创建向量嵌入元数据
		Metadata metadata = createEmbeddingMetadata(articleDO, paragraphModel);

		// 生成文本段和向量嵌入
		TextSegment segment = TextSegment.from(paragraphModel.getEntireParagraph(), metadata);
		Embedding embedding = embeddingModel.embed(segment).content();
		String fragmentId = embeddingStore.add(embedding);

		// 处理数据库操作（更新或新增）
		processSourceDataOperation(articleDO, knowledgeDO, paragraphModel, fragmentId, anchor, context);
	}

	/**
	 * 创建向量嵌入元数据
	 * <p>
	 * 为文本段创建包含来源、URL和锚点信息的元数据
	 * </p>
	 *
	 * @param articleDO      文章对象
	 * @param paragraphModel 段落模型
	 * @return 向量嵌入元数据
	 */
	private Metadata createEmbeddingMetadata(BotArticleDO articleDO, H3ParagraphModel paragraphModel) {
		Metadata metadata = new Metadata();
		// 文章名称作为来源
		metadata.put("source", articleDO.getArticleName());
		// 语雀链接
		metadata.put("yuque_url", articleDO.getArticleUrl() + StringPool.HASH + paragraphModel.getId());
		// 段落锚点
		metadata.put("anchor", paragraphModel.getId());
		return metadata;
	}

	/**
	 * 处理源数据操作
	 * <p>
	 * 根据锚点是否已存在，决定更新现有数据还是新增数据
	 * </p>
	 *
	 * @param articleDO      文章对象
	 * @param knowledgeDO    知识库对象
	 * @param paragraphModel 段落模型
	 * @param fragmentId     向量片段ID
	 * @param anchor         段落锚点
	 * @param context        处理上下文
	 */
	private void processSourceDataOperation(BotArticleDO articleDO, BotKnowledgeDO knowledgeDO,
			H3ParagraphModel paragraphModel, String fragmentId, String anchor, ArticleProcessContext context) {

		BotSourceDataDO existingSourceData = context.sourceDataDOMap.get(anchor);

		// 如果锚点已存在且数据对象不为空，则更新
		if (context.anchorList.contains(anchor) && existingSourceData != null) {
			updateExistingSourceData(existingSourceData, paragraphModel, context);
		} else {
			// 否则新增数据
			insertNewSourceData(articleDO, knowledgeDO, paragraphModel, fragmentId, context);
		}
	}

	/**
	 * 更新现有源数据
	 * <p>
	 * 更新已存在的源数据内容，并从待删除列表中移除
	 * </p>
	 *
	 * @param existingSourceData 现有源数据对象
	 * @param paragraphModel     段落模型
	 * @param context            处理上下文
	 */
	private void updateExistingSourceData(BotSourceDataDO existingSourceData, H3ParagraphModel paragraphModel,
			ArticleProcessContext context) {

		// 更新内容
		existingSourceData.setSourceContent(paragraphModel.getContentWithoutHTag());
		// 重置更新时间，让数据库自动设置
		existingSourceData.setUpdateTime(null);

		context.updateSourceDataDOList.add(existingSourceData);
		// 从待删除列表中移除
		context.anchorList.remove(paragraphModel.getId());

		LogUtil.debug(log, "VectorServiceImpl.updateExistingSourceData >> 更新现有源数据 >> anchor = {}", paragraphModel.getId());
	}

	/**
	 * 新增源数据
	 * <p>
	 * 创建新的源数据对象并添加到新增列表
	 * </p>
	 *
	 * @param articleDO      文章对象
	 * @param knowledgeDO    知识库对象
	 * @param paragraphModel 段落模型
	 * @param fragmentId     向量片段ID
	 * @param context        处理上下文
	 */
	private void insertNewSourceData(BotArticleDO articleDO, BotKnowledgeDO knowledgeDO,
			H3ParagraphModel paragraphModel, String fragmentId, ArticleProcessContext context) {

		BotSourceDataDO newSourceData = new BotSourceDataDO();
		// H3标题
		newSourceData.setTitle(paragraphModel.getH3Title());
		// 文章ID
		newSourceData.setSourceDataId(articleDO.getArticleId());
		// 内容（不含H3标签）
		newSourceData.setSourceContent(paragraphModel.getContentWithoutHTag());
		// 锚点
		newSourceData.setAnchor(paragraphModel.getId());
		// 向量片段ID
		newSourceData.setFragmentId(fragmentId);
		// 知识库ID
		newSourceData.setKnowledgeId(knowledgeDO.getKnowledgeId());
		// 初始化回答次数为0
		newSourceData.setAnswerCount(CommonConstant.ZERO);

		context.insertSourceDataDOList.add(newSourceData);

		LogUtil.debug(log, "VectorServiceImpl.insertNewSourceData >> 新增源数据 >> anchor = {}", paragraphModel.getId());
	}

	/**
	 * 在事务中批量更新数据库
	 * <p>
	 * 使用事务模板确保数据一致性，批量执行更新、新增和删除操作
	 * </p>
	 *
	 * @param context 处理上下文，包含待操作的数据列表
	 * @throws RuntimeException 当数据库操作失败时抛出
	 */
	private void updateDatabaseInTransaction(ArticleProcessContext context) {
		try {
			transactionTemplate.execute(status -> {
				// 批量更新现有文章元数据
				if (CollectionUtils.isNotEmpty(context.updateSourceDataDOList)) {
					botSourceDataDAO.updateBatchById(context.updateSourceDataDOList);
					LogUtil.info(log, "VectorServiceImpl.updateDatabaseInTransaction >> 批量更新元数据完成 >> count = {}",
							context.updateSourceDataDOList.size());
				}

				// 批量新增文章元数据
				if (CollectionUtils.isNotEmpty(context.insertSourceDataDOList)) {
					botSourceDataDAO.saveBatch(context.insertSourceDataDOList);
					LogUtil.info(log, "VectorServiceImpl.updateDatabaseInTransaction >> 批量新增元数据完成 >> count = {}",
							context.insertSourceDataDOList.size());
				}

				// 批量删除过期的文章元数据
				if (CollectionUtils.isNotEmpty(context.anchorList)) {
					botSourceDataDAO.deleteBatchByAnchorList(context.anchorList);
					LogUtil.info(log, "VectorServiceImpl.updateDatabaseInTransaction >> 批量删除元数据完成 >> count = {}",
							context.anchorList.size());
				}

				return Boolean.TRUE;
			});
		} catch (Exception e) {
			LogUtil.error(log, "VectorServiceImpl.updateDatabaseInTransaction >> 数据库事务执行失败", e);
			throw new RuntimeException("数据库批量操作失败", e);
		}
	}

	/**
	 * 文章处理上下文
	 * <p>
	 * 用于在文章处理过程中传递和维护状态信息的内部类
	 * </p>
	 */
	private static class ArticleProcessContext {
		/** 文章中的锚点列表（可变，用于删除操作） */
		List<String> anchorList;

		/** 文章中的元数据映射（锚点 -> 数据对象） */
		Map<String, BotSourceDataDO> sourceDataDOMap;

		/** 待更新的元数据列表 */
		List<BotSourceDataDO> updateSourceDataDOList;

		/** 待新增的元数据列表 */
		List<BotSourceDataDO> insertSourceDataDOList;
	}

}
