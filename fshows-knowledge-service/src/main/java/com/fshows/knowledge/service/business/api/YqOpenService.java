package com.fshows.knowledge.service.business.api;

/**
 * 语雀开放接口服务
 */
public interface YqOpenService {
    
    /**
     * 下载语雀文章，获取导出URL
     * 
     * @param docId 文档ID，语雀文档的唯一标识符
     * @param cookie cookie
     * @param csrfToken csrfToken
     * @return 导出文章的下载URL，可直接用于下载markdown文件
     * @throws RuntimeException 当请求失败或响应格式异常时抛出
     */
    String downloadYuQueArticle(String docId, String cookie, String csrfToken);
    
    /**
     * 获取语雀文章内容 （通过 url 中anchor 控制是否返回为H 标签内容）
     * 
     * @param downloadUrl 文章下载URL，通过downloadYuQueArticle方法获取
     * @param cookie cookie
     * @param csrfToken csrfToken
     * @return 文章的markdown内容
     * @throws RuntimeException 当请求失败或响应格式异常时抛出
     */
    String getYuQueArticleContent(String downloadUrl, String cookie, String csrfToken);
}
