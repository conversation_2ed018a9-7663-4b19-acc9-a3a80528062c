/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved
 */
package com.fshows.knowledge.service.util;

import cn.hutool.core.util.ObjectUtil;
import com.huike.nova.common.constant.CommonConstant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version NumUtil.java, v 0.1 2024-07-18-3:30 下午 liubo
 */
public class NumUtil {


    /**
     * 元转分
     *
     * @param num
     * @return {@link String}
     */
    public static String yuanToFen(BigDecimal num) {

        if (ObjectUtil.isNull(num)) {
            return null;
        }
        BigDecimal fenValue = num.multiply(new BigDecimal("100"));
        int fenIntValue = fenValue.intValue();
        return String.valueOf(fenIntValue);
    }

    /**
     * 元转分
     *
     * @param num
     * @return {@link String}
     */
    public static Integer yuanToFenInteger(BigDecimal num) {
        if (ObjectUtil.isNull(num)) {
            return CommonConstant.ZERO;
        }
        BigDecimal fenValue = num.multiply(new BigDecimal("100"));
        return fenValue.intValue();
    }

    /**
     * 元转分
     *
     * @param num
     * @return {@link String}
     */
    public static Long yuanToFenLong(BigDecimal num) {

        if (num == null) {
            return null;
        }
        // 将元转换为分
        BigDecimal fenValue = num.multiply(new BigDecimal("100"));
        return fenValue.longValueExact();
    }

    /**
     * 元转分
     *
     * @param num
     * @return {@link String}
     */
    public static BigDecimal fenToYuan(Long num) {

        if (num == null) {
            return null;
        }
        BigDecimal fen = new BigDecimal(num);
        BigDecimal yuan = fen.divide(new BigDecimal("100"));
        return yuan;
    }
}