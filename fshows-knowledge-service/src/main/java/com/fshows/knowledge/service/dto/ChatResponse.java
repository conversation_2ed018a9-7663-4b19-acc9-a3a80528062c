package com.fshows.knowledge.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问答接口响应类
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "RAG查询响应结果")
public class ChatResponse {

    /**
     * 请求是否成功
     */
    @Schema(description = "请求是否成功", example = "true")
    private boolean success;

    /**
     * 错误或状态消息 (例如，在 success=false 时显示 "网络繁忙，请稍后重试")
     */
    @Schema(description = "响应消息", example = "查询成功")
    private String message;

    /**
     * 用户输入的问题
     */
    @Schema(description = "用户查询问题", example = "如何使用RAG技术？")
    private String query;

    /**
     * 答案
     */
    @Schema(description = "AI生成的答案")
    private String answer;

    /**
     * 消息 id 本次提问的唯一标识
     */
    @Schema(description = "消息唯一标识", example = "msg_123456789")
    private String messageId;

    /**
     * 答案开始文本
     */
    @Schema(description = "答案开始标识文本")
    private String answerStartText;

    /**
     * 答案结束文本
     */
    @Schema(description = "答案结束标识文本")
    private String answerEndText;

    /**
     * AI回答所引用的附件/资料来源列表
     */
    @Schema(description = "检索到的相关文档列表")
    private List<MatchResult> matchResultList;

    /**
     * 匹配结果详情
     */
    @Data
    @Schema(description = "检索匹配结果")
    public static class MatchResult {
        /**
         * 引用的资料内容
         */
        @Schema(description = "匹配的文档内容片段")
        private String text;
        
        /**
         * 相似度分数
         */
        @Schema(description = "相似度分数", example = "0.85")
        private double score;
        
        /**
         * 语雀文档原地址 url
         */
        @Schema(description = "原始文档URL")
        private String yuQueUrl;
        
        /**
         * 语雀文档名称
         */
        @Schema(description = "文档标题", example = "RAG技术指南")
        private String source;
        
        /**
         * 包含锚点信息的语雀文档地址 url
         */
        @Schema(description = "带锚点的文档URL")
        private String yuQueUrlAnchor;
    }
}