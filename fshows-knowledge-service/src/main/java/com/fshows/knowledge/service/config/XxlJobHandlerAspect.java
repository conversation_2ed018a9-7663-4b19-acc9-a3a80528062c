package com.fshows.knowledge.service.config;

import com.huike.nova.common.util.TraceIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

/**
 * xxljob日志增加traceId
 *
 * <AUTHOR>
 * @date 2023/4/19 10:00
 * @copyright 2022 barm Inc. All rights reserved
 */
@Aspect
@Component
@Slf4j
public class XxlJobHandlerAspect {
    @Before("execution(public * com..*.execute(String))")
    public void beforeMethod(JoinPoint joinPoint) {
        //往slf4j的MDC中添加traceId
        String traceId = TraceIdGenerator.generate();
        MDC.put("TRACE_ID", traceId);
    }
}
