/**
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.fshows.knowledge.service.message;

/**
 * <AUTHOR>
 * @version IProducer.java, v 0.1 2022-03-10 4:54 下午 mayucong
 */
public interface IProducer {

    /**
     * 发送消息（适用于消息内容少，messageKey和内容相同的情况）
     *
     * @param msg 发送的消息体与messageKey
     * @return boolean
     */
    boolean sendMessage(String msg);

    /**
     * 发送消息
     *
     * @param key 消息的key
     * @param msg 发送的消息
     * @return boolean
     */
    boolean sendMessage(String key, String msg);


    /**
     * 发送延迟消息
     *
     * @param msg        发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    boolean sendDelayMessage(String msg, int dliverTime);

    /**
     * 发送延迟消息
     *
     * @param key        作为key
     * @param body       发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    boolean sendDelayMessage(String key, String body, int dliverTime);

    /**
     * 发送消息（适用于消息内容少，messageKey和内容相同的情况）
     * push MQ失败默认重试一次
     *
     * @param msg 发送的消息体与messageKey
     * @return boolean
     */
    boolean sendMessageRetry(String msg);

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param key 消息的key
     * @param msg 发送的消息
     * @return boolean
     */
    boolean sendMessageRetry(String key, String msg);

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param msg        发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    boolean sendDelayMessageRetry(String msg, int dliverTime);

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param key        作为key
     * @param body       发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    boolean sendDelayMessageRetry(String key, String body, int dliverTime);


    /**
     * 发送延迟消息
     *
     * @param msg        发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    boolean sendDelayMessage(String msg, long dliverTime);

    /**
     * 发送延迟消息
     *
     * @param key        作为key
     * @param body       发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    boolean sendDelayMessage(String key, String body, long dliverTime);

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param msg        发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    boolean sendDelayMessageRetry(String msg, long dliverTime);

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param key        作为key
     * @param body       发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    boolean sendDelayMessageRetry(String key, String body, long dliverTime);

    /**
     * 启动服务
     */
    void start();

    /**
     * 关闭服务
     */
    void shutdown();
}
