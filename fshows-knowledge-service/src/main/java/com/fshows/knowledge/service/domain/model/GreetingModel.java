package com.fshows.knowledge.service.domain.model;

import lombok.Data;

import java.util.List;

/**
 * GreetingResponse
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Data
public class GreetingModel {

    /**
     * '指引文案'
     */
    private String guideTitle;

    /**
     * '开场问候语'
     */
    private String openingGreeting;

    /**
     * '答案开始文本'
     */
    private String answerStartText;

    /**
     * '答案结束文本'
     */
    private String answerEndText;

    /**
     * 推荐问题列表
     */
    private List<String> recommendQuestionList;

}