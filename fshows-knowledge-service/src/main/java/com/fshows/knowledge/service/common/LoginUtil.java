/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.knowledge.service.common;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.fshows.knowledge.service.domain.model.LoginModel;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @version LoginUtil.java, v 0.1 2024-06-06 10:08 AM ruanzy
 */
public class LoginUtil {

    /**
     * 获得登录基础信息
     */
    public static LoginModel getLoginBasicInfo() {
        String loginModelJson = getLoginModelJson();
        if (StringUtils.isNotBlank(loginModelJson)) {
            return JSONObject.parseObject(loginModelJson, LoginModel.class);
        } else {
            throw new CommonException(ErrorCodeEnum.LOGIN_INVALID);
        }
    }

    /**
     * 请求头获取accessToken
     *
     * @return
     */
    private static String getLoginModelJson() {
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        final String accessToken = ServletUtil.getHeaderIgnoreCase(request, "Access-Token");
        return (String) StpUtil.getTokenSessionByToken(accessToken).get(accessToken);
    }
}