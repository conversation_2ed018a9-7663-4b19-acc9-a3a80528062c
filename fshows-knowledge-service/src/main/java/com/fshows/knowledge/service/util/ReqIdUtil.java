/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved
 */
package com.fshows.knowledge.service.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import com.huike.nova.common.enums.acct.GenerateIdPrefixEnum;

import java.util.Date;

/**
 * <AUTHOR>
 * @version ReqIdUtil.java, v 0.1 2024-07-18-5:46 下午 liubo
 */
public class ReqIdUtil {

    /**
     * 根据业务生成id
     * @param prefixEnum
     * @param length
     * @return {@link String}
     */
    public static String generateId(GenerateIdPrefixEnum prefixEnum, int length) {

        return prefixEnum.getValue() + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + RandomUtil.randomNumbers(length);

    }
}