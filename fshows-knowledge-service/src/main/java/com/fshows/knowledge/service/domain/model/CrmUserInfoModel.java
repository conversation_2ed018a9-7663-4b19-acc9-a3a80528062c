package com.fshows.knowledge.service.domain.model;

import lombok.Data;

/**
 * CrmUserInfoModel
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Data
public class CrmUserInfoModel {
    /**
     * 错误代码
     * 9996: 登录过期，请重新登录
     */
    private String errorCode;

    /**
     * 错误消息
     * 描述具体的错误信息
     */
    private String errorMsg;

    /**
     * 请求是否成功
     * true: 成功, false: 失败
     */
    private boolean success;
    
    /**
     * 司南登录账号电话号码
     */
    private String mobile;
    /**
     * 司南的用户Id lm_crm_user.user_id
     */
    private String userId;
    /**
     * 代理商Id
     */
    private Integer sysUserId;
    /**
     * 用户类型
     */
    private Integer userType;
}