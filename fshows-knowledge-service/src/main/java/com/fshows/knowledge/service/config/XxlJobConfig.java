/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.fshows.knowledge.service.config;

import com.huike.nova.common.util.LogUtil;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version XxlJobConfig.java, v 0.1 2023-01-03 2:06 PM ruanzy
 */
@Configuration
@Slf4j
@ComponentScan(basePackages = "com.fshows.knowledge.service.jobhandler")
public class XxlJobConfig {
    @Value("${knowledge.xxl.job.admin-addresses}")
    private String adminAddresses;

    @Value("${knowledge.xxl.job.executor-appname}")
    private String appName;

    @Value("${knowledge.xxl.job.executor-port:0}")
    private int port;

    @Value("${knowledge.xxl.job.executor-logpath}")
    private String logPath;

    @Value("${knowledge.xxl.job.executor-logretentiondays}")
    private int logRetentionDays;

    @Bean(initMethod = "start", destroyMethod = "destroy")
    public XxlJobSpringExecutor xxlJobExecutor() {
        LogUtil.info(log, "xxl-job config init.");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
        xxlJobSpringExecutor.setAppName(appName);
        xxlJobSpringExecutor.setIp(null);
        //TODO 更改端口 用于测试
        //port = 45518;
        xxlJobSpringExecutor.setPort(port);
        xxlJobSpringExecutor.setAccessToken(null);
        xxlJobSpringExecutor.setLogPath(logPath);
        xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);
        return xxlJobSpringExecutor;
    }
}