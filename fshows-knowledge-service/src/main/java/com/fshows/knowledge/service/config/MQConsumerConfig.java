package com.fshows.knowledge.service.config;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.MessageListener;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.aliyun.openservices.ons.api.bean.Subscription;
import com.google.common.collect.Maps;
import com.huike.nova.common.config.SysConfig;
import com.huike.nova.common.enums.EnvEnum;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.TraceIdGenerator;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * MQ消费者
 *
 * <AUTHOR>
 * @version MQConsumerConfig.java, v 0.1 2022/3/11 9:49 上午 mayucong
 */
@Configuration
@AllArgsConstructor
@Slf4j
public class MQConsumerConfig {

    private MQConfig mqConfig;
    private SysConfig sysConfig;


    /**
     * 创建消费者对象
     *
     * @param consumerId      消费者ID
     * @param topicId         主体
     * @param messageListener 消费者
     * @return
     */
    private ConsumerBean createTagConsumerBean(String consumerId, String topicId, String tag, MessageListener messageListener) {
        ConsumerBean consumerBean = new ConsumerBean();
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, mqConfig.getMqNamesrvAddress());
        properties.setProperty(PropertyKeyConst.ConsumerId, consumerId);
        properties.setProperty(PropertyKeyConst.AccessKey, mqConfig.getAccesskey());
        properties.setProperty(PropertyKeyConst.SecretKey, mqConfig.getSecretkey());
        // 重试次数
        properties.setProperty(PropertyKeyConst.MaxReconsumeTimes, "26");
        // 消费者数量
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "15");
        properties.setProperty(PropertyKeyConst.ConsumeTimeout, "3");

        Subscription subscription = new Subscription();
        subscription.setTopic(topicId);
        subscription.setExpression("*");
        subscription.setExpression(tag);

        Map<Subscription, MessageListener> subscriptionTable = Maps.newHashMap();

        subscriptionTable.put(subscription, (message, context) -> {
            Action action;
            try {
                String mdcTraceId = (String) message.getUserProperties().get("TRACE_ID");
                MDC.put("TRACE_ID", mdcTraceId == null ? TraceIdGenerator.generate() : mdcTraceId);
                action = messageListener.consume(message, context);
            } finally {
                MDC.remove("TRACE_ID");
            }
            return action;
        });

        consumerBean.setProperties(properties);
        consumerBean.setSubscriptionTable(subscriptionTable);

        return consumerBean;
    }

    /**
     * 创建导出消费者对象
     *
     * @param consumerId      消费者ID
     * @param topicId         主体
     * @param messageListener 消费者
     * @return
     */
    private ConsumerBean exportCreateTagConsumerBean(String consumerId, String topicId, String tag, MessageListener messageListener) {
        ConsumerBean consumerBean = new ConsumerBean();
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, mqConfig.getMqNamesrvAddress());
        properties.setProperty(PropertyKeyConst.ConsumerId, consumerId);
        properties.setProperty(PropertyKeyConst.AccessKey, mqConfig.getAccesskey());
        properties.setProperty(PropertyKeyConst.SecretKey, mqConfig.getSecretkey());
        // 重试次数
        properties.setProperty(PropertyKeyConst.MaxReconsumeTimes, "26");
        // 消费者数量
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "2");
        properties.setProperty(PropertyKeyConst.ConsumeTimeout, "3");

        Subscription subscription = new Subscription();
        subscription.setTopic(topicId);
        subscription.setExpression("*");
        subscription.setExpression(tag);

        Map<Subscription, MessageListener> subscriptionTable = Maps.newHashMap();

        subscriptionTable.put(subscription, (message, context) -> {
            Action action;
            try {
                String mdcTraceId = (String) message.getUserProperties().get("TRACE_ID");
                MDC.put("TRACE_ID", mdcTraceId == null ? TraceIdGenerator.generate() : mdcTraceId);
                action = messageListener.consume(message, context);
            } finally {
                MDC.remove("TRACE_ID");
            }
            return action;
        });

        consumerBean.setProperties(properties);
        consumerBean.setSubscriptionTable(subscriptionTable);

        return consumerBean;
    }


    /**
     * 创建导出指定 ip消费者对象（只给通用导出使用）
     *
     * @param consumerId      消费者ID
     * @param topicId         主体
     * @param messageListener 消费者
     * @return
     */
    private ConsumerBean exportSpecifyIpCreateTagConsumerBean(String consumerId, String topicId, String tag, MessageListener messageListener) {
        LogUtil.info(log, "MQConsumerConfig.exportSpecifyIpCreateTagConsumerBean >> 接口开始 >> topicId = {},tag={},gid={}", topicId,tag,consumerId);
        // 获取当前机器 ip
        try {
            String hostAddress = InetAddress.getLocalHost().getHostAddress();
            List<String> exportIpWhiteList = JSONObject.parseArray(sysConfig.getExportIpWhiteList(), String.class);
            LogUtil.info(log, "MQConsumerConfig.exportSpecifyIpCreateTagConsumerBean >> 获取 ip 结束 >>  hostAddress = {},exportIpWhiteList={},env={}",hostAddress, exportIpWhiteList,sysConfig.getEnv());
            if ((EnvEnum.PROD.getValue().equalsIgnoreCase(sysConfig.getEnv()) || EnvEnum.BETA.getValue().equalsIgnoreCase(sysConfig.getEnv())) &&
                    !exportIpWhiteList.contains(hostAddress)){
                LogUtil.info(log, "MQConsumerConfig.exportSpecifyIpCreateTagConsumerBean >> 不创建导出消费 >> topicId = {},tag={},gid={},hostAddress={},exportIpWhiteList={},当前机器ip不在白名单内", topicId,tag,consumerId,hostAddress,exportIpWhiteList);
                return null;
            }
        } catch (UnknownHostException e) {
            LogUtil.error(log, "MQConsumerConfig.exportSpecifyIpCreateTagConsumerBean >> 接口异常 >> topicId = {},tag={},gid={}", topicId,tag,consumerId);
        }
        ConsumerBean consumerBean = new ConsumerBean();
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, mqConfig.getMqNamesrvAddress());
        properties.setProperty(PropertyKeyConst.ConsumerId, consumerId);
        properties.setProperty(PropertyKeyConst.AccessKey, mqConfig.getAccesskey());
        properties.setProperty(PropertyKeyConst.SecretKey, mqConfig.getSecretkey());
        // 重试次数
        properties.setProperty(PropertyKeyConst.MaxReconsumeTimes, "26");
        // 消费者数量
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, "2");
        properties.setProperty(PropertyKeyConst.ConsumeTimeout, "3");

        Subscription subscription = new Subscription();
        subscription.setTopic(topicId);
        subscription.setExpression("*");
        subscription.setExpression(tag);

        Map<Subscription, MessageListener> subscriptionTable = Maps.newHashMap();

        subscriptionTable.put(subscription, (message, context) -> {
            Action action;
            try {
                String mdcTraceId = (String) message.getUserProperties().get("TRACE_ID");
                MDC.put("TRACE_ID", mdcTraceId == null ? TraceIdGenerator.generate() : mdcTraceId);
                action = messageListener.consume(message, context);
            } finally {
                MDC.remove("TRACE_ID");
            }
            return action;
        });

        consumerBean.setProperties(properties);
        consumerBean.setSubscriptionTable(subscriptionTable);

        return consumerBean;
    }

}