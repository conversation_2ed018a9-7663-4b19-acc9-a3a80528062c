package com.fshows.knowledge.service.business;

import com.fshows.knowledge.service.domain.model.GreetingModel;
import com.fshows.knowledge.service.domain.param.QuestionMatchParam;

import java.util.List;

/**
 * QuestionService
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
public interface QuestionService {
    /**
     * 获取推荐问题和问候语
     *
     * @return 结果
     */
    GreetingModel getGreeting();

    /**
     * 匹配问题
     *
     * @param param 参数
     * @return 结果
     */
    List<String> questionMatch(QuestionMatchParam param);

}