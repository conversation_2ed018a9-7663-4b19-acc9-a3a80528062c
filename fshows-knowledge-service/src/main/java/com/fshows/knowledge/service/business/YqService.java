package com.fshows.knowledge.service.business;

import com.fshows.knowledge.service.domain.param.YuQueWebHookParam;
import lombok.Data;

/**
 * YqService
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/20
 */
public interface YqService {

    /**
     * 语雀文章创建、回调处理
     *
     * @param param 语雀回调参数
     */
    void handleYuQueWebhook(YuQueWebHookParam param);

    /**
     * 下载文章
     *
     * @param bookId    知识库Id
     * @param articleId 文章Id
     */
    void bookArticleDownload(String bookId, String articleId);
}