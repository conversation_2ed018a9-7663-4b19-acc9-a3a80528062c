package com.fshows.knowledge.service.message;


import com.huike.nova.common.enums.ProduceEnum;

/**
 * <AUTHOR>
 * @version MsgProducer.java, v 0.1 2022/3/10 4:04 下午 mayucong
 */
public interface MsgProducer {
    /**
     * 发送消息
     *
     * @param produceEnum 生产者类型
     * @param key         消息key
     * @param msg         发送的消息
     * @return
     */
    boolean sendMessage(ProduceEnum produceEnum, String key, String msg);

    /**
     * 发送消息
     *
     * @param produceEnum 生产者类型
     * @param msg         发送的消息
     * @return
     */
    boolean sendMessage(ProduceEnum produceEnum, String msg);


    /**
     * 发送延迟消息
     *
     * @param produceEnum 生产者类型
     * @param msg         发送的消息
     * @param dliverTime  延时发送时间:3 * 60 * 1000 延时三分钟
     * @return
     */
    boolean sendDelayMessage(ProduceEnum produceEnum, String msg, int dliverTime);

    /**
     * 发送延迟消息
     *
     * @param produceEnum 生产者类型
     * @param key         key - 一般是订单号
     * @param body        发送的消息
     * @param dliverTime  延时发送时间:3 * 60 * 1000 延时三分钟
     * @return
     */
    boolean sendDelayMessage(ProduceEnum produceEnum, String key, String body, int dliverTime);

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param produceEnum 生产者类型
     * @param key         消息key
     * @param msg         发送的消息
     * @return
     */
    boolean sendMessageRetry(ProduceEnum produceEnum, String key, String msg);

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param produceEnum 生产者类型
     * @param msg         发送的消息
     * @return
     */
    boolean sendMessageRetry(ProduceEnum produceEnum, String msg);


    /**
     * 发送延迟消息,push MQ失败默认重试一次
     *
     * @param produceEnum 生产者类型
     * @param msg         发送的消息
     * @param dliverTime  延时发送时间:3 * 60 * 1000 延时三分钟
     * @return
     */
    boolean sendDelayMessageRetry(ProduceEnum produceEnum, String msg, int dliverTime);

    /**
     * 发送延迟消息,push MQ失败默认重试一次
     *
     * @param produceEnum 生产者类型
     * @param key         key - 一般是订单号
     * @param body        发送的消息
     * @param dliverTime  延时发送时间:3 * 60 * 1000 延时三分钟
     * @return
     */
    boolean sendDelayMessageRetry(ProduceEnum produceEnum, String key, String body, int dliverTime);

    /**
     * 发送延迟消息,push MQ失败默认重试一次
     *
     * @param produceEnum 生产者类型
     * @param msg         发送的消息
     * @param dliverTime  延时发送时间:3 * 60 * 1000 延时三分钟
     * @return
     */
    boolean sendDelayMessageRetry(ProduceEnum produceEnum, String msg, long dliverTime);

}