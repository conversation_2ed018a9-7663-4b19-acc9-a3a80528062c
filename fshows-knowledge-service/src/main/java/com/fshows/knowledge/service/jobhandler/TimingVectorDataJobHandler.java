/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.knowledge.service.jobhandler;

import cn.hutool.core.date.DateUtil;
import com.fshows.knowledge.service.business.api.VectorService;
import com.huike.nova.common.util.LogUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
@JobHandler("TimingVectorDataJobHandler")
@AllArgsConstructor
public class TimingVectorDataJobHandler extends IJobHandler {

	private VectorService vectorService;


	/**
	 * 定时向量数据脚本
	 *
	 * @param content 集团 appid
	 */
	@Override
	public ReturnT<String> execute(String content) {
		XxlJobLogger.log("TimingVectorDataJobHandler >> 脚本执行开始：time = {}", DateUtil.now());
		LogUtil.info(log, "TimingVectorDataJobHandler >> 脚本执行开始 >> content = {}", content);
		try {
			vectorService.timingVectorData(content);
			LogUtil.info(log, "TimingVectorDataJobHandler >> 脚本执行结束 >> content = {}", content);
			XxlJobLogger.log("TimingVectorDataJobHandler >> 脚本执行结束：time = {}", DateUtil.now());
			return ReturnT.SUCCESS;
		}catch (Exception e){
			LogUtil.error(log, "TimingVectorDataJobHandler >> 脚本执行异常 >> content = {}", e, content);
			throw e;
		}
	}
}