package com.fshows.knowledge.service;

import com.fshows.knowledge.service.dto.ChatResponse;
import com.fshows.knowledge.service.dto.QueryRequest;

/**
 * RAG查询服务接口
 * 
 * <AUTHOR>
 */
public interface RagQueryService {

    /**
     * 处理RAG查询请求
     * 
     * @param request 查询请求参数
     * @return 查询响应结果
     */
    ChatResponse processQuery(QueryRequest request);

    /**
     * 验证知识库访问权限
     * 
     * @param querySource 查询来源
     * @return 是否有权限访问
     */
    boolean validateKnowledgeBaseAccess(Integer querySource);

}