package com.fshows.knowledge.service.business;

import com.fshows.knowledge.service.domain.model.FavoriteListPageModel;
import com.fshows.knowledge.service.domain.param.FavoriteCancelParam;
import com.fshows.knowledge.service.domain.param.FavoriteListPageParam;
import com.fshows.knowledge.service.domain.param.FavoriteSaveParam;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;

/**
 * FavoriteService
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
public interface FavoriteService {

    /**
     * 收藏问题
     *
     * @param param 参数
     */
    void favoriteSave(FavoriteSaveParam param);

    /**
     * 取消收藏
     *
     * @param param 参数
     */
    void favoriteCancel(FavoriteCancelParam param);

    /**
     * 收藏列表
     *
     * @param pageParam 参数
     * @return 结果
     */
    PageResult<FavoriteListPageModel> pageFavoriteList(PageParam<FavoriteListPageParam> pageParam);
}