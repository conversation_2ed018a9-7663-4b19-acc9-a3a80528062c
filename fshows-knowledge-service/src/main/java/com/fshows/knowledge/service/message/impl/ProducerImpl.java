/**
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.fshows.knowledge.service.message.impl;

import cn.hutool.core.util.StrUtil;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.SendResult;
import com.aliyun.openservices.ons.api.exception.ONSClientException;
import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.fsframework.core.utils.SystemClock;
import com.fshows.knowledge.service.common.ProduceBean;
import com.fshows.knowledge.service.message.IProducer;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

/**
 * <AUTHOR>
 * @version ProducerImpl.java, v 0.1 2022-03-10 4:55 下午 mayucong
 */
@Slf4j
public class ProducerImpl implements IProducer {
    private ProduceBean produce;

    public ProducerImpl(ProduceBean bean) {
        this.produce = bean;
    }

    private boolean pushMessage(Message message) {
        Producer producer = produce.getProducer();
        // 发送消息，只要不抛异常就是成功
        try {
            message.putUserProperties("TRACE_ID", MDC.get("TRACE_ID"));
            SendResult sendResult = producer.send(message);
            LogUtil.info(log, "pushMessage >> 消息队列,发送消息成功！key={}, SendResult={}", message.getKey(), sendResult);
            return true;
        } catch (ONSClientException e) {
            LogUtil.error(log, "pushMessage >> 消息队列,发送消息失败！key = {}", e, message.getKey());
            return false;
        }
    }

    /**
     * 发送消息（适用于消息内容少，messageKey和内容相同的情况）
     *
     * @param msg 发送的消息体与messageKey
     * @return boolean
     */
    @Override
    public boolean sendMessage(String msg) {
        return sendMessage(msg, msg);
    }

    /**
     * 发送消息
     *
     * @param key 消息的key
     * @param msg 发送的消息
     * @return boolean
     */
    @Override
    public boolean sendMessage(String key, String msg) {
        if (StrUtil.isBlank(msg)) {
            LogUtil.warn(log, "sendMessage >> 缺少生产者配置信息 Msg =null");
            return false;
        }
        Message message = new Message(produce.getTopicId(), produce.getTag(), msg.getBytes());
        message.setKey(produce.getKeyPrefix() + key);
        return pushMessage(message);
    }

    /**
     * 发送延迟消息
     *
     * @param msg        发送的消息
     * @param dliverTime 延时发送时间：3 * 60 * 1000 延时三分钟
     * @return
     */
    @Override
    public boolean sendDelayMessage(String msg, int dliverTime) {
        if (StrUtil.isBlank(msg)) {
            LogUtil.warn(log, "sendDelayMessage >> 缺少生产者配置信息 Msg =null");
            return false;
        }
        Message message = new Message(produce.getTopicId(), produce.getTag(), msg.getBytes());
        message.setKey(produce.getKeyPrefix() + msg);
        message.setStartDeliverTime(SystemClock.millisClock().now() + dliverTime);
        return pushMessage(message);
    }

    /**
     * 发送延迟消息
     *
     * @param key        作为key
     * @param body       发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    @Override
    public boolean sendDelayMessage(String key, String body, int dliverTime) {
        if (StrUtil.isBlank(body) || StrUtil.isBlank(key)) {
            LogUtil.warn(log, "缺少生产者配置信息 Msg ={},key={}", body, key);
            return false;
        }
        Message message = new Message(produce.getTopicId(), produce.getTag(), body.getBytes());
        message.setKey(produce.getKeyPrefix() + key);
        message.setStartDeliverTime(SystemClock.millisClock().now() + dliverTime);
        return pushMessage(message);
    }

    /**
     * push MQ失败默认重试一次
     *
     * @param message
     * @return
     */
    private boolean pushMessageRetry(Message message) {
        Producer producer = produce.getProducer();
        // 发送消息，只要不抛异常就是成功
        try {
            message.putUserProperties("TRACE_ID", MDC.get("TRACE_ID"));
            SendResult sendResult = producer.send(message);
            LogUtil.info(log, "pushMessageRetry >> 消息队列,发送消息成功！key={}, SendResult={}", message.getKey(), sendResult);
            return true;
        } catch (ONSClientException e) {
            LogUtil.warn(log, "pushMessageRetry >> 消息队列,发送消息失败,发起重试一次！key = {}", e, message.getKey());
            return pushMessage(message);
        }
    }

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param msg
     * @return
     */
    @Override
    public boolean sendMessageRetry(String msg) {
        return sendMessageRetry(msg, msg);
    }

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param key 消息的key
     * @param msg 发送的消息
     * @return boolean
     */
    @Override
    public boolean sendMessageRetry(String key, String msg) {
        if (StrUtil.isBlank(msg)) {
            LogUtil.warn(log, "缺少生产者配置信息 Msg =null");
            return false;
        }
        Message message = new Message(produce.getTopicId(), produce.getTag(), msg.getBytes());
        message.setKey(produce.getKeyPrefix() + key);
        return pushMessageRetry(message);
    }

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param msg        发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    @Override
    public boolean sendDelayMessageRetry(String msg, int dliverTime) {
        if (StrUtil.isBlank(msg)) {
            LogUtil.warn(log, "sendDelayMessageRetry >> 缺少生产者配置信息 Msg =null");
            return false;
        }
        Message message = new Message(produce.getTopicId(), produce.getTag(), msg.getBytes());
        message.setKey(produce.getKeyPrefix() + msg);
        message.setStartDeliverTime(SystemClock.millisClock().now() + dliverTime);
        return pushMessageRetry(message);
    }

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param key        作为key
     * @param body       发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    @Override
    public boolean sendDelayMessageRetry(String key, String body, int dliverTime) {
        if (StrUtil.isBlank(body) || StrUtil.isBlank(key)) {
            LogUtil.warn(log, "sendDelayMessageRetry >> 缺少生产者配置信息 Msg ={},key={}", body, key);
            return false;
        }
        Message message = new Message(produce.getTopicId(), produce.getTag(), body.getBytes());
        message.setStartDeliverTime(SystemClock.millisClock().now() + dliverTime);
        message.setKey(produce.getKeyPrefix() + key);
        return pushMessageRetry(message);
    }

    @Override
    public boolean sendDelayMessage(String msg, long dliverTime) {
        if (msg == null) {
            LogUtil.warn(log, "sendDelayMessage >> 缺少生产者配置信息 Msg =null");
            return false;
        }
        Message message = new Message(produce.getTopicId(), produce.getTag(), msg.getBytes());
        message.setKey(produce.getKeyPrefix() + msg);
        message.setStartDeliverTime(SystemClock.millisClock().now() + dliverTime);
        return pushMessage(message);
    }

    /**
     * 发送延迟消息
     *
     * @param key        作为key
     * @param body       发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    @Override
    public boolean sendDelayMessage(String key, String body, long dliverTime) {
        if (StrUtil.isBlank(body) || StrUtil.isBlank(key)) {
            LogUtil.warn(log, "缺少生产者配置信息 Msg ={},key={}", body, key);
            return false;
        }
        Message message = new Message(produce.getTopicId(), produce.getTag(), body.getBytes());
        message.setKey(produce.getKeyPrefix() + key);
        message.setStartDeliverTime(SystemClock.millisClock().now() + dliverTime);
        return pushMessage(message);
    }

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param msg        发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    @Override
    public boolean sendDelayMessageRetry(String msg, long dliverTime) {
        if (StrUtil.isBlank(msg)) {
            LogUtil.warn(log, "sendDelayMessageRetry >> 缺少生产者配置信息 Msg =null");
            return false;
        }
        Message message = new Message(produce.getTopicId(), produce.getTag(), msg.getBytes());
        message.setKey(produce.getKeyPrefix() + msg);
        message.setStartDeliverTime(SystemClock.millisClock().now() + dliverTime);
        return pushMessageRetry(message);
    }

    /**
     * 发送消息,push MQ失败默认重试一次
     *
     * @param key        作为key
     * @param body       发送的消息
     * @param dliverTime 延时发送时间:3 * 60 * 1000 延时三分钟
     * @return boolean
     */
    @Override
    public boolean sendDelayMessageRetry(String key, String body, long dliverTime) {
        if (StrUtil.isBlank(body) || StrUtil.isBlank(key)) {
            LogUtil.warn(log, "sendDelayMessageRetry >> 缺少生产者配置信息 Msg ={},key={}", body, key);
            return false;
        }
        Message message = new Message(produce.getTopicId(), produce.getTag(), body.getBytes());
        message.setStartDeliverTime(SystemClock.millisClock().now() + dliverTime);
        message.setKey(produce.getKeyPrefix() + key);
        return pushMessageRetry(message);
    }

    /**
     * 启动服务
     */
    @Override
    public void start() {
        produce.getProducer().start();
    }

    /**
     * 关闭服务
     */
    @Override
    public void shutdown() {
        produce.getProducer().shutdown();
    }
}
