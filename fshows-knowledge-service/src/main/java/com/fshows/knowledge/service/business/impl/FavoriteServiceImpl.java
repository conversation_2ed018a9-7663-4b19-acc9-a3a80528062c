package com.fshows.knowledge.service.business.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.knowledge.dao.domain.param.FavoriteListPageParamDTO;
import com.fshows.knowledge.dao.domain.result.FavoriteListPageResultDTO;
import com.fshows.knowledge.dao.entity.BotUserFavoritesDO;
import com.fshows.knowledge.dao.repository.BotUserFavoritesDAO;
import com.fshows.knowledge.service.business.FavoriteService;
import com.fshows.knowledge.service.common.LoginUtil;
import com.fshows.knowledge.service.domain.mapper.FavoriteServiceObjMapper;
import com.fshows.knowledge.service.domain.model.FavoriteListPageModel;
import com.fshows.knowledge.service.domain.model.LoginModel;
import com.fshows.knowledge.service.domain.param.FavoriteCancelParam;
import com.fshows.knowledge.service.domain.param.FavoriteListPageParam;
import com.fshows.knowledge.service.domain.param.FavoriteSaveParam;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.metadata.PageParam;
import com.huike.nova.common.metadata.PageResult;
import com.huike.nova.common.util.IdWorkerUtil;
import com.huike.nova.common.util.LogUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * FavoriteServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Slf4j
@Service
@AllArgsConstructor
public class FavoriteServiceImpl implements FavoriteService {

    private BotUserFavoritesDAO botUserFavoritesDAO;
    private FavoriteServiceObjMapper favoriteServiceObjMapper;

    /**
     * 收藏问题
     *
     * @param param 参数
     */
    @Override
    public void favoriteSave(FavoriteSaveParam param) {
        if (ObjectUtil.isNotNull(botUserFavoritesDAO.getByMessageId(param.getMessageId()))) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("请勿重复收藏");
        }
        LoginModel loginBasicInfo = LoginUtil.getLoginBasicInfo();
        BotUserFavoritesDO favoritesDO = new BotUserFavoritesDO();
        favoritesDO.setFavoriteId(IdWorkerUtil.getSingleId());
        favoritesDO.setUserId(loginBasicInfo.getUserId());
        favoritesDO.setQuestion(param.getQuestion());
        favoritesDO.setAnswer(param.getAnswer());
        favoritesDO.setMessageId(param.getMessageId());
        botUserFavoritesDAO.save(favoritesDO);
    }

    /**
     * 取消收藏
     *
     * @param param 参数
     */
    @Override
    public void favoriteCancel(FavoriteCancelParam param) {
        BotUserFavoritesDO favoritesDO = botUserFavoritesDAO.getByFavoriteId(param.getFavoriteId());
        if (ObjectUtil.isNull(favoritesDO)) {
            LogUtil.warn(log, "favoriteCancel >> 收藏数据不存在 >> param ={}", JSON.toJSONString(param));
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("收藏数据不存在");
        }
        botUserFavoritesDAO.removeById(favoritesDO);
    }

    /**
     * 收藏列表
     *
     * @param pageParam 参数
     * @return 结果
     */
    @Override
    public PageResult<FavoriteListPageModel> pageFavoriteList(PageParam<FavoriteListPageParam> pageParam) {
        if (!LoginUtil.getLoginBasicInfo().getUserId().equals(pageParam.getQuery().getUserId())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("违规操作");
        }
        PageParam<FavoriteListPageParamDTO> pageParamDTO = favoriteServiceObjMapper.toFavoriteListPageParamDTO(pageParam);
        return favoriteServiceObjMapper.toFavoriteListPageModel(botUserFavoritesDAO.favoritePageList(pageParamDTO));
    }
}