package com.fshows.knowledge.service.impl;

import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 向量存储工厂类
 * 根据不同的查询源返回对应的向量存储实例
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class EmbeddingStoreFactory {

    private final Map<Integer, EmbeddingStore<TextSegment>> embeddingStores = new ConcurrentHashMap<>();

    /**
     * 根据查询源获取对应的向量存储
     * 
     * @param querySource 查询源 (1: 司南app, 2: 浏览器插件)
     * @return 向量存储实例
     */
    public EmbeddingStore<TextSegment> getEmbeddingStore(Integer querySource) {
        return embeddingStores.computeIfAbsent(querySource, this::createEmbeddingStore);
    }

    /**
     * 创建向量存储实例
     * 
     * @param querySource 查询源
     * @return 向量存储实例
     */
    private EmbeddingStore<TextSegment> createEmbeddingStore(Integer querySource) {
        log.info("创建向量存储实例: querySource={}", querySource);
        
        // 这里暂时使用内存存储，实际项目中应该根据配置使用Milvus或Chroma
        // TODO: 根据配置选择不同的向量数据库
        switch (querySource) {
            case 1: // 司南app
                return createInMemoryStore("sinan_app");
            case 2: // 浏览器插件
                return createInMemoryStore("browser_plugin");
            default:
                log.warn("未知的查询源: {}, 使用默认存储", querySource);
                return createInMemoryStore("default");
        }
    }

    /**
     * 创建内存向量存储
     */
    private EmbeddingStore<TextSegment> createInMemoryStore(String storeName) {
        log.info("创建内存向量存储: {}", storeName);
        return new InMemoryEmbeddingStore<>();
    }


}