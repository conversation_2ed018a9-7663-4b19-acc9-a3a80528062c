/**
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.fshows.knowledge.service.common;

import com.aliyun.openservices.ons.api.Producer;
import org.apache.commons.lang3.builder.ReflectionToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <AUTHOR>
 * @version ProductBean.java, v 0.1 2022-03-10 5:18 下午 mayucong
 */
public class ProduceBean {

    /**
     * 生产者
     */
    private Producer producer;
    /**
     * Message Topic 主题
     */
    private String topicId;
    /**
     * Message Tag 标签
     */
    private String tag;
    /**
     * 代表消息关键业务的前缀
     */
    private String keyPrefix;

    /**
     * Getter method for property <tt>producer</tt>.
     *
     * @return property value of producer
     */
    public Producer getProducer() {
        return producer;
    }

    /**
     * Setter method for property <tt>producer</tt>.
     *
     * @param producer value to be assigned to property producer
     */
    public void setProducer(Producer producer) {
        this.producer = producer;
    }

    /**
     * Getter method for property <tt>topicId</tt>.
     *
     * @return property value of topicId
     */
    public String getTopicId() {
        return topicId;
    }

    /**
     * Setter method for property <tt>topicId</tt>.
     *
     * @param topicId value to be assigned to property topicId
     */
    public void setTopicId(String topicId) {
        this.topicId = topicId;
    }

    /**
     * Getter method for property <tt>tag</tt>.
     *
     * @return property value of tag
     */
    public String getTag() {
        return tag;
    }

    /**
     * Setter method for property <tt>tag</tt>.
     *
     * @param tag value to be assigned to property tag
     */
    public void setTag(String tag) {
        this.tag = tag;
    }

    /**
     * Getter method for property <tt>keyPrefix</tt>.
     *
     * @return property value of keyPrefix
     */
    public String getKeyPrefix() {
        return keyPrefix;
    }

    /**
     * Setter method for property <tt>keyPrefix</tt>.
     *
     * @param keyPrefix value to be assigned to property keyPrefix
     */
    public void setKeyPrefix(String keyPrefix) {
        this.keyPrefix = keyPrefix;
    }

    @Override
    public String toString() {
        return ReflectionToStringBuilder.reflectionToString(this,
                ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
