package com.fshows.knowledge.service.business.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.fshows.knowledge.dao.entity.BotArticleDO;
import com.fshows.knowledge.dao.entity.BotKnowledgeDO;
import com.fshows.knowledge.dao.repository.BotArticleDAO;
import com.fshows.knowledge.dao.repository.BotKnowledgeDAO;
import com.fshows.knowledge.service.business.YqService;
import com.fshows.knowledge.service.domain.param.YuQueWebHookParam;
import com.huike.nova.common.enums.ProcessStatusEnum;
import com.huike.nova.common.util.LogUtil;
import lombok.AllArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YqServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/20
 */
@Slf4j
@Service
@AllArgsConstructor
public class YqServiceImpl implements YqService {

    private BotArticleDAO botArticleDAO;
    private BotKnowledgeDAO botKnowledgeDAO;

    /**
     * 语雀文章创建、回调处理
     *
     * @param param 语雀回调参数
     */
    @Override
    public void handleYuQueWebhook(YuQueWebHookParam param) {
        YuQueWebHookParam.YuQueData data = param.getData();
        if (ObjectUtil.isNull(data)) {
            LogUtil.warn(log, "语雀回调参数为空");
            return;
        }
        // 查询该知识库下该文章是否存在
        BotArticleDO articleDO = botArticleDAO.getByKnowledgeIdAndArticleId(data.getBook().getId().toString(), data.getId().toString());
        if (ObjectUtil.isNotNull(articleDO)) {
            // 修改
            articleDO.setActionType(data.getActionType());
            articleDO.setArticleName(data.getTitle());
            articleDO.setProcessStatus(ProcessStatusEnum.DOWNLOAD_COMPLETE.getValue());
            botArticleDAO.updateData(articleDO);
            return;
        }
        // 新增
        articleDO = new BotArticleDO();
        articleDO.setBookId(data.getBook().getId().toString());
        articleDO.setArticleId(data.getId().toString());
        articleDO.setArticleName(data.getTitle());
        articleDO.setArticleUrl(data.getUrl());
        articleDO.setProcessStatus(ProcessStatusEnum.DOWNLOAD_COMPLETE.getValue());
        articleDO.setActionType(data.getActionType());
        botArticleDAO.save(articleDO);
    }

    /**
     * 下载文章
     *
     * @param bookId    知识库Id
     * @param articleId 文章Id
     */
    @Override
    public void bookArticleDownload(String bookId, String articleId) {
        List<BotArticleDO> list = botArticleDAO.findListByBookIdAndArticleId(bookId, articleId);
        if (CollectionUtil.isEmpty(list)) {
            return;
        }
        for (BotArticleDO articleDO : list) {
            BotKnowledgeDO knowledgeDO = botKnowledgeDAO.getByBookId(articleDO.getBookId());
            if (ObjectUtil.isNull(knowledgeDO)) {
                LogUtil.warn(log, "bookArticleDownload >>  知识库不存在 >> bookId={}", bookId);
            }
            // todo 下载文章
            // 更新状态和内部知识库的Id
            articleDO.setKnowledgeId(knowledgeDO.getKnowledgeId());
            articleDO.setProcessStatus(ProcessStatusEnum.DOWNLOAD_COMPLETE.getValue());
            botArticleDAO.updateData(articleDO);
        }
    }
}