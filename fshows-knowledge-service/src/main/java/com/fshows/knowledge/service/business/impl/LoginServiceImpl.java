package com.fshows.knowledge.service.business.impl;

import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.fshows.knowledge.dao.entity.BotUsersDO;
import com.fshows.knowledge.dao.repository.BotUsersDAO;
import com.fshows.knowledge.service.business.LoginService;
import com.fshows.knowledge.service.business.api.SinanService;
import com.fshows.knowledge.service.domain.model.CrmUserInfoModel;
import com.fshows.knowledge.service.domain.model.LoginModel;
import com.fshows.knowledge.service.domain.param.login.LoginParam;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.enums.LoginSourceEnum;
import com.huike.nova.common.exception.CommonException;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.MD5Util;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * LoginServiceImpl
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Slf4j
@Service
@AllArgsConstructor
public class LoginServiceImpl implements LoginService {

    private BotUsersDAO botUsersDAO;
    private SinanService sinanService;

    /**
     * 登录
     *
     * @param param 参数
     * @return 登录成功返回信息
     */
    @Override
    public LoginModel login(LoginParam param) {
        // 校验登录信息
        this.loginParamCheck(param);

        LoginSourceEnum loginSourceEnum = LoginSourceEnum.getByValue(param.getSource());
        // 登录
        BotUsersDO usersDO = switch (Objects.requireNonNull(loginSourceEnum)) {
            case SINAN ->
                // 司南token登录
                    this.sinanLogin(param);
            case CUSTOMER_SERVICE ->
                //  客服账密登录
                    this.customerServiceLogin(param);
            default -> throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("登录来源错误");
        };
        usersDO = botUsersDAO.getByUserName(usersDO.getUserName());
        // 退出原先登录态
        StpUtil.logout();
        // 登录成功，构建登录信息
        SaLoginModel loginModel = new SaLoginModel();
        // 登录态时间维护,可配置
        loginModel.setTimeout(CommonConstant.SECONDS_7_DAYS);
        loginModel.setDevice("knowledge");
        StpUtil.login(usersDO.getUserName(), loginModel);
        String accessToken = StpUtil.getTokenValue();

        // 返回登录信息
        LoginModel model = new LoginModel();
        model.setAccessToken(accessToken);
        model.setAssociationId(usersDO.getAssociationId());
        model.setUserType(usersDO.getUserType());
        model.setUserName(usersDO.getUserName());
        model.setUserId(usersDO.getId().toString());

        // sa-token存储登录信息
        StpUtil.getTokenSessionByToken(accessToken)
                .set(accessToken, JSON.toJSONString(model))
                .updateTimeout(CommonConstant.SECONDS_7_DAYS);

        LogUtil.info(log, "login >> 登录成功 >> accessToken={}, param={}", accessToken, JSON.toJSON(param));

        // 更新最后更新的时间
        botUsersDAO.updateUpdateTime(usersDO.getUserName());
        return model;
    }

    /**
     * 司南登录
     *
     * @param param 登录参数
     */
    private BotUsersDO sinanLogin(LoginParam param) {
        CrmUserInfoModel infoModel = sinanService.sinanTokenVerify(param.getToken(), param.getDeviceId());
        if (ObjectUtil.isNull(infoModel)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("司南token验证异常");
        }
        // 接口返回异常
        if (StringUtils.isNotBlank(infoModel.getErrorCode())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage(infoModel.getErrorMsg());
        }
        if (StringUtils.isBlank(infoModel.getMobile())) {
            LogUtil.warn(log, "sinanLogin >> 登录失败，请检查手机号是否正常 >> userInfo={}", JSON.toJSONString(infoModel));
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("请核实登录的账号是否正常");
        }
        BotUsersDO usersDO = botUsersDAO.getByUserName(infoModel.getMobile());
        if (ObjectUtil.isNull(usersDO)) {
            usersDO = new BotUsersDO();
            usersDO.setUserName(infoModel.getMobile());
            usersDO.setPassword(MD5Util.sign("FS88769879", CommonConstant.UTF8));
            usersDO.setUserType(LoginSourceEnum.SINAN.getValue());
            // 存司南对应代理商表tp_user.id
            usersDO.setAssociationId(infoModel.getSysUserId().toString());
            botUsersDAO.save(usersDO);
        }
        return usersDO;
    }

    public static void main(String[] args) {
        System.out.println(MD5Util.sign("FS88769879", CommonConstant.UTF8));
    }

    /**
     * 客服登录
     *
     * @param param 登录参数
     */
    private BotUsersDO customerServiceLogin(LoginParam param) {
        BotUsersDO usersDO = botUsersDAO.getByUserName(param.getUserName());
        if (ObjectUtil.isNull(usersDO)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("用户不存在");
        }
        if (!usersDO.getPassword().equals(param.getPassword())) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("密码错误");
        }
        return usersDO;
    }


    /**
     * 登录参数校验
     *
     * @param param 登录参数
     */
    private void loginParamCheck(LoginParam param) {
        if (LoginSourceEnum.SINAN.getValue().equals(param.getSource())) {
            if (StringUtils.isBlank(param.getToken()) || StringUtils.isBlank(param.getDeviceId())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("token和deviceId不能为空");
            }
        }
        if (LoginSourceEnum.CUSTOMER_SERVICE.getValue().equals(param.getSource())) {
            if (StringUtils.isBlank(param.getUserName()) || StringUtils.isBlank(param.getPassword())) {
                throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("用户名和密码不能为空");
            }
        }
    }
}