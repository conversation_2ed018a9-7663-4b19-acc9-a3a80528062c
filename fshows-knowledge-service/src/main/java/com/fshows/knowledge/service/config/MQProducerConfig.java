package com.fshows.knowledge.service.config;


import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.fshows.knowledge.service.message.IProducer;
import com.huike.nova.common.enums.ProduceEnum;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 消息生产者配置中心
 *
 * <AUTHOR>
 * @version MQProducerConfig.java, v 0.1 2022/3/10 4:36 下午 mayucong
 */
@Configuration
@AllArgsConstructor
public class MQProducerConfig {

    private MQConfig mqConfig;
    private Map<ProduceEnum, IProducer> produceBeanMap;

    /**
     * 获取生产者对象
     *
     * @param produceEnum
     * @return
     */
    public IProducer getProdcerBean(ProduceEnum produceEnum) {
        return produceBeanMap.get(produceEnum);
    }

    // /**
    //  * 导出中心
    //  *
    //  * @return
    //  */
    // @Bean(name = "exportCommonProducer", initMethod = "start", destroyMethod = "shutdown")
    // public IProducer exportCommonProducer() {
    //     return produceBeanMap.get(ProduceEnum.COMMON_EXPORT);
    // }


    /**
     * 创建生产者对象
     *
     * @param producerId
     * @return
     */
    private ProducerBean createProducerBean(String producerId) {
        ProducerBean producerBean = new ProducerBean();
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, mqConfig.getMqNamesrvAddress());
        properties.setProperty(PropertyKeyConst.ProducerId, producerId);
        properties.setProperty(PropertyKeyConst.AccessKey, mqConfig.getAccesskey());
        properties.setProperty(PropertyKeyConst.SecretKey, mqConfig.getSecretkey());
        // 设置发送超时时间，单位毫秒
        properties.setProperty(PropertyKeyConst.SendMsgTimeoutMillis, "10000");
        producerBean.setProperties(properties);
        return producerBean;
    }

    /**
     * 初始化
     */
    @PostConstruct
    private void initExportBeanMap() {
        produceBeanMap = new ConcurrentHashMap<>(6);
        // 通用导出
        // ProducerBean producerBean = createProducerBean(mqConfig.getExportCommonGid());
        // ProduceBean exportCommonBean = new ProduceBean();
        // exportCommonBean.setProducer(producerBean);
        // exportCommonBean.setTopicId(mqConfig.getCommonTopicId());
        // exportCommonBean.setTag(mqConfig.getExportCommonTag());
        // exportCommonBean.setKeyPrefix(mqConfig.getExportCommonPrefix());
        // produceBeanMap.put(ProduceEnum.COMMON_EXPORT, new ProducerImpl(exportCommonBean));
    }
}