package com.fshows.knowledge.service.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import feign.Client;
import feign.Logger;
import feign.RequestInterceptor;
import feign.Util;
import feign.codec.Decoder;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import okhttp3.ConnectionPool;
import org.springframework.context.annotation.Bean;

import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;

/**
 * Feign客户端
 * 不要加@Compoment参数
 *
 * <AUTHOR> (<EMAIL>)
 * @version FeignClientNovaConfig.java, v1.0 2024/12/2 18:20 John Exp$
 */
@Slf4j
public class FeignClientNovaConfig {
    @Bean
    public Client feignClient() {
        return new Client.Default(null, null);
    }

    @Bean
    public okhttp3.OkHttpClient okHttpClient() {
        return new okhttp3.OkHttpClient.Builder()
                .connectTimeout(5, TimeUnit.SECONDS)   // 连接超时
                .readTimeout(15, TimeUnit.SECONDS)      // 读取超时
                .writeTimeout(15, TimeUnit.SECONDS)     // 写入超时
                .connectionPool(new ConnectionPool(2, 5, TimeUnit.MINUTES)) // 连接池
                .build();
    }


    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.NONE;
    }

    @Bean
    public ErrorDecoder feignNovaErrorDecoder() {
        return (methodKey, response) -> {
            // 读取响应体为字符串
            CommonException exception = new CommonException(ErrorCodeEnum.NOVA_API_ERROR);
            if (response.status() != 200) {
                return exception.detailMessage("请求错误,请检查网络连接,错误码:{}", response.status()).subError(response.status(), "HTTP接口错误");
            }
            return new ErrorDecoder.Default().decode(methodKey, response);
        };
    }

    @Bean
    public RequestInterceptor feignLoggingInterceptor() {
        return template -> {
            // 打印请求信息
            System.out.println(" 【Feign Request】URL: " + template.url());
            System.out.println(" 【Feign Request】Headers: " + template.headers());
            // 响应日志需通过自定义Decoder或ErrorDecoder实现（见下文）
            // 使用响应缓存拦截器（推荐）
        };
    }

    @Bean
    public Decoder feignDecoder(ObjectMapper mapper) {
        return (response, type) -> {
            // 原始响应内容读取
            String body = Util.toString(response.body().asReader(StandardCharsets.UTF_8));
            System.out.println(" 【Feign Response】Body: " + body);
            // 继续使用默认Jackson解码
            return mapper.readValue(body,  mapper.constructType(type));
        };
    }
}
