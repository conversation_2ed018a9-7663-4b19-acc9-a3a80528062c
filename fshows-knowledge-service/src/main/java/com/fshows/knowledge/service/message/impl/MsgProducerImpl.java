package com.fshows.knowledge.service.message.impl;


import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.knowledge.service.config.MQProducerConfig;
import com.fshows.knowledge.service.message.IProducer;
import com.fshows.knowledge.service.message.MsgProducer;
import com.huike.nova.common.enums.ProduceEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version MsgProducerImpl.java, v 0.1 2022/3/10 4:06 下午 mayucong
 */
@Slf4j
@Component
@AllArgsConstructor
public class MsgProducerImpl implements MsgProducer {

    private MQProducerConfig mqProducerConfig;

    /**
     * 生产消息,
     *
     * @param produceEnum 生产者类型
     * @param key         消息key
     * @param msg         发送的消息
     * @return
     */
    @Override
    public boolean sendMessage(ProduceEnum produceEnum, String key, String msg) {
        IProducer bean = mqProducerConfig.getProdcerBean(produceEnum);
        if (bean == null) {
            LogUtil.warn(log, "缺少生产者配置信息 produceEnum ={},Msg ={}", produceEnum, msg);
            return false;
        }
        return bean.sendMessage(key, msg);
    }

    /**
     * 生产消息
     *
     * @param produceEnum 生产者类型
     * @param msg         发送的消息
     * @return
     */
    @Override
    public boolean sendMessage(ProduceEnum produceEnum, String msg) {
        IProducer bean = mqProducerConfig.getProdcerBean(produceEnum);
        if (bean == null) {
            LogUtil.warn(log, "缺少生产者配置信息 produceEnum ={},Msg ={}", produceEnum, msg);
            return false;
        }
        return bean.sendMessage(msg);
    }

    /**
     * 发送延迟消息
     *
     * @param produceEnum 生产者类型
     * @param msg         发送的消息
     * @param dliverTime  延时发送时间:3 * 60 * 1000 延时三分钟
     * @return
     */
    @Override
    public boolean sendDelayMessage(ProduceEnum produceEnum, String msg, int dliverTime) {
        IProducer bean = mqProducerConfig.getProdcerBean(produceEnum);
        if (bean == null) {
            LogUtil.warn(log, "sendDelayMessage >> 缺少生产者配置信息 >> produceEnum = {},Msg = {}", produceEnum, msg);
            return false;
        }
        return bean.sendDelayMessage(msg, dliverTime);
    }

    @Override
    public boolean sendDelayMessage(ProduceEnum produceEnum, String key, String body, int dliverTime) {
        IProducer bean = mqProducerConfig.getProdcerBean(produceEnum);
        if (bean == null) {
            LogUtil.warn(log, "缺少生产者配置信息 produceEnum ={},key ={},body={}", produceEnum, key, body);
            return false;
        }
        return bean.sendDelayMessage(key, body, dliverTime);
    }

    /**
     * 生产消息,push MQ失败默认重试一次
     *
     * @param produceEnum 生产者类型
     * @param key         消息key
     * @param msg         发送的消息
     * @return
     */
    @Override
    public boolean sendMessageRetry(ProduceEnum produceEnum, String key, String msg) {
        IProducer bean = mqProducerConfig.getProdcerBean(produceEnum);
        if (bean == null) {
            LogUtil.warn(log, "sendMessageRetry >> 缺少生产者配置信息 produceEnum ={},Msg ={},key={}", produceEnum, msg, key);
            return false;
        }
        return bean.sendMessageRetry(key, msg);
    }

    /**
     * 生产消息,push MQ失败默认重试一次
     *
     * @param produceEnum 生产者类型
     * @param msg         发送的消息
     * @return
     */
    @Override
    public boolean sendMessageRetry(ProduceEnum produceEnum, String msg) {
        IProducer bean = mqProducerConfig.getProdcerBean(produceEnum);
        if (bean == null) {
            LogUtil.warn(log, "sendMessageRetry >> 缺少生产者配置信息 produceEnum ={},Msg ={}", produceEnum, msg);
            return false;
        }
        return bean.sendMessageRetry(msg);
    }

    /**
     * 发送延迟消息,push MQ失败默认重试一次
     *
     * @param produceEnum 生产者类型
     * @param msg         发送的消息
     * @param dliverTime  延时发送时间:3 * 60 * 1000 延时三分钟
     * @return
     */
    @Override
    public boolean sendDelayMessageRetry(ProduceEnum produceEnum, String msg, int dliverTime) {
        IProducer bean = mqProducerConfig.getProdcerBean(produceEnum);
        if (bean == null) {
            LogUtil.warn(log, "sendDelayMessage >> 缺少生产者配置信息 >> produceEnum = {},Msg = {}", produceEnum, msg);
            return false;
        }
        return bean.sendDelayMessageRetry(msg, dliverTime);
    }

    /**
     * 发送延迟消息,push MQ失败默认重试一次
     *
     * @param produceEnum 生产者类型
     * @param key         key - 一般是订单号
     * @param body        发送的消息
     * @param dliverTime  延时发送时间:3 * 60 * 1000 延时三分钟
     * @return
     */
    @Override
    public boolean sendDelayMessageRetry(ProduceEnum produceEnum, String key, String body, int dliverTime) {
        IProducer bean = mqProducerConfig.getProdcerBean(produceEnum);
        if (bean == null) {
            LogUtil.warn(log, "sendDelayMessageRetry >> 缺少生产者配置信息 produceEnum ={},key ={},body={}", produceEnum, key, body);
            return false;
        }
        return bean.sendDelayMessageRetry(key, body, dliverTime);
    }

    /**
     * 发送延迟消息,push MQ失败默认重试一次
     *
     * @param produceEnum 生产者类型
     * @param msg         发送的消息
     * @param dliverTime  延时发送时间:3 * 60 * 1000 延时三分钟
     * @return
     */
    @Override
    public boolean sendDelayMessageRetry(ProduceEnum produceEnum, String msg, long dliverTime) {
        IProducer bean = mqProducerConfig.getProdcerBean(produceEnum);
        if (bean == null) {
            LogUtil.warn(log, "sendDelayMessage >> 缺少生产者配置信息 >> produceEnum = {},Msg = {}", produceEnum, msg);
            return false;
        }
        return bean.sendDelayMessageRetry(msg, dliverTime);
    }

}