package com.fshows.knowledge.service.domain.model;

import lombok.Data;

/**
 * TokenVerifyModel
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025/8/21
 */
@Data
public class TokenVerifyModel {
    /**
     * 错误代码
     * 9996: 登录过期，请重新登录
     */
    private String errorCode;

    /**
     * 错误消息
     * 描述具体的错误信息
     */
    private String errorMsg;

    /**
     * 请求是否成功
     * true: 成功, false: 失败
     */
    private boolean success;

    /**
     * JWT令牌
     * 用于身份验证和授权的token
     */
    private String token;

    /**
     * 手机号码
     * 用户注册的手机号
     */
    private String mobile;

    /**
     * 用户ID
     * 用户的唯一标识符
     */
    private String userId;

    /**
     * 系统用户ID
     * 在系统内部的用户编号
     */
    private int sysUserId;

    /**
     * 用户类型
     * 1: 管理员, 2: 普通用户, 3: 游客等
     * 具体类型需要根据业务定义确认
     */
    private int userType;
}