<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fshows.knowledge.dao.mapper.BotSourceDataMapper">
    <select id="findQuestionMatch" resultType="String">
        select
            title
        from
            bot_source_data
        where knowledge_id = #{knowledgeId,jdbcType=VARCHAR}
            and title like concat('%', #{content,jdbcType=VARCHAR}, '%')
            and is_del = 0
            limit #{limit,jdbcType=INTEGER}
    </select>
    <select id="findAnchorListBySourceDataId" resultType="java.lang.String">
        select anchor from bot_source_data where source_data_id = #{sourceDataId,jdbcType=VARCHAR} and is_del = 0
    </select>
</mapper>
