<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fshows.knowledge.dao.mapper.BotUserFavoritesMapper">
    <select id="favoritePageList" resultType="com.fshows.knowledge.dao.domain.result.FavoriteListPageResultDTO">
        select id,
            user_id,
            favorite_id,
            question,
            answer,
            message_id
        from
            bot_user_favorites
        where user_id= #{pageDTO.userId,jdbcType=VARCHAR}
            and is_del = 0
            order by create_time desc
    </select>
</mapper>
