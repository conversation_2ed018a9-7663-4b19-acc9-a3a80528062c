package com.fshows.knowledge.dao.repository;

import com.fshows.knowledge.dao.entity.BotChannelConfigDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 通道配置信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface BotChannelConfigDAO extends IService<BotChannelConfigDO> {

    /**
     * 根据来源查询配置信息
     *
     * @param source 来源
     * @return 结果
     */
    BotChannelConfigDO getBySource(Integer source);
}
