package com.fshows.knowledge.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 通道配置信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
@TableName("bot_channel_config")
public class BotChannelConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 1 代理商 2 客服
     */
    @TableField("source")
    private Integer source;

    /**
     * 指引文案
     */
    @TableField("guide_title")
    private String guideTitle;

    /**
     * 开场问候语
     */
    @TableField("opening_greeting")
    private String openingGreeting;

    /**
     * 答案开始文本
     */
    @TableField("answer_start_text")
    private String answerStartText;

    /**
     * 答案结束文本
     */
    @TableField("answer_end_text")
    private String answerEndText;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String SOURCE = "source";

    public static final String GUIDE_TITLE = "guide_title";

    public static final String OPENING_GREETING = "opening_greeting";

    public static final String ANSWER_START_TEXT = "answer_start_text";

    public static final String ANSWER_END_TEXT = "answer_end_text";

    public static final String IS_DEL = "is_del";

}
