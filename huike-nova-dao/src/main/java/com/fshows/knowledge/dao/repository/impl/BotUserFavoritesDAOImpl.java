package com.fshows.knowledge.dao.repository.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.knowledge.dao.domain.param.FavoriteListPageParamDTO;
import com.fshows.knowledge.dao.domain.result.FavoriteListPageResultDTO;
import com.fshows.knowledge.dao.entity.BotUserFavoritesDO;
import com.fshows.knowledge.dao.mapper.BotUserFavoritesMapper;
import com.fshows.knowledge.dao.repository.BotUserFavoritesDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.metadata.PageParam;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户收藏表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Service
public class BotUserFavoritesDAOImpl extends ServiceImpl<BotUserFavoritesMapper, BotUserFavoritesDO> implements BotUserFavoritesDAO {

    /**
     * 查询收藏明细
     *
     * @param favoriteId 收藏Id
     * @return 结果
     */
    @Override
    public BotUserFavoritesDO getByFavoriteId(String favoriteId) {
        return query().eq(BotUserFavoritesDO.FAVORITE_ID, favoriteId)
                .eq(BotUserFavoritesDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 根据消息id获取收藏明细
     *
     * @param messageId 消息Id
     * @return 结果
     */
    @Override
    public BotUserFavoritesDO getByMessageId(String messageId) {
        return query().eq(BotUserFavoritesDO.MESSAGE_ID, messageId)
                .eq(BotUserFavoritesDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 分页查询集团账号列表
     *
     * @param pageDTO 参数
     * @return 结果
     */
    @Override
    public Page<FavoriteListPageResultDTO> favoritePageList(PageParam<FavoriteListPageParamDTO> pageDTO) {
        Page<FavoriteListPageResultDTO> page = new Page<>();
        page.setCurrent(pageDTO.getPage());
        page.setSize(pageDTO.getPageSize());
        return getBaseMapper().favoritePageList(page, pageDTO.getQuery());
    }
}
