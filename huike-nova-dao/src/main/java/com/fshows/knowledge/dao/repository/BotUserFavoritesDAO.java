package com.fshows.knowledge.dao.repository;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.knowledge.dao.domain.param.FavoriteListPageParamDTO;
import com.fshows.knowledge.dao.domain.result.FavoriteListPageResultDTO;
import com.fshows.knowledge.dao.entity.BotUserFavoritesDO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.huike.nova.common.metadata.PageParam;

/**
 * <p>
 * 用户收藏表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface BotUserFavoritesDAO extends IService<BotUserFavoritesDO> {

    /**
     * 查询收藏明细
     *
     * @param favoriteId 收藏Id
     * @return 结果
     */
    BotUserFavoritesDO getByFavoriteId(String favoriteId);

    /**
     * 根据消息id获取收藏明细
     *
     * @param messageId 消息Id
     * @return 结果
     */
    BotUserFavoritesDO getByMessageId(String messageId);


    /**
     * 分页查询集团账号列表
     *
     * @param pageDTO 参数
     * @return 结果
     */
    Page<FavoriteListPageResultDTO> favoritePageList(PageParam<FavoriteListPageParamDTO> pageDTO);

}
