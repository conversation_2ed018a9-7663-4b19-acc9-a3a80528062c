package com.fshows.knowledge.dao.mapper;

import com.fshows.knowledge.dao.entity.BotSourceDataDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 元数据存储表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface BotSourceDataMapper extends BaseMapper<BotSourceDataDO> {
    /**
     * 匹配问题
     *
     * @param knowledgeId 所属知识库Id
     * @param content     内容
     * @param limit       记录数
     * @return 结果
     */
    List<String> findQuestionMatch(@Param("knowledgeId") String knowledgeId, @Param("content") String content, @Param("limit") Integer limit);

	/**
	 * 根据知语雀文章 id查询锚点列表
	 *
	 * @param sourceDataId 语雀文章 id
	 * @return 锚点列表
	 */
	List<String> findAnchorListBySourceDataId(@Param("sourceDataId") String sourceDataId);
}
