package com.fshows.knowledge.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fshows.knowledge.dao.domain.param.FavoriteListPageParamDTO;
import com.fshows.knowledge.dao.domain.result.FavoriteListPageResultDTO;
import com.fshows.knowledge.dao.entity.BotUserFavoritesDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 用户收藏表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface BotUserFavoritesMapper extends BaseMapper<BotUserFavoritesDO> {

    /**
     * 分页查询集团账号列表
     *
     * @param page     分页参数
     * @param pageDTO 查询参数
     * @return 结果
     */
    Page<FavoriteListPageResultDTO> favoritePageList(Page<FavoriteListPageResultDTO> page, @Param("pageDTO") FavoriteListPageParamDTO pageDTO);

}
