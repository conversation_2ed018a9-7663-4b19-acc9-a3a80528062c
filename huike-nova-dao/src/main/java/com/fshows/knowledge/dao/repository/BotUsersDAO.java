package com.fshows.knowledge.dao.repository;

import com.fshows.knowledge.dao.entity.BotUsersDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface BotUsersDAO extends IService<BotUsersDO> {

    /**
     * 根据用户名查询用户
     *
     * @param userName 用户名
     * @return 结果
     */
    BotUsersDO getByUserName(String userName);

    /**
     * 更新用户更新时间
     *
     * @param userName 用户名
     */
    void updateUpdateTime(String userName);
}
