package com.fshows.knowledge.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.knowledge.dao.entity.BotSourceDataDO;
import com.fshows.knowledge.dao.mapper.BotSourceDataMapper;
import com.fshows.knowledge.dao.repository.BotSourceDataDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 元数据存储表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Service
public class BotSourceDataDAOImpl extends ServiceImpl<BotSourceDataMapper, BotSourceDataDO> implements BotSourceDataDAO {

    /**
     * 匹配问题
     *
     * @param content 内容
     * @param limit   记录数
     * @return 结果
     */
    public List<String> findQuestionMatch(String knowledgeId, String content, Integer limit) {
        return baseMapper.findQuestionMatch(knowledgeId, content, limit);
    }

    /**
     * 根据语雀文章 id查询锚点列表
     *
     * @param sourceDataId 语雀文章 id
     * @return 锚点列表
     */
    @Override
    public List<String> findAnchorListBySourceDataId(String sourceDataId) {
        return getBaseMapper().findAnchorListBySourceDataId(sourceDataId);
    }

    /**
     * 根据语雀文章 id查询数据
     *
     * @param sourceDataId 知识库id
     * @return 锚点列表
     */
    @Override
    public List<BotSourceDataDO> findListBySourceDataId(String sourceDataId) {
        return query().eq(BotSourceDataDO.SOURCE_DATA_ID, sourceDataId)
                .eq(BotSourceDataDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .list();
    }

    /**
     * 根据锚点列表删除数据
     *
     * @param anchorList 锚点列表
     */
    @Override
    public void deleteBatchByAnchorList(List<String> anchorList) {
        update().set(BotSourceDataDO.IS_DEL, DelFlagEnum.DEL.getValue())
                .in(BotSourceDataDO.ANCHOR, anchorList)
                .update();
    }
}
