package com.fshows.knowledge.dao.repository.impl;

import com.fshows.knowledge.dao.entity.BotChannelConfigDO;
import com.fshows.knowledge.dao.mapper.BotChannelConfigMapper;
import com.fshows.knowledge.dao.repository.BotChannelConfigDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 通道配置信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Service
public class BotChannelConfigDAOImpl extends ServiceImpl<BotChannelConfigMapper, BotChannelConfigDO> implements BotChannelConfigDAO {

    /**
     * 根据来源查询配置信息
     *
     * @param source 来源
     * @return 结果
     */
    @Override
    public BotChannelConfigDO getBySource(Integer source) {
        return query().eq(BotChannelConfigDO.SOURCE, source)
                .eq(BotChannelConfigDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }
}
