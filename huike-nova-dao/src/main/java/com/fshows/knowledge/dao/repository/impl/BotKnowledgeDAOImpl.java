package com.fshows.knowledge.dao.repository.impl;

import com.fshows.knowledge.dao.entity.BotKnowledgeDO;
import com.fshows.knowledge.dao.mapper.BotKnowledgeMapper;
import com.fshows.knowledge.dao.repository.BotKnowledgeDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.DelFlagEnum;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 知识库 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Service
public class BotKnowledgeDAOImpl extends ServiceImpl<BotKnowledgeMapper, BotKnowledgeDO> implements BotKnowledgeDAO {

    /**
     * 查询知识库信息
     *
     * @param bookId 知识库Id
     * @return 知识库配置
     */
    @Override
    public BotKnowledgeDO getByBookId(String bookId) {
        return query().eq(BotKnowledgeDO.BOOK_ID, bookId)
                .eq(BotKnowledgeDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 根据来源查询
     *
     * @param source 来源
     * @return 结果
     */
    @Override
    public BotKnowledgeDO getBySource(Integer source) {
        return query().eq(BotKnowledgeDO.SOURCE, source)
                .eq(BotKnowledgeDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }
}
