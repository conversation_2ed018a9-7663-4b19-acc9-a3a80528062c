package com.fshows.knowledge.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 知识库文章表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
@TableName("bot_article")
public class BotArticleDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 知识库Id（内部）
     */
    @TableField("knowledge_id")
    private String knowledgeId;

    /**
     * 文章Id
     */
    @TableField("article_id")
    private String articleId;

    /**
     * 文件Url
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 处理状态0-初始化，1-下载成功；2-向量成功
     */
    @TableField("process_status")
    private Integer processStatus;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 语雀知识库 id
     */
    @TableField("book_id")
    private String bookId;

    /**
     * 语雀文章 url
     */
    @TableField("article_url")
    private String articleUrl;

    /**
     * 语雀文章名称
     */
    @TableField("article_name")
    private String articleName;

    /**
     * 操作类型
     */
    @TableField("action_type")
    private String actionType;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String KNOWLEDGE_ID = "knowledge_id";

    public static final String ARTICLE_ID = "article_id";

    public static final String FILE_URL = "file_url";

    public static final String PROCESS_STATUS = "process_status";

    public static final String IS_DEL = "is_del";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String BOOK_ID = "book_id";

    public static final String ARTICLE_URL = "article_url";

    public static final String ARTICLE_NAME = "article_name";

}
