package com.fshows.knowledge.dao.repository;

import com.fshows.knowledge.dao.entity.BotKnowledgeDO;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * 知识库 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface BotKnowledgeDAO extends IService<BotKnowledgeDO> {
    /**
     * 查询知识库信息
     *
     * @param bookId 知识库Id
     * @return 知识库配置
     */
    BotKnowledgeDO getByBookId(String bookId);

    /**
     * 根据来源查询
     *
     * @param source 来源
     * @return 结果
     */
    BotKnowledgeDO getBySource(Integer source);
}
