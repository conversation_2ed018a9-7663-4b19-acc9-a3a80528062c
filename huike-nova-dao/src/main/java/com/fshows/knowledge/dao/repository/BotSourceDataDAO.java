package com.fshows.knowledge.dao.repository;

import com.fshows.knowledge.dao.entity.BotSourceDataDO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 元数据存储表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface BotSourceDataDAO extends IService<BotSourceDataDO> {

    /**
     * 匹配问题
     *
     * @param knowledgeId 所属知识库
     * @param content     内容
     * @param limit       记录数
     * @return 结果
     */
    List<String> findQuestionMatch(String knowledgeId, String content, Integer limit);

	/**
	 * 根据语雀文章 id查询数据
	 *
	 * @param sourceDataId 知识库id
	 * @return 锚点列表
	 */
	List<String> findAnchorListBySourceDataId(String sourceDataId);

	/**
	 * 根据语雀文章 id查询数据
	 *
	 * @param sourceDataId 知识库id
	 * @return 锚点列表
	 */
	List<BotSourceDataDO> findListBySourceDataId(String sourceDataId);

	/**
	 * 根据锚点列表删除数据
	 *
	 * @param anchorList 锚点列表
	 */
	void deleteBatchByAnchorList(List<String> anchorList);
}
