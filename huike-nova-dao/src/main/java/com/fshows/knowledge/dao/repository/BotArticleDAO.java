package com.fshows.knowledge.dao.repository;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fshows.knowledge.dao.entity.BotArticleDO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 知识库文章表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
public interface BotArticleDAO extends IService<BotArticleDO> {

    /**
     * 根据文章查询文章
     *
     * @param bookId    知识库Id
     * @param articleId 文章id
     * @return 文章
     */
    BotArticleDO getByKnowledgeIdAndArticleId(String bookId, String articleId);


    /**
     * 更新文章
     *
     * @param botArticleDO 文章
     */
    void updateData(BotArticleDO botArticleDO);

    /**
     * 查询文章列表
     *
     * @param bookId    知识库Id
     * @param articleId 文章id
     * @return 文章
     */
    List<BotArticleDO> findListByBookIdAndArticleId(String bookId, String articleId);

    /**
     * 搜索文章（根据更新时间和处理状态）
     *
     * @param updateTime    更新时间
     * @param processStatus 处理状态0-初始化，1-下载成功；2-向量成功
     * @return 文章列表
     */
    List<BotArticleDO> findByUpdateTimeAndProcessStatus(Date updateTime, Integer processStatus);
}
