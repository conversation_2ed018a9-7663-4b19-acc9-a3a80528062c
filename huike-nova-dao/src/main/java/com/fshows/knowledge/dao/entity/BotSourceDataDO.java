package com.fshows.knowledge.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 元数据存储表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
@TableName("bot_source_data")
public class BotSourceDataDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 问题标题
     */
    @TableField("title")
    private String title;

    /**
     * 源数据 id （语雀文档 id）
     */
    @TableField("source_data_id")
    private String sourceDataId;

    /**
     * 源数据内容
     */
    @TableField("source_content")
    private String sourceContent;

    /**
     * 语雀标题下的锚点信息
     */
    @TableField("anchor")
    private String anchor;

    /**
     * 向量化后的片段 id（删除的唯一主键）
     */
    @TableField("fragment_id")
    private String fragmentId;

    /**
     * 知识库 id
     */
    @TableField("knowledge_id")
    private String knowledgeId;

    /**
     * 回答次数
     */
    @TableField("answer_count")
    private Integer answerCount;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String IS_DEL = "is_del";

    public static final String TITLE = "title";

    public static final String SOURCE_DATA_ID = "source_data_id";

    public static final String SOURCE_CONTENT = "source_content";

    public static final String ANCHOR = "anchor";

    public static final String FRAGMENT_ID = "fragment_id";

    public static final String KNOWLEDGE_ID = "knowledge_id";

    public static final String ANSWER_COUNT = "answer_count";

}
