package com.fshows.knowledge.dao.repository.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fshows.knowledge.dao.entity.BotArticleDO;
import com.fshows.knowledge.dao.mapper.BotArticleMapper;
import com.fshows.knowledge.dao.repository.BotArticleDAO;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.enums.ProcessStatusEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 知识库文章表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Service
public class BotArticleDAOImpl extends ServiceImpl<BotArticleMapper, BotArticleDO> implements BotArticleDAO {

    /**
     * 根据文章查询文章
     *
     * @param bookId    知识库Id
     * @param articleId 文章id
     * @return 文章
     */
    @Override
    public BotArticleDO getByKnowledgeIdAndArticleId(String bookId, String articleId) {
        return query().eq(BotArticleDO.BOOK_ID, bookId)
                .eq(BotArticleDO.ARTICLE_ID, articleId)
                .eq(BotArticleDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1").one();
    }

    /**
     * 更新文章
     *
     * @param botArticleDO 文章
     */
    @Override
    public void updateData(BotArticleDO botArticleDO) {
        botArticleDO.setUpdateTime(null);
        updateById(botArticleDO);
    }

    /**
     * 查询文章列表
     *
     * @param bookId    知识库Id
     * @param articleId 文章id
     * @return 文章
     */
    @Override
    public List<BotArticleDO> findListByBookIdAndArticleId(String bookId, String articleId) {
        return query().eq(StringUtils.isNotBlank(bookId), BotArticleDO.BOOK_ID, bookId)
                .eq(StringUtils.isNotBlank(articleId), BotArticleDO.ARTICLE_ID, articleId)
                .eq(BotArticleDO.PROCESS_STATUS, ProcessStatusEnum.INIT.getValue())
                .eq(BotArticleDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .list();
    }

    /**
     * 搜索文章（根据更新时间和处理状态）
     *
     * @param updateTime    更新时间
     * @param processStatus 处理状态0-初始化，1-下载成功；2-向量成功
     * @return 文章列表
     */
    @Override
    public List<BotArticleDO> findByUpdateTimeAndProcessStatus(Date updateTime, Integer processStatus) {
		return query().gt(BotArticleDO.UPDATE_TIME, updateTime)
                .eq(BotArticleDO.PROCESS_STATUS, processStatus)
                .eq(BotArticleDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .list();
    }
}
