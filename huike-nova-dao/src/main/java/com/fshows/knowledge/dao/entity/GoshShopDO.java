package com.fshows.knowledge.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 来逛呗-商铺表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-13
 */
@Data
@TableName("gosh_shop")
public class GoshShopDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 商铺id
     */
    @TableField("shop_id")
    private String shopId;

    /**
     * 商铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 广场id
     */
    @TableField("org_id")
    private String orgId;

    /**
     * 一级类目Id
     */
    @TableField("one_category_id")
    private String oneCategoryId;

    /**
     * 二级类目Id
     */
    @TableField("two_category_id")
    private String twoCategoryId;

    /**
     * 三级类目id
     */
    @TableField("three_category_id")
    private String threeCategoryId;

    /**
     * 详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 管理员名称
     */
    @TableField("admin_name")
    private String adminName;

    /**
     * 管理员手机号
     */
    @TableField("admin_phone")
    private String adminPhone;

    /**
     * 付呗uid
     */
    @TableField("user_id")
    private Integer userId;

    /**
     * 门店id
     */
    @TableField("store_id")
    private Integer storeId;

    /**
     * 确认状态  1未确认 2已确认
     */
    @TableField("confirm_status")
    private Integer confirmStatus;

    /**
     * 0正常 1删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 付呗侧account_id
     */
    @TableField("account_id")
    private String accountId;

    /**
     * 付呗侧开户审核状态 0默认 1审核驳回 2审核中 3审核通过;4-待激活
     */
    @TableField("apply_status")
    private Integer applyStatus;

    /**
     * 类目名称 拼接后的
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 集团ID
     */
    @TableField("bloc_id")
    private String blocId;

    /**
     * 失败原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 隐藏门店:0-正常；1-隐藏
     */
    @TableField("hide_status")
    private Integer hideStatus;

    /**
     * 自动提现:0-手动；1-自动
     */
    @TableField("auto_withdraw")
    private Integer autoWithdraw;

    /**
     * 结算状态：1-可结算；2-冻结结算
     */
    @TableField("settle_status")
    private Integer settleStatus;

    /**
     * 冻结解冻最后一次原因
     */
    @TableField("freeze_reason")
    private String freezeReason;

    /**
     * 广场下商铺poiId
     */
    @TableField("mall_poi_str")
    private String mallPoiStr;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String SHOP_ID = "shop_id";

    public static final String SHOP_NAME = "shop_name";

    public static final String ORG_ID = "org_id";

    public static final String ONE_CATEGORY_ID = "one_category_id";

    public static final String TWO_CATEGORY_ID = "two_category_id";

    public static final String THREE_CATEGORY_ID = "three_category_id";

    public static final String ADDRESS = "address";

    public static final String ADMIN_NAME = "admin_name";

    public static final String ADMIN_PHONE = "admin_phone";

    public static final String USER_ID = "user_id";

    public static final String STORE_ID = "store_id";

    public static final String CONFIRM_STATUS = "confirm_status";

    public static final String IS_DEL = "is_del";

    public static final String ACCOUNT_ID = "account_id";

    public static final String APPLY_STATUS = "apply_status";

    public static final String CATEGORY_NAME = "category_name";

    public static final String BLOC_ID = "bloc_id";

    public static final String REASON = "reason";

    public static final String HIDE_STATUS = "hide_status";

    public static final String AUTO_WITHDRAW = "auto_withdraw";

    public static final String SETTLE_STATUS = "settle_status";

    public static final String FREEZE_REASON = "freeze_reason";

    public static final String MALL_POI_STR = "mall_poi_str";

}
