package com.fshows.knowledge.dao.repository.impl;

import cn.hutool.core.date.DateUtil;
import com.fshows.knowledge.dao.entity.BotUsersDO;
import com.fshows.knowledge.dao.mapper.BotUsersMapper;
import com.fshows.knowledge.dao.repository.BotUsersDAO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huike.nova.common.enums.DelFlagEnum;
import com.huike.nova.common.util.FsDateUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Service
public class BotUsersDAOImpl extends ServiceImpl<BotUsersMapper, BotUsersDO> implements BotUsersDAO {

    /**
     * 根据用户名查询用户
     *
     * @param userName 用户名
     * @return 结果
     */
    @Override
    public BotUsersDO getByUserName(String userName) {
        return query().eq(BotUsersDO.USER_NAME, userName)
                .eq(BotUsersDO.IS_DEL, DelFlagEnum.NOT_DEL.getValue())
                .last("limit 1")
                .one();
    }

    /**
     * 更新用户更新时间
     *
     * @param userName 用户名
     */
    @Override
    public void updateUpdateTime(String userName) {
        update().set(BotUsersDO.UPDATE_TIME, DateUtil.date())
                .eq(BotUsersDO.USER_NAME, userName).update();
    }
}
