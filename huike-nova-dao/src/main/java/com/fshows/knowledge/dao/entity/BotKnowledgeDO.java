package com.fshows.knowledge.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 知识库
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-20
 */
@Data
@TableName("bot_knowledge")
public class BotKnowledgeDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 知识库名称（数据库层面）
     */
    @TableField("knowledge_name")
    private String knowledgeName;

    /**
     * 语雀知识库 id
     */
    @TableField("book_id")
    private Integer bookId;

    /**
     * 知识库名称（语雀层面）
     */
    @TableField("name")
    private String name;

    /**
     * 知识库 id （此表的关联 id）
     */
    @TableField("knowledge_id")
    private String knowledgeId;

    /**
     * 0 正常 1 删除
     */
    @TableField("is_del")
    @TableLogic
    private Integer isDel;

    /**
     * 1 代理商 2 客服
     */
    @TableField("source")
    private Integer source;

    /**
     * 语雀Cookie
     */
    @TableField("cookie")
    private String cookie;

    /**
     * 语雀x-csrf-token
     */
    @TableField("x_csrf_token")
    private String xCsrfToken;

    public Date getCreateTime() {
        if (this.createTime != null) {
          return new Date(this.createTime.getTime());
        } else {
          return null;
        }
    }

    public void setCreateTime(Date createTime) {
        if (createTime != null) {
            this.createTime = new Date(createTime.getTime());
        } else {
            this.createTime = null;
        }
    }
    public Date getUpdateTime() {
        if (this.updateTime != null) {
          return new Date(this.updateTime.getTime());
        } else {
          return null;
        }
    }

    public void setUpdateTime(Date updateTime) {
        if (updateTime != null) {
            this.updateTime = new Date(updateTime.getTime());
        } else {
            this.updateTime = null;
        }
    }


    public static final String ID = "id";

    public static final String CREATE_TIME = "create_time";

    public static final String UPDATE_TIME = "update_time";

    public static final String KNOWLEDGE_NAME = "knowledge_name";

    public static final String BOOK_ID = "book_id";

    public static final String NAME = "name";

    public static final String KNOWLEDGE_ID = "knowledge_id";

    public static final String IS_DEL = "is_del";

    public static final String SOURCE = "source";

    public static final String COOKIE = "cookie";

    public static final String X_CSRF_TOKEN = "x_csrf_token";

}
