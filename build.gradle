description = "根项目 build.gradle"
// 引入首展阿里云私库
apply from: 'scripts/rdc-repo.gradle'

// 编译脚本
buildscript {
    // 公共变量定义，依赖版本定义
    ext {
        springBootVersion = '2.7.2'
        kotlin_version = '1.8.21'
        redisson_version = '3.16.3'
        mybatis_plus_version = "*******"
        mybatis_plus_dynamic_datasource_version = "3.5.0"
        aliyun_ice_version = "1.1.2"
        aliyun_oss_version = "3.13.2"
        map_struct_version = "1.4.2.Final"
        aliyun_oss_version = "3.13.2"
        dysmsapi20170525_version = "2.0.6"
        easyexcel_version = "3.1.0"
        xxl_job_version = "2.0.1"
    }

    repositories {
        mavenLocal()
        // 阿里云官方Maven镜像
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url 'https://nexus-server.51fubei.com/nexus/content/groups/public/' }
        maven {
            url 'https://packages.aliyun.com/maven/repository/2095455-snapshot-vkcgv3/'
            credentials {
                username '61c41097d6190210d84fd51e'
                password 'KATf7WjH1Bee'
            }
        }
        mavenCentral()
    }
    dependencies {
        classpath "org.springframework.boot:spring-boot-gradle-plugin:$springBootVersion"
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:${kotlin_version}"
        // 加密库插件,在主项目中使用apply plugin: "com.byteowls.jasypt" 引入
        // ./gradlew encryptProperties --file-filter-pattern='application-((?!prod).*)\.properties' --password=encryptor_password
//        classpath "com.byteowls:jasypt-gradle-plugin:1.0.1"
        classpath "com.yimeibailing.gradle.plugin:ymbl-gradle-plugin:1.0.1-SNAPSHOT"
    }
}

// 所有项目
allprojects {
    if (project.name == "fshows-knowledge-web") {
        apply plugin: 'java'

    } else {
        apply plugin: 'java-library'
        apply plugin: 'maven-publish'
    }
    apply plugin: 'idea'
    // SpringBoot依赖管理插件
    apply plugin: 'io.spring.dependency-management'
//    apply plugin: 'com.byteowls.jasypt'

    group = 'com.fshows.knowledge'
    version = '1.0.0'

    // springboot依赖管理
    dependencyManagement {
        imports {
            mavenBom("org.springframework.boot:spring-boot-dependencies:$springBootVersion") {
                // 对于部分Kotlin的库，需要指定kotlin版本，springboot默认是1.3的kotlin
                bomProperty("kotlin.version", "${kotlin_version}")
            }
        }
    }

    configurations {
        compile.exclude module: 'spring-boot-starter-logging'
        compile.exclude module: "logback-classic"
        compile.exclude module: "log4j-over-slf4j"
        compile.exclude module: 'slf4j-log4j12'
        compile.exclude module: 'log4j'

        compileOnly {
            extendsFrom annotationProcessor
        }

        configureEach {
            // 排除SpringBoot自带的log，使用log4j2
            exclude group: 'org.springframework.boot', module: 'spring-boot-starter-logging'
        }
    }
}

// 子项目
subprojects {
    // 设置源码编译为Java8
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
    tasks.withType(JavaCompile) {
        options.encoding = 'UTF-8'
    }

    // 打包源码
    task packageSources(type: Jar) {
        classifier = 'sources'
        from sourceSets.main.allSource
    }

    artifacts.archives packageSources
    clean.delete += file("$projectDir/out")

    repositories {
        mavenLocal()
        maven { url "https://maven.aliyun.com/repository/public" }
        maven { url 'https://nexus-server.51fubei.com/nexus/content/groups/public/' }
    }

    dependencies {
        compileOnly 'org.projectlombok:lombok'
        annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
        annotationProcessor 'org.projectlombok:lombok'
        implementation "org.springframework.boot:spring-boot-starter:${springBootVersion}"
        implementation 'org.springframework.boot:spring-boot-starter-log4j2:2.6.6'
        implementation "org.redisson:redisson-spring-boot-starter:${redisson_version}"
        implementation "com.baomidou:mybatis-plus-boot-starter:${mybatis_plus_version}"
        implementation "com.baomidou:dynamic-datasource-spring-boot-starter:${mybatis_plus_dynamic_datasource_version}"
        implementation "com.purgeteam:dynamic-config-spring-boot-starter:0.1.1.RELEASE"

        implementation "com.aliyun:dysmsapi20170525:${dysmsapi20170525_version}"
        // 阿里云OSS
        implementation "com.aliyun.oss:aliyun-sdk-oss:${aliyun_oss_version}"
        // 云剪辑
        implementation "com.aliyun:ice20201109:${aliyun_ice_version}"

        // 加速编译
//        developmentOnly 'org.springframework.boot:spring-boot-devtools'

        implementation 'org.apache.commons:commons-lang3:3.4'
        implementation "com.google.guava:guava:31.1-jre"
        implementation 'com.alibaba:fastjson:1.2.83'
        implementation 'commons-codec:commons-codec:1.10'
        implementation 'com.annimon:stream:1.2.1'
        implementation "org.mapstruct:mapstruct:${map_struct_version}"
        annotationProcessor "org.mapstruct:mapstruct-processor:${map_struct_version}"

        // If you are using mapstruct in test code
        testAnnotationProcessor "org.mapstruct:mapstruct-processor:${map_struct_version}"
        // oss
        implementation "com.aliyun.oss:aliyun-sdk-oss:${aliyun_oss_version}"

        // log4j2异步记录辅助
        implementation 'com.lmax:disruptor:3.4.4'
        // 工具包
        implementation 'cn.hutool:hutool-all:5.7.13'
        //生成二维码工具包
        implementation 'com.google.zxing:javase:3.3.3'

        implementation 'javax.validation:validation-api:2.0.1.Final'
        implementation 'org.hibernate.validator:hibernate-validator:6.0.13.Final'
        testCompileOnly 'org.projectlombok:lombok'
        testAnnotationProcessor 'org.projectlombok:lombok'
        testImplementation 'junit:junit:4.12'
        testImplementation 'org.powermock:powermock-core:1.6.5'
        testImplementation 'org.powermock:powermock-api-mockito:1.6.5'
        testImplementation 'org.powermock:powermock-module-junit4:1.6.5'
        testImplementation 'org.mockito:mockito-all:1.10.19'
        testImplementation "org.springframework.boot:spring-boot-starter-test:$springBootVersion"
        // excel导出
        implementation "com.alibaba:easyexcel:${easyexcel_version}"
        implementation 'com.vdurmont:emoji-java:4.0.0'
        implementation 'org.aspectj:aspectjweaver:1.9.9'
//        implementation 'taobao-sdk-java:taobao-sdk-java:20221125'
        //xxl-job
        implementation "com.xuxueli:xxl-job-core:${xxl_job_version}"

        implementation 'org.apache.httpcomponents:httpcore:4.4.16'
        implementation 'org.apache.httpcomponents:httpclient:4.5.14'
        //nacos
        implementation 'com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config:2021.0.4.0'
        // 引入bootstrap
        implementation 'org.springframework.cloud:spring-cloud-starter-bootstrap:3.1.9'
        implementation 'org.springframework.boot:spring-boot-starter-websocket'
        // 引入sa-token
        // Sa-Token 权限认证，在线文档：https://sa-token.cc
        implementation 'cn.dev33:sa-token-spring-boot-starter:1.34.0'
        // Sa-Token 整合 Redis （使用 jackson 序列化方式）
        implementation 'cn.dev33:sa-token-dao-redis-jackson:1.34.0'
        implementation 'com.github.rholder:guava-retrying:2.0.0'
        implementation 'com.alipay.sdk:alipay-sdk-java:4.38.111.ALL'
        implementation 'com.belerweb:pinyin4j:2.5.0'
        implementation("com.aliyun:alibaba-dingtalk-service-sdk:2.0.0")
        implementation 'com.dingtalk.open:app-stream-client:1.1.0'
        implementation 'dev.langchain4j:langchain4j-easy-rag:1.3.0-beta9'
        implementation 'dev.langchain4j:langchain4j:1.3.0'
        implementation 'dev.langchain4j:langchain4j-open-ai:1.3.0'
        implementation 'dev.langchain4j:langchain4j-mcp:1.3.0-beta9'
        implementation 'dev.langchain4j:langchain4j-community-dashscope:1.3.0-beta9'
        implementation 'dev.langchain4j:langchain4j-chroma:1.3.0-beta9'
        implementation 'dev.langchain4j:langchain4j-embeddings-all-minilm-l6-v2:1.3.0-beta9'
        implementation 'dev.langchain4j:langchain4j-milvus:1.3.0-beta9'
    }
}

tasks.named('test') {
    useJUnitPlatform()
}