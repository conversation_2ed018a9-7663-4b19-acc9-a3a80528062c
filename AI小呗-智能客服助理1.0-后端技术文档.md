## 1. 修改历史
| 版本号 | 作者 | 时间 | 修改内容 |
| --- | --- | --- | --- |
| v1.0 | xxx | 2023-04-01 | 文档初始化 |
|  |  |  |  |


## 2. 概述
### 2.1 术语
> 如果在PRD文档中已经定义了某一个术语，建议在此处引用定义，以确保本文档的阅读者能够在一篇文档中就可以了解所有的关键术语。
>

| **术语** | **英文** | **释义** | **参考链接** |
| --- | --- | --- | --- |
|  |  |  |  |


### 2.2 背景
> 需求提出者（产品经理）需要提供项目要解决的问题，后续的规划和意义等。可以从PRD或者项目启动会议的文档中获取。
>

### 2.3 目标
> 本次迭代需要取得的项目和业务目标，项目目标包括上线时间，系统稳定性等，业务目标主要是项目组所承担的业务KPI
>

## 3. 系统架构与领域模型
### 3.1 架构分析
> 如果本次开发内容是项目的首次开发或者是大规模的重构，则需要重点在本小节描述整体的技术架构规划，包括业务架构、技术架构、应用架构、数据架构、物理架构等模块。
>
> 如果开发的内容是在原有系统上添加功能，需要引用原有系统的系统架构，帮助文档阅读者理解本次修改是否存在架构上的调整。
>

![](https://cdn.nlark.com/yuque/__puml/b8be0c2cf8eeb6eaa8fa4043cef4343b.svg)

#### 核心技术选型分析
+ **核心框架: LangChain4j**
    - **定位**: 作为“核心对话服务”的粘合剂，LangChain4j是整个RAG流程的大脑。它将文档加载、文本切分、向量化、信息检索和LLM调用等步骤串联起来，极大地简化了复杂AI应用的开发。
    - **优势**: 其模块化的设计允许灵活替换组件（如不同的向量数据库、LLM），为未来技术升级提供了便利。
+ **大语言模型: 千问大模型**
    - **定位**: 系统的“生成引擎”，负责理解检索到的上下文信息，并生成流畅、自然的回答。
    - **优势**: 作为国产大模型的优秀代表，千问在中文理解和生成方面具有天然优势，更符合国内业务场景的需求。选择API调用方式，可以降低本地部署的运维成本。
+ **向量数据库: Milvus**
    - **定位**: RAG系统的“记忆核心”，存储所有知识的向量表示。
    - **优势**: Milvus是一款开源、高性能的向量数据库，专为大规模向量相似度搜索设计。它的可扩展性和丰富的索引类型，能够保证在高并发场景下依然有出色的检索性能，是构建企业级RAG应用的理想选择。
+ **认证服务: Sa-Token **
    - **定位**: “统一认证服务”的实现框架，解决双入口的权限控制问题。
    - **优势**: 架构中明确了需要支持App内嵌的SSO（单点登录）和插件的独立登录。Sa-Token提供了成熟的解决方案，能够快速实现这两种模式，并提供踢人下线、会话管理等丰富功能，开发效率远高于纯手动实现JWT。

### 3.2 领域模型
> 系统模型的静态视图，表现各个业务模型直接的关系，关系包括，依赖，关联，组合，聚合，实现，泛化 等，可用类图表达，希望能以DDD的思想来表达业务的领域模型，这里需要交代清楚系统涉及到的领域模型的具体情况，除了关系之外，尽可能的表达领域的核心属性和事件。
>

## 4. 用例分析
### 4.1 业务用例分析
> 业务用例主要来自PRD中需要完成业务功能而分解出来的用例，比如我们常见的登录、注册、找回密码等，业务用例会有参与者，即使用我们的系统的用户，这里既可以是人，也有可能是系统。最终需要表述的就是参与者与用例的关系。
>

### 4.2 系统用例分析
> 系统用例主要是为了实现业务用例，我们设计出来的应用、服务、接口等内容。
>

### 4.3 边界分析
> 本小节主要描述上述系统用例之间的边界问题，简单可以理解为哪些接口应该放在哪些应用中，这些接口的上下游依赖关系是什么。
>

## 5. 业务变更影响
### 5.1 原业务影响分析
> 这里需要描述本次变更对原有业务的影响，重点是业务影响，而不是应用和系统的影响，比如费率调整需要由实时生效变更为次日生效，影响到的业务包括司南开户、费率获取等。
>

#### 5.1.1 影响业务1
#### 5.1.2 影响业务2
### 5.2 业务兼容性分析
> 本节分析接口的变更是否存在兼容性问题
>

### 5.3 RPC接口影响
> 本次变更的RPC接口的影响，需要梳理本次变更的接口的上下游调用关系，接口兼容问题。
>

### 5.4 HTTP接口影响
> 本次变更的HTTP接口的影响，需要梳理本次变更的接口的上下游调用关系，接口兼容问题。
>



## 6. 业务功能分析
### 6.1 权限控制
#### 6.1.1 司南权限查询接口
接口：/knowledge/role

业务逻辑

+ 使用Apollo 做三段式控制
    - **开启状态**：功能对所有用户开放
    - **关闭状态**：功能对所有用户禁用
    - **灰度状态**：仅白名单内用户可使用该功能
+ 代码实现在crmgw中
    - [http://app-api-test.51youdian.com:9033/lifecircle-new/lifecircle-crmgw](http://app-api-test.51youdian.com:9033/lifecircle-new/lifecircle-crmgw)
+ 入参
    - agentId  字符串    代理商Id
+ 出参
    - true/false



#### 6.1.2 根据司南token查询用户信息
接口：/knowledge/token/info?deviceId=****&token=****

+ 代码实现在crmgw中
    - [http://app-api-test.51youdian.com:9033/lifecircle-new/lifecircle-crmgw](http://app-api-test.51youdian.com:9033/lifecircle-new/lifecircle-crmgw)
+ 入参
    - 省略
+ 出参

```java
{
    "mobile": "18895369669",
    "userId": "4FC1D6803CF44D18B91386E60E2C3E23",
    "sysUserId": 2078380,
    "userType": 3
}
```



**系统架构**

![](https://cdn.nlark.com/yuque/__mermaid_v3/0dd6f0aac0426216f29c11f57c35fa4c.svg)

**功能发布流程**

![](https://cdn.nlark.com/yuque/__mermaid_v3/4a44ffdd2b27bcc6f4ae9024ffa7c549.svg)

### 6.2 文档向量部分
### 6.3 问答主流程
#### 6.3.1 钉钉 webhook
![](https://cdn.nlark.com/yuque/0/2025/png/21581979/1755064275668-ee6845b5-18c5-45b0-a91e-cf741455a2bf.png)

```java
{
	"data":{
		"body_html":"<!doctype html><div class=\"lake-content\" typography=\"classic\"><ul class=\"ne-ul\"><li id=\"u866b7873\" data-lake-index-type=\"0\"><span class=\"ne-text\">如何保证段落分割的准确性</span></li></ul><ul class=\"ne-list-wrap\"><ul ne-level=\"1\" class=\"ne-ul\"><li id=\"ud548d209\" data-lake-index-type=\"0\"><span class=\"ne-text\">按段落分割</span></li><li id=\"u0c78f364\" data-lake-index-type=\"0\"><span class=\"ne-text\">按行分割</span></li><li id=\"ue62d9e6b\" data-lake-index-type=\"0\"><span class=\"ne-text\">按句子分割</span></li><li id=\"ud9211a60\" data-lake-index-type=\"0\"><span class=\"ne-text\">按单词分割</span></li><li id=\"u9884c703\" data-lake-index-type=\"0\"><span class=\"ne-text\">按字符分割</span></li><li id=\"ued3c746e\" data-lake-index-type=\"0\"><span class=\"ne-text\">按正则表达式分割</span></li><li id=\"u44ab0750\" data-lake-index-type=\"0\"><span class=\"ne-text\"> 递归分割器</span></li><li id=\"ue91973dd\" data-lake-index-type=\"0\"><span class=\"ne-text\">Markdown标题层级的分割</span></li><li id=\"u80ab2af7\" data-lake-index-type=\"0\"><span class=\"ne-text\">大模型分割</span></li></ul></ul><ul class=\"ne-ul\"><li id=\"u712b6a59\" data-lake-index-type=\"0\"><span class=\"ne-text\">如何实现数据库隔离</span></li></ul><ul class=\"ne-list-wrap\"><ul ne-level=\"1\" class=\"ne-ul\"><li id=\"udc98e547\" data-lake-index-type=\"0\"><span class=\"ne-text\">不同业务数据隔离（收款单业务机器人要求读取不到扫码点餐的业务数据）</span></li><li id=\"u495ab3b8\" data-lake-index-type=\"0\"><span class=\"ne-text\">公开和非公开数据隔离 （要展示语雀元数据跳转定位，必须要知识库公开）</span></li></ul></ul><ul class=\"ne-ul\"><li id=\"ue6c00c70\" data-lake-index-type=\"0\"><span class=\"ne-text\">增量文档维护</span></li></ul><ul class=\"ne-list-wrap\"><ul ne-level=\"1\" class=\"ne-ul\"><li id=\"ucaa73a7f\" data-lake-index-type=\"0\"><a href=\"https://fshows.yuque.com/rqxish/rgu3dn/settings/webhooks\" data-href=\"https://fshows.yuque.com/rqxish/rgu3dn/settings/webhooks\" target=\"_blank\" class=\"ne-link\"><span class=\"ne-text\">https://fshows.yuque.com/rqxish/rgu3dn/settings/webhooks</span></a></li><li id=\"u1a67ca73\" data-lake-index-type=\"0\"><span class=\"ne-text\">原文档更新内容</span></li><li id=\"u9e77e661\" data-lake-index-type=\"0\"><span class=\"ne-text\">新增文档</span></li><li id=\"ufe81b4a5\" data-lake-index-type=\"0\"><span class=\"ne-text\">删除文档</span></li></ul></ul><ul class=\"ne-ul\"><li id=\"u0eb88878\" data-lake-index-type=\"0\"><span class=\"ne-text\">共创知识库文档的格式（要待开发阶段持续补充）</span></li></ul><ul class=\"ne-list-wrap\"><ul ne-level=\"1\" class=\"ne-ul\"><li id=\"ub9c6b605\" data-lake-index-type=\"0\"><span class=\"ne-text\">影响知识库问答的准确性</span></li><li id=\"ufc03c807\" data-lake-index-type=\"0\"><span class=\"ne-text\">影响前端页面展示效果</span></li><li id=\"u66013778\" data-lake-index-type=\"0\"><span class=\"ne-text\">影响问答回答速度（若无法规范格式，需在检索完知识库后对检索到的答案通过 LLM 在做一遍格式序列化）</span></li><li id=\"u988e4ad1\" data-lake-index-type=\"0\"><span class=\"ne-text\">文档内标签使用规范（会影响准确性和展示）</span></li></ul></ul><ul class=\"ne-ul\"><li id=\"ua5914296\" data-lake-index-type=\"0\"><span class=\"ne-text\">向量化速度问题</span></li></ul><ul class=\"ne-list-wrap\"><ul ne-level=\"1\" class=\"ne-ul\"><li id=\"ud22b90d6\" data-lake-index-type=\"0\"><span class=\"ne-text\">因当前观察下来通过分割器分割后的文档不满足要求，当前使用了 LLM 进行分割</span></li><li id=\"u9504d222\" data-lake-index-type=\"0\"><span class=\"ne-text\">优化此部分方案</span></li></ul></ul><ul class=\"ne-list-wrap\"><ul class=\"ne-list-wrap\"><ul ne-level=\"2\" class=\"ne-ul\"><li id=\"u3b8e4c94\" data-lake-index-type=\"0\"><span class=\"ne-text\">拓展分割器分割的办法   达到可用程度</span></li><li id=\"u1d81beab\" data-lake-index-type=\"0\"><span class=\"ne-text\">严格控制文档标题</span></li></ul></ul></ul><ul class=\"ne-ul\"><li id=\"u83296648\" data-lake-index-type=\"0\"><span class=\"ne-text\">token 消耗问题</span></li></ul><ul class=\"ne-list-wrap\"><ul ne-level=\"1\" class=\"ne-ul\"><li id=\"uf9173651\" data-lake-index-type=\"0\"><span class=\"ne-text\">增量文档更新消耗</span></li><li id=\"ud0a6d1f4\" data-lake-index-type=\"0\"><span class=\"ne-text\">LLM 分割消耗</span></li></ul></ul><ul class=\"ne-list-wrap\"><ul class=\"ne-list-wrap\"><ul ne-level=\"2\" class=\"ne-ul\"><li id=\"u30bebd3b\" data-lake-index-type=\"0\"><span class=\"ne-text\">当前使用 LLM 分割文档会比较大量的消耗 LLM 的 token 且速度较慢</span></li></ul></ul></ul><ul class=\"ne-ul\"><li id=\"u5a09b561\" data-lake-index-type=\"0\"><span class=\"ne-text\">会话 OR 上下文管理</span></li></ul><ul class=\"ne-list-wrap\"><ul ne-level=\"1\" class=\"ne-ul\"><li id=\"u5b31d37c\" data-lake-index-type=\"0\"><span class=\"ne-text\"></span></li></ul></ul></div>",
		"content_updated_at":"2025-08-13T06:05:28.000Z",
		"action_type":"update",
		"book":{
			"content_updated_at":"2025-08-13T06:05:27.775Z",
			"_serializer":"v2.book",
			"description":"项目",
			"created_at":"2025-06-18T02:21:33.000Z",
			"type":"Book",
			"likes_count":0,
			"public":2,
			"items_count":18,
			"updated_at":"2025-08-13T06:05:28.000Z",
			"user_id":33142665,
			"name":"项目",
			"creator_id":21581979,
			"id":66207456,
			"slug":"rgu3dn",
			"watches_count":0
		},
		"_serializer":"webhook.doc_detail",
		"created_at":"2025-08-04T01:08:47.000Z",
		"title":"开发实现需要考虑的问题",
		"body":"+ 如何保证段落分割的准确性\n    - 按段落分割\n    - 按行分割\n    - 按句子分割\n    - 按单词分割\n    - 按字符分割\n    - 按正则表达式分割\n    -  递归分割器\n    - Markdown标题层级的分割\n    - 大模型分割\n+ 如何实现数据库隔离\n    - 不同业务数据隔离（收款单业务机器人要求读取不到扫码点餐的业务数据）\n    - 公开和非公开数据隔离 （要展示语雀元数据跳转定位，必须要知识库公开）\n+ 增量文档维护\n    - [https://fshows.yuque.com/rqxish/rgu3dn/settings/webhooks](https://fshows.yuque.com/rqxish/rgu3dn/settings/webhooks)\n    - 原文档更新内容\n    - 新增文档\n    - 删除文档\n+ 共创知识库文档的格式（要待开发阶段持续补充）\n    - 影响知识库问答的准确性\n    - 影响前端页面展示效果\n    - 影响问答回答速度（若无法规范格式，需在检索完知识库后对检索到的答案通过 LLM 在做一遍格式序列化）\n    - 文档内标签使用规范（会影响准确性和展示）\n+ 向量化速度问题\n    - 因当前观察下来通过分割器分割后的文档不满足要求，当前使用了 LLM 进行分割\n    - 优化此部分方案\n        * 拓展分割器分割的办法   达到可用程度\n        * 严格控制文档标题\n+ token 消耗问题\n    - 增量文档更新消耗\n    - LLM 分割消耗\n        * 当前使用 LLM 分割文档会比较大量的消耗 LLM 的 token 且速度较慢\n+ 会话 OR 上下文管理\n    - \n\n",
		"path":"rqxish/rgu3dn/zy3r0r3d2zg30t7d",
		"word_count":391,
		"updated_at":"2025-08-13T06:05:28.000Z",
		"id":230864101,
		"published_at":"2025-08-13T06:05:28.000Z",
		"slug":"zy3r0r3d2zg30t7d",
		"body_draft":"",
		"format":"lake",
		"book_id":66207456,
		"url":"https://fshows.yuque.com/rqxish/rgu3dn/zy3r0r3d2zg30t7d",
		"tags":[],
		"likes_count":0,
		"actor":{
			"work_id":"",
			"books_count":5,
			"_serializer":"v2.user",
			"description":"",
			"created_at":"2021-05-10T06:05:37.000Z",
			"type":"User",
			"login":"yangxuerui",
			"public_books_count":0,
			"avatar_url":"https://static-legacy.dingtalk.com/media/lADPDgQ9rh7eACXNAZDNAZA_400_400.jpg",
			"public":1,
			"updated_at":"2025-06-20T16:29:15.000Z",
			"following_count":0,
			"followers_count":0,
			"organization_id":0,
			"name":"杨学瑞",
			"id":21581979
		},
		"user_id":21581979,
		"comments_count":0,
		"publish":false,
		"webhook_subject_type":"update",
		"first_published_at":"2025-08-04T02:01:14.203Z",
		"actor_id":21581979,
		"user":{
			"work_id":"",
			"books_count":5,
			"_serializer":"v2.user",
			"description":"由空间内个人数据迁移生成的团队",
			"created_at":"2022-09-26T14:27:19.000Z",
			"type":"Group",
			"login":"rqxish",
			"public_books_count":2,
			"avatar_url":"https://gw.alipayobjects.com/zos/rmsportal/SZXCSCJTuhRSmAggBlVp.png",
			"public":2,
			"updated_at":"2025-06-18T02:21:33.000Z",
			"following_count":0,
			"followers_count":1,
			"organization_id":1795,
			"name":"杨学瑞的个人团队",
			"id":33142665
		}
	}
}
```

业务逻辑

+ 新增 and 修改
    - 接收到webhook 消息后
        * 根据消息中的 book_id查询knowledge_base  (知识库表)
        * 不存在则终止
    - 根据webhook消息中id字段去调用文档下载
    - 根据下载好的文档去调用文档分割
    - 根据分割好的文档去调用文档向量
+ 删除
    - 根据文档 id 删除source_data数据和向量数据

```plsql
update source_data set is_del= 1 where source_data_id = ？ and is_del = 0
```



#### 6.3.1 下载文档
```java
curl 'https://fshows.yuque.com/api/docs/230864101/export' \
-H 'Accept: application/json' \
-H 'Accept-Language: zh-CN,zh;q=0.9' \
-H 'Connection: keep-alive' \
-H 'Content-Type: application/json' \
-b 'lang=zh-cn; _yuque_session=wEBXOAr4uWYdSZnbD3UlbmzlyqbO3Wv7FpZCGkGcoBZxc-xr8j4VIcGi4tcATQhAw4LGzSScXDdawN-wS60REA==; tfstk=g76naAVtcFgbgAa96O9QgRpF6dVtdp9WOaHJyLLz_F8sJ2Hd48kP2ifpay9dENb15wId2TfsEis5pk98ApsBFLzYk-BlAM97jc_cN9DZ_3sZLb8z6dJk7WeLk-eAY1yOZyUAeHZ7Be-yUHRyYCrMmHJrUX7y7C-2D2JPzaJNbnKqzUleaFkw53JyzaJzjG86qL8r1_owJa7CQPFXC54RLDXwxBYVYejdROyHTXsMSYkPIgRHuTYiUYWMxgjo0NMUnF5da9pNLPuXQi1O8hv0E0xGiGJexawjdpSVb_RhE7nXP1jNMCfsvktGKiXkmCuaxU6d-TAC3rH681X1EpCT-xLWa1BBG9UZKK5fvpCP7ukyr1-P4VcZgPNxFhrRQbGWThtMkTBtbptK9u7YjlcVGB-6Y-EgjbwyThtM4lqiiAAefHVA.; receive-cookie-deprecation=1; _tea_utm_cache_10000007=undefined; aliyungf_tc=8231b7fb21ee5f74948166f9280e2699e4ca3377ac18704fae088bd7ccc937e1; yuque_ctoken=69mlfQIbviYUXYCkV3B6FZEz; current_theme=default; acw_tc=ac11000117550671069185187e6b1d8916826b3d259813a29f058a76d05669' \
-H 'Origin: https://fshows.yuque.com' \
-H 'Referer: https://fshows.yuque.com/rqxish/rgu3dn/zy3r0r3d2zg30t7d' \
-H 'Sec-Fetch-Dest: empty' \
-H 'Sec-Fetch-Mode: cors' \
-H 'Sec-Fetch-Site: same-origin' \
-H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36' \
-H 'X-Requested-With: XMLHttpRequest' \
-H 'sec-ch-ua: "Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'sec-ch-ua-platform: "macOS"' \
-H 'x-csrf-token: 69mlfQIbviYUXYCkV3B6FZEz' \
-H 'x-login: yangxuerui' \
--data-raw '{"type":"markdown","force":0,"options":"{\"latexType\":1,\"enableAnchor\":1}"}'
```

业务逻辑

+ 使用 rpa 方式下载语雀文档
+ 参数文档 id 在 webhook 中获取
+ 请求头的Cookie和x-csrf-token在knowledge_base中获取
+ 增加钉钉告警
+ 入参
    - <font style="color:#DF2A3F;">enableAnchor 影响是否有锚点信息</font>

```json
{
    "type": "markdown",
    "force": 0,
    "options": "{\"latexType\":1,\"enableAnchor\":1}"
}
```

#### 6.3.1 分割
业务逻辑

+ 拿到下载后的文档后

```java
// 读取下载后的文档
Document document = FileSystemDocumentLoader.loadDocument("/Users/<USER>/Documents/rag/本地生活FAQ.md");            // 预编译正则表达式（匹配一个或多个 #）
// 定义正则
Pattern pattern = Pattern.compile("(?<!#)###(?!#)");
// 文档内容
String text = document.text();
// 使用正则表达式分割字符串
String[] parts = pattern.split(text);
```

#### 6.3.1 向量
业务逻辑

+ 拿到分割后的内容
+ 拿到标签中的锚点信息
    - <h3 id="wIyLm">4、发起分账，这个手机号码，是获取的哪里</h3>

```java
// 向量库
EmbeddingStore<TextSegment> embeddingStore = ChromaEmbeddingStore
.builder()
.baseUrl(chromaEndpoint)
.collectionName("rag_knowledge_base")
.logRequests(true)
.logResponses(true)
.build();

// 元数据 （来源自webhook 推送中数据）
Metadata metadata = new Metadata();
// 文档名称
metadata.put("source", "余额分账FAQ");
// 文档原路径
metadata.put("yuque_url", "https://fshows.yuque.com/tech-ozd0u/szv77s/sscgeggetsqpni8c");
// 带锚点的路径
metadata.put("yuque_url_anchor", "https://fshows.yuque.com/tech-ozd0u/szv77s/sscgeggetsqpni8c#tW3zP");
// 锚点信息
metadata.put("anchor", "#tW3zP");



// 利用向量模型进行向量化， 然后存储向量到向量数据库
// 嵌入的原始内容。
TextSegment segment = TextSegment.from("片段内容", metadata);
// 要添加到存储区的嵌入。
Embedding embedding = embeddingModel.embed(segment).content();
// 添加到向量库
embeddingStore.add(embedding, segment);
```

+ 构建source_data表数据
    - 要兼容同文档数据修改，不清除回答数量的需求
    - 先查询当前文章下的source_data数据
    - 获取新的分段数据锚点信息
        * 锚点信息一致，认定为同一条数据
        * 新分段匹配不到旧分段
            + 新增
        * 旧分段匹配不到新分段
            + 删除旧分段

```plsql
CREATE TABLE `source_data` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '0 正常 1 删除',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '问题标题',
  `source_data_id` varchar(32) NOT NULL DEFAULT '' COMMENT '源数据 id （语雀文档 id）',
  `source_content` longtext NOT NULL COMMENT '源数据内容',
  `anchor` varchar(32) NOT NULL DEFAULT '' COMMENT '语雀标题下的锚点信息',
  `fragment_id` varchar(32) NOT NULL DEFAULT '' COMMENT '向量化后的片段 id（删除的唯一主键）',
  `knowledge_base_id` varchar(32) NOT NULL DEFAULT '' COMMENT '知识库 id',
  PRIMARY KEY (`id`),
  KEY `idx_createtime` (`create_time`),
  KEY `idx_title` (`title`),
  KEY `idx_source_data_id` (`source_data_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='元数据存储表';
```

#### 6.3.1 查询
```java
/**
 * 查询请求DTO
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryRequest {

    /**
     * 查询内容
     */
    private String query;

    /**
     * 最大结果数，默认5
     */
    private int maxResults = 5;

    /**
     * 最小相似度分数，默认0.7
     */
    private double minScore = 0.7;

    /**
     * 请求来源（不同数据来源要查询不同的向量库） 1 司南 app 2 浏览器插件 
     */
    private int querySource;

}
```

```java
package com.fshows.langchain4jexamples.agent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问答接口响应类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatResponse {

    /**
	 * 请求是否成功
	 */
    private boolean success;

    /**
	 * 错误或状态消息 (例如，在 success=false 时显示 "网络繁忙，请稍后重试")
	 */
    private String message;

    /**
	 * 用户输入的问题
	 */
    private String query;

    /**
	 * 答案
	 */
    private String answer;

    /**
	 * 消息 id 本次提问的唯一标识
	 */
    private String messageId;

    /**
     * 答案开始文本
     */
    private String answerStartText;

    /**
     * 答案结束文本
     */
    private String answerEndText;

    /**
	 * AI回答所引用的附件/资料来源列表
	 */
    private List<MatchResult> matchResultList;

    /*猜你想问*/


    @Data
    public static class MatchResult {
        /**
		 * 引用的资料内容
		 */
        private String text;
        /**
		 * 相似度分数
		 */
        private double score;
        /**
		 * 语雀文档原地址 url
		 */
        private String yuQueUrl;
        /**
		 * 语雀文档名称
		 */
        private String source;
        /**
		 * 包含锚点信息的语雀文档地址 url
		 */
        private String yuQueUrlAnchor;
    }
}
```

业务逻辑

+ 根据入参querySource判断查询那个向量库

```plsql
select * from user_knowledge_base where source = querySource and is_del= 0
```

    - 不存在报错
+ 动态获取知识库（防止频繁创建 bean）

```java
/**
     * 缓存不同知识库的 EmbeddingStore
     * Key: collectionName, Value: EmbeddingStore
     */
private final Map<String, EmbeddingStore<TextSegment>> embeddingStoreCache = new ConcurrentHashMap<>();

/**
     * 动态获取或创建指定知识库的 EmbeddingStore
     * @param targetCollectionName 目标知识库名称
     * @return EmbeddingStore 实例
     */
private EmbeddingStore<TextSegment> getEmbeddingStore(String targetCollectionName) {
// 如果缓存中已存在，直接返回
if (embeddingStoreCache.containsKey(targetCollectionName)) {
    log.info("RagAgent.getEmbeddingStore >> 从缓存获取知识库 >> collectionName: {}", targetCollectionName);
    return embeddingStoreCache.get(targetCollectionName);
}

// 缓存中不存在，创建新的 EmbeddingStore
log.info("RagAgent.getEmbeddingStore >> 创建新的知识库连接 >> collectionName: {}", targetCollectionName);
try {
    EmbeddingStore<TextSegment> newEmbeddingStore = ChromaEmbeddingStore.builder()
    .baseUrl(chromaEndpoint)
    .collectionName(targetCollectionName)
    .logRequests(true)
    .logResponses(true)
    .build();

    // 添加到缓存
    embeddingStoreCache.put(targetCollectionName, newEmbeddingStore);
    log.info("RagAgent.getEmbeddingStore >> 知识库连接创建成功并已缓存 >> collectionName: {}", targetCollectionName);

    return newEmbeddingStore;
} catch (Exception e) {
    log.error("RagAgent.getEmbeddingStore >> 创建知识库连接失败 >> collectionName: {}, error: {}", targetCollectionName, e.getMessage());
    throw new RuntimeException("创建知识库连接失败: " + e.getMessage(), e);
}
}
```

+ 查询向量库

```java
// 获取指定知识库的 EmbeddingStore
EmbeddingStore<TextSegment> targetEmbeddingStore = getEmbeddingStore(targetCollectionName);

// 将查询向量化
Embedding queryEmbedding = embeddingModel.embed(query).content();

// 构建搜索请求
EmbeddingSearchRequest searchRequest = EmbeddingSearchRequest.builder()
.queryEmbedding(queryEmbedding)
.maxResults(maxResults)
.minScore(minScore)
.build();

// 执行搜索
List<EmbeddingMatch<TextSegment>> matches = targetEmbeddingStore.search(searchRequest).matches();
```

+ 根据搜索内容构建返参结果
+ 增加回答次数（后面可以做真正的热门问题排序）
+ 答案开始文本和答案结束文本取值channel_config表

### 6.4 文档追踪 
```java
public static void main(String[] args) {
    String chromaEndpoint = "http://localhost:8000";
    String aliAiKey = System.getenv("ALI_AI_KEY");
    EmbeddingStore<TextSegment> embeddingStore = ChromaEmbeddingStore
    .builder()
    .baseUrl(chromaEndpoint)
    .collectionName("rag_knowledge_base")
    .logRequests(true)
    .logResponses(true)
    .build();


    QwenEmbeddingModel embeddingModel = QwenEmbeddingModel.builder()
    .apiKey(aliAiKey)
    .modelName("text-embedding-v4")
    .dimension(1024)
    .build();

    // 元数据 （来源自webhook 推送中数据）
    Metadata metadata = new Metadata();
    metadata.put("source", "余额分账FAQ");
    metadata.put("yuque_url", "https://fshows.yuque.com/tech-ozd0u/szv77s/sscgeggetsqpni8c#tW3zP");
    metadata.put("anchor", "#tW3zP");



    // 利用向量模型进行向量化， 然后存储向量到向量数据库
    // 嵌入的原始内容。
    TextSegment segment = TextSegment.from("""
                                           来团呗oem代理商手机号修改
                                           1. 业务提供老手机号A、新手机号B
                                           2. 确定新手机号B，在系统中不存在，在ailike_b_oem_user查询验证，若存在不允许订正
                                           SELECT * FROM `ailike_b_oem_user` WHERE `phone_number` = 'kLErL6sFJl865ZAgbzxoMQ==';
                                           3. 老手机号在ailike_b_oem_user表，查询user_id，在ailike_b_oem_identity确定数据；
                                           a. 若单条，直接完成下面订正
                                           b. 若多条，通过belong_id确定要修改的代理商
                                           UPDATE `ailike_b_oem_agent` SET `phone_number` = '', `phone_number_encrypt_ext` = '' WHERE `id` = ;
                                           UPDATE `ailike_b_oem_user` SET `phone_number` = '', `phone_number_encrypt_ext` = '' WHERE `id` = ;
                                           """, metadata);
    // 要添加到存储区的嵌入。
    Embedding embedding = embeddingModel.embed(segment).content();
    // 添加到向量库
    embeddingStore.add(embedding, segment);

    // 需要查询的内容 向量化
    Embedding queryEmbedding = embeddingModel.embed("团小贝用不了").content();

    // 去向量数据库查询
    // 构建查询条件
    EmbeddingSearchRequest build = EmbeddingSearchRequest.builder()
    .queryEmbedding(queryEmbedding)
    .maxResults(10)
    .minScore(0.6)
    .build();


    // 查询
    EmbeddingSearchResult<TextSegment> segmentEmbeddingSearchResult = embeddingStore.search(build);

    segmentEmbeddingSearchResult.matches().forEach(embeddingMatch -> {
        String source = embeddingMatch.embedded().metadata().getString("source");
        String yuque_url = embeddingMatch.embedded().metadata().getString("yuque_url");
                                                   String anchor = embeddingMatch.embedded().metadata().getString("anchor");
                                                   System.out.println("来源元数据文档名称" + source);
                                                   System.out.println("来源元数据文档地址" + yuque_url);
                                                   System.out.println("来源元数据文档中问题标题锚点" + anchor);
                                                   });


                                                   }
```

#### 6.4.1 锚点信息向量库存储
业务逻辑

+ 锚点信息和片段拓展信息存储到Metadata（元数据）中
+ 存储字段（信息来源为webhook 推送中数据）
    - yuque_url 语雀文档原地址 url
    - source 对应文档名称
    - yuque_url_anchor 包含锚点信息的语雀文档地址

```java
// 元数据 （来源自webhook 推送中数据）
Metadata metadata = new Metadata();
metadata.put("source", "余额分账FAQ");
metadata.put("yuque_url", "https://fshows.yuque.com/tech-ozd0u/szv77s/sscgeggetsqpni8c#tW3zP");
metadata.put("anchor", "#tW3zP");



// 利用向量模型进行向量化， 然后存储向量到向量数据库
// 嵌入的原始内容。
TextSegment segment = TextSegment.from("""
                                       来团呗oem代理商手机号修改
                                       1. 业务提供老手机号A、新手机号B
                                       2. 确定新手机号B，在系统中不存在，在ailike_b_oem_user查询验证，若存在不允许订正
                                       SELECT * FROM `ailike_b_oem_user` WHERE `phone_number` = 'kLErL6sFJl865ZAgbzxoMQ==';
                                       3. 老手机号在ailike_b_oem_user表，查询user_id，在ailike_b_oem_identity确定数据；
                                       a. 若单条，直接完成下面订正
                                       b. 若多条，通过belong_id确定要修改的代理商
                                       UPDATE `ailike_b_oem_agent` SET `phone_number` = '', `phone_number_encrypt_ext` = '' WHERE `id` = ;
                                       UPDATE `ailike_b_oem_user` SET `phone_number` = '', `phone_number_encrypt_ext` = '' WHERE `id` = ;
                                       """, metadata);
// 要添加到存储区的嵌入。
Embedding embedding = embeddingModel.embed(segment).content();
// 添加到向量库
embeddingStore.add(embedding, segment);
```

#### 6.4.1 语雀锚点跳转
```java
package com.fshows.langchain4jexamples.agent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 问答接口响应类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatResponse {

	/**
	 * 请求是否成功
	 */
	private boolean success;

	/**
	 * 错误或状态消息 (例如，在 success=false 时显示 "网络繁忙，请稍后重试")
	 */
	private String message;

	/**
	 * 用户输入的问题
	 */
	private String query;

	/**
	 * 答案
	 */
	private String answer;

    /**
	 * 消息 id 本次提问的唯一标识
	 */
	private String messageId;

	/**
	 * “猜您还想问”的问题列表
	 */
	private List<String> relatedQuestionList;

	/**
	 * AI回答所引用的附件/资料来源列表
	 */
	private List<MatchResult> matchResultList;


	@Data
	public static class MatchResult {
		/**
		 * 引用的资料内容
		 */
		private String text;
		/**
		 * 相似度分数
		 */
		private double score;
		/**
		 * 语雀文档原地址 url
		 */
		private String yuQueUrl;
		/**
		 * 语雀文档名称
		 */
		private String source;
		/**
		 * 包含锚点信息的语雀文档地址 url
		 */
		private String yuQueUrlAnchor;
	}
}
```

业务逻辑

+ 问答中获取元数据信息

```java
// 执行搜索
List<EmbeddingMatch<TextSegment>> matches = embeddingStore.search("你是谁").matches();
// 循环结果
for (EmbeddingMatch<TextSegment> match : matches) {
    // 获取源数据
    String source = match.embedded().metadata().getString("source");
    String yuQueUrl = match.embedded().metadata().getString("yuque_url");
    String yuQueUrlAnchor = match.embedded().metadata().getString("yuque_url_anchor");
}
```

### 6.5 我的收藏
#### 6.5.1 收藏
业务逻辑

+ 入参
    - userId 用户 id
    - answer 收藏内容
    - question 用户提问问题
    - message_id 消息 id
+ 落用户收藏表

```plsql
insert into user_favorites
```

#### 6.5.1 取消收藏
业务逻辑

+ 入参
    - userId 用户 id
    - message_id 消息 id
+ 根据用户 id 和消息 id 删除收藏

```plsql
update user_favorites set is_del = 1 where user_id = ？and message_id = ？and is_del = 0
```

#### 6.5.1 收藏列表
业务逻辑

+ 入参
    - userId 用户 id
    - content 搜索内容
+ 根据入参用户 id 分页查询收藏列表

```plsql
select * from user_favorites where user_id = ? and is_del = 0  and answer like %?%
```

#### ~~6.5.1 收藏消息 id 列表（app 存储的话需要）~~
~~业务逻辑~~

+ ~~入参~~
    - ~~userId 用户 id~~
+ ~~根据入参用户 id 获取收藏消息 id 列表~~

```plsql
select message_id from user_favorites where user_id = ？ and is_del= 0 
```



### 6.6 标题匹配
#### 6.6.1 标题匹配
业务逻辑

+ 入参
    - content 搜索内容

```plsql
select title from source_data where title like %?% and is_del = 0 and 知识库 id=  ？
```

### 6.7 用户模块
#### 6.7.1 司南登录态查询
业务逻辑

+ crmgw 中获取当前登录信息   
+ 返回当前账号信息

#### 6.7.2 司南 token 换智能客服账号 token 登录
业务逻辑

+ 入参
    - source 1 代理商 2 客服
+ 调用司南登录态查询
    - 无登录态直接报错
+ 使用返参登录
    - 无知识库信息
        * 报错
    - 根据司南代理商 id 和user_type = 1查询users表

```plsql
select * from users where user_type = 1 and association_id = ? and is_del = 0
```

    - 有账号直接登录
    - 无账号创建账号  然后登录
        * 根据 source = 1 查询知识库表

```plsql
select * from knowledge_base where source =1 and is_del= 0
```

        * 构建user 表
        * 账号默认密码 FS88769879

#### 6.7.3 账号密码登录
业务逻辑

+ 入参
    - user_name 账号
    - password 密码
    - user_type 用户类型  1 代理商 2 客服
+ 出参
    - accessToken  授权token
    - userId 用户 id
    - user_name 用户名
    - user_type 1 代理商 2 客服
    - association_id  关联 id   司南为 代理商 id
+ 登录
    - 根据 source = 2 查询识库表

```plsql
select * from  knowledge_base where source = 2 and is_del= 0
```

    - 无知识库信息
        * 报错
    - 根据
    - 根据账号查询用户表

```plsql
select * from users where user_type = 2 and user_name = ? and is_del = 0
```

    - 无账号
        * 报错
    - 有账号
        * 校验密码

### 6.8  猜你想问
通过分词+TF-IDF + 余弦相似度算法实现猜你想问功能。

猜你想问返回 20 条数据  点击换一换前端随机即可

```java
public class TFIDFRecommendationServiceImpl implements TFIDFRecommendationService {

    // 知识库中的问题列表
    private List<String> questions = new ArrayList<>();

    // 每个问题的TF-IDF向量
    private Map<String, Map<String, Double>> tfidfVectors = new HashMap<>();

    // 所有词的IDF值
    private Map<String, Double> idfMap = new HashMap<>();

    /**
     * 构造函数，初始化问题知识库
     *
     * @param questions 初始化的问题列表
     */
    public TFIDFRecommendationServiceImpl(List<String> questions) {
        if (questions != null && !questions.isEmpty()) {
            this.questions.addAll(questions);
            // 预计算所有问题的TF-IDF向量
            computeAllTFIDFVectors();
        }
    }




    public String recommend(String query) {
        if (query == null || query.trim().isEmpty()) {
            return null;
        }

        List<String> recommendations = recommend(query, 1);
        return recommendations.isEmpty() ? null : recommendations.get(0);
    }


    public List<String> recommend(String query, int limit) {
        if (query == null || query.trim().isEmpty() || limit <= 0 || questions.isEmpty()) {
            return Collections.emptyList();
        }

        // 计算查询语句的TF-IDF向量
        Map<String, Double> queryVector = computeTFIDFVector(query);

        // 计算查询向量与知识库中每个问题向量的相似度
        List<QuestionSimilarity> similarities = new ArrayList<>();
        for (String question : questions) {
            double similarity = cosineSimilarity(queryVector, tfidfVectors.get(question));
            similarities.add(new QuestionSimilarity(question, similarity));
        }

        // 按相似度降序排序，并返回前limit个结果
        return similarities.stream()
                .sorted((qs1, qs2) -> Double.compare(qs2.similarity, qs1.similarity))
                .limit(limit)
                .map(qs -> qs.question)
                .collect(Collectors.toList());
    }

     public void addQuestion(String question) {
        if (question == null || question.trim().isEmpty() || questions.contains(question)) {
            return;
        }

        questions.add(question);
        // 重新计算IDF和TF-IDF向量
        computeAllTFIDFVectors();
    }

     public void addQuestions(List<String> questions) {
        if (questions == null || questions.isEmpty()) {
            return;
        }

        boolean added = false;
        for (String question : questions) {
            if (question != null && !question.trim().isEmpty() && !this.questions.contains(question)) {
                this.questions.add(question);
                added = true;
            }
        }

        // 只有在添加了新问题时才重新计算
        if (added) {
            computeAllTFIDFVectors();
        }
    }

    public List<String> getAllQuestions() {
        return new ArrayList<>(questions);
    }

    /**
     * 使用jieba分词器对文本进行分词
     *
     * @param text 待分词的文本
     * @return 分词结果列表
     */
    private List<String> segmentWithJieba(String text) {
        JiebaSegmenter segmenter = new JiebaSegmenter();
        List<SegToken> tokens = segmenter.process(text, JiebaSegmenter.SegMode.INDEX);
        List<String> result = new ArrayList<>();
        for (SegToken token : tokens) {
            String word = token.word.trim();
            // 过滤掉空字符串和长度为1的词
            if (!word.isEmpty() && word.length() > 1) {
                result.add(word);
            }
        }
        return result;
    }

    /**
     * 计算所有问题的TF-IDF向量
     */
    private void computeAllTFIDFVectors() {
        // 先计算IDF
        computeIDF();

        // 再计算每个问题的TF-IDF向量
        tfidfVectors.clear();
        for (String question : questions) {
            tfidfVectors.put(question, computeTFIDFVector(question));
        }
    }

    /**
     * 计算所有词的IDF值
     */
    private void computeIDF() {
        idfMap.clear();

        // 统计每个词出现在多少个文档中
        Map<String, Integer> docFreq = new HashMap<>();
        for (String question : questions) {
            List<String> tokens = segmentWithJieba(question);
            // 使用Set去重，确保每个词在同一个文档中只计算一次
            tokens.stream().distinct().forEach(token ->
                    docFreq.put(token, docFreq.getOrDefault(token, 0) + 1)
            );
        }

        // 计算IDF值，公式：log(总文档数 / 包含该词的文档数)
        int totalDocs = questions.size();
        docFreq.forEach((term, freq) ->
                idfMap.put(term, Math.log((double) totalDocs / freq))
        );
    }

    /**
     * 计算单个问题的TF-IDF向量
     *
     * @param question 问题文本
     * @return TF-IDF向量
     */
    private Map<String, Double> computeTFIDFVector(String question) {
        List<String> tokens = segmentWithJieba(question);

        // 计算词频(TF)
        Map<String, Integer> termFreq = new HashMap<>();
        for (String token : tokens) {
            termFreq.put(token, termFreq.getOrDefault(token, 0) + 1);
        }

        // 计算TF-IDF向量
        Map<String, Double> tfidfVector = new HashMap<>();
        int totalTerms = tokens.size();
        termFreq.forEach((term, freq) -> {
            // TF = 词频 / 总词数
            double tf = (double) freq / totalTerms;
            // TF-IDF = TF * IDF
            double idf = idfMap.getOrDefault(term, 0.0);
            tfidfVector.put(term, tf * idf);
        });

        return tfidfVector;
    }

    /**
     * 计算两个向量的余弦相似度
     *
     * @param vec1 向量1
     * @param vec2 向量2
     * @return 余弦相似度值
     */
    private double cosineSimilarity(Map<String, Double> vec1, Map<String, Double> vec2) {
        // 获取两个向量的所有维度
        Map<String, Double> allTerms = new HashMap<>();
        allTerms.putAll(vec1);
        allTerms.putAll(vec2);

        // 计算点积
        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (String term : allTerms.keySet()) {
            double v1 = vec1.getOrDefault(term, 0.0);
            double v2 = vec2.getOrDefault(term, 0.0);

            dotProduct += v1 * v2;
            norm1 += v1 * v1;
            norm2 += v2 * v2;
        }

        // 避免除以零
        if (norm1 == 0 || norm2 == 0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 用于存储问题和相似度的内部类
     */
    private static class QuestionSimilarity {
        String question;
        double similarity;

        QuestionSimilarity(String question, double similarity) {
            this.question = question;
            this.similarity = similarity;
        }
    }

    /**
     * 默认构造函数
     */
    public TFIDFRecommendationServiceImpl() {
        // 初始化一些默认问题
        List<String> defaultQuestions = new ArrayList<>();
        defaultQuestions.add("如何修改密码");
        defaultQuestions.add("吃苹果的好处");
        defaultQuestions.add("关机可以省电码");
        defaultQuestions.add("修炼童子神功");
        defaultQuestions.add("修改电话");
        defaultQuestions.add("支付成功后可以打印小票");
        defaultQuestions.add("如何修改个人信息");
        defaultQuestions.add("如何联系客服");
        defaultQuestions.add("如何申请退款");
        defaultQuestions.add("如何查看订单");
        defaultQuestions.add("如何取消订单");
        defaultQuestions.add("支付失败怎么办");
        defaultQuestions.add("如何修改收货地址");
        defaultQuestions.add("如何查看物流信息");

        this.questions.addAll(defaultQuestions);
        computeAllTFIDFVectors();
    }

    public static void main(String[] args) {
        // 创建"猜你想问"服务实例
        TFIDFRecommendationServiceImpl service = new TFIDFRecommendationServiceImpl();

        // 用户输入查询
        String userQuery = "如何取消订单";

        // 获取推荐结果
        List<String> recommendations = service.recommend(userQuery, 5);

        System.out.println("用户查询: " + userQuery);
        System.out.println("猜你还想问:");
        for (int i = 0; i < recommendations.size(); i++) {
            System.out.println((i + 1) + ". " + recommendations.get(i));
        }
    }
}

```



### 6.9  推荐问题
#### 6.9.1 推荐问题列表
首先要维护一批推荐问题数据（本期存储在nacos，各端 100 条？）

业务逻辑

+ 根据不同端获取对应的 nacos  返回随机的 20 条？
+ 换一换（前端随机 list 即可）
+ 开场标题文案 channel_config的guide_title
+ 开场问候取 channel_config的opening_greeting

### 6.10  上下文
等功能完善后在考虑上下文做还是不做

要综合评估对问答的影响点

正确性

询问耗时

token 消耗

等



### 最终版本模块
+ 权限控制（三段式开关） （海哥）
    - 根据司南 token 查询当前登录信息 3
    - 校验当前用户是否有权限 3
+ 问答主流程  学瑞
    - webhook 接收（考虑实时还是定时） 2
    - 文档下载  3
    - 分割  8
    - 向量 4
        * 元数据
        * 向量数据
        * 维护 mysql 数据
    - 问答 4
+ 我的收藏
    - 收藏 3
    - 取消收藏 3
+ 标题匹配
    - 标题匹配接口 3
+ 用户模块
    - 客服（账密登录）5
    - 司南（司南 token 换机器人 token登录） 5
+ 推荐问题列表（开场）
    - 根据登录信息查询开场信息接口（区分知识库）4



+ 下期内容
    - 猜你想问
    - 上下文



+ 开发相关
    - 项目初始化   
    - 接入 langchain4j 基本能力（llm 等）
    - 向量库选择 and 部署接入







1. 司南访问   知识库 1

2 插件访问   知识库 2

3 代理商后台  知识库 1



1 账号设置知识库的权限  知识库 1，知识库 2

2 账号是单知识库权限的   知识库 1



 





## 7 数据模型
>  数据库表结构的设计情况，主要是数据字典和数据E-R模型的展现，还有是否设计到分库分表，分块分表的拆分数量和拆分键的设计说明
>

```plsql
CREATE TABLE `user_favorites` (
	`id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
	`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '修改时间',
	`user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '用户 id',
	`favorite_id` varchar(32) NOT NULL DEFAULT '' COMMENT '收藏 id',
	`question` varchar(3000) NOT NULL DEFAULT '' COMMENT '用户提问问题',
	`answer` longtext NOT NULL COMMENT '收藏的AI回答内容',
	`message_id` varchar(32) NOT NULL DEFAULT '' COMMENT '消息 id',
	`is_del` tinyint NOT NULL DEFAULT 0 COMMENT '0 正常 1 删除',
	PRIMARY KEY (`id`),
	KEY `idx_createtime` (`create_time`),
	KEY `idx_user_id` (`user_id`),
	KEY `idx_favorite_id` (`favorite_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='用户收藏表';

CREATE TABLE `source_data` (
	`id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
	`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '修改时间',
	`is_del` tinyint NOT NULL DEFAULT 0 COMMENT '0 正常 1 删除',
	`title` varchar(50) NOT NULL DEFAULT '' COMMENT '问题标题',
	`source_data_id` varchar(32) NOT NULL DEFAULT '' COMMENT '源数据 id （语雀文档 id）',
	`source_content` longtext NOT NULL COMMENT '源数据内容',
	`anchor` varchar(32) NOT NULL DEFAULT '' COMMENT '语雀标题下的锚点信息',
	`fragment_id` varchar(32) NOT NULL DEFAULT '' COMMENT '向量化后的片段 id（删除的唯一主键）',
	`knowledge_base_id` varchar(32) NOT NULL DEFAULT '' COMMENT '知识库 id',
	`answer_count` int NOT NULL DEFAULT 0 COMMENT '回答次数',
	PRIMARY KEY (`id`),
	KEY `idx_createtime` (`create_time`),
	KEY `idx_title` (`title`),
	KEY `idx_source_data_id` (`source_data_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='元数据存储表';

CREATE TABLE `users` (
	`id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
	`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '修改时间',
	`user_name` varchar(32) NOT NULL DEFAULT '' COMMENT '用户名',
	`password` varchar(255) NOT NULL COMMENT '密码（MD5）',
	`user_type` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '1 代理商 2 客服（只打标使用）',
	`is_del` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '0 正常 1 删除',
	`association_id` varchar(32) NOT NULL DEFAULT '' COMMENT '关联 id   司南为 代理商 id',
	`knowledge_base_id` varchar(32) NOT NULL DEFAULT '' COMMENT '知识库 id',
	PRIMARY KEY (`id`),
	KEY `idx_createtime` (`create_time`),
	KEY `idx_user_name` (`user_name`),
	KEY `idx_association_id` (`association_id`),
	KEY `idx_knowledge_base_id` (`knowledge_base_id`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='用户表';

CREATE TABLE `user_knowledge_base` (
	`id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
	`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '修改时间',
	`knowledge_base` varchar(32) NOT NULL DEFAULT '' COMMENT '知识库名称',
	`user_id` varchar(32) NOT NULL DEFAULT '' COMMENT '用户 id',
	`knowledge_base_id` varchar(32) NOT NULL DEFAULT '' COMMENT '知识库 id',
	`is_del` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '0 正常 1 删除',
	`source` tinyint NOT NULL DEFAULT 0 COMMENT '1 代理商 2 客服',
	`book_id` int NOT NULL DEFAULT 0 COMMENT '语雀知识库 id',
	PRIMARY KEY (`id`),
	KEY `idx_createtime` (`create_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='用户知识库关联表';

CREATE TABLE `knowledge_base` (
	`id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
	`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '修改时间',
	`knowledge_base` varchar(32) NOT NULL DEFAULT '' COMMENT '知识库名称（数据库层面）',
	`book_id` int NOT NULL DEFAULT 0 COMMENT '语雀知识库 id',
	`name` varchar(50) NOT NULL DEFAULT '' COMMENT '知识库名称（语雀层面）',
	`knowledge_base_id` varchar(32) NOT NULL DEFAULT '' COMMENT '知识库 id （此表的关联 id）',
	`is_del` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '0 正常 1 删除',
	`source` tinyint unsigned NOT NULL DEFAULT 0 COMMENT '1 代理商 2 客服',
	`cookie` varchar(2000) NOT NULL DEFAULT '' COMMENT '语雀Cookie',
	`x-csrf-token` varchar(255) NOT NULL DEFAULT '' COMMENT '语雀x-csrf-token',
	PRIMARY KEY (`id`),
	KEY `idx_createtime` (`create_time`)
) DEFAULT CHARACTER SET=utf8mb4 COMMENT='知识库';


```

## 8. 风险分析
### 8.1 宕机风险
> 此处重点对系统稳定性进行分析，比如流量高峰、外部接口异常等情况下对业务的影响和响应措施，具体落地为系统、业务数据监控和报警相关的分析，同时还需要分析系统报警之后的应急处理方案。
>



### 8.2 资损风险
> 如果业务涉及到资金，则需要在本处分析潜在的资损风险以及监控措施，同时还需要分析收到报警之后的止血方案。
>



### 8.3 其他风险
> 除以上风险之外可能存在的其他风险
>



## 9. 测试关注点 
> 需要描述测试过程中需要重点关注的内容，主要涉及到核心系统变更、慢SQL、资金业务、并发问题等。
>

## 10. 灰度方案
> 对于业务影响较大的变更，需要分析系统的功能开关和灰度方案。
>

### 10.1 司南 |付呗APP 灰度方案
### 10.2 XXX小程序灰度方案


## 11. 研发成员
| **角色** | **人员** |
| --- | --- |
| <font style="color:rgb(6, 17, 120);">技术经理</font> |  |
| <font style="color:rgb(0, 58, 140);">产品</font> |  |
| <font style="color:rgb(0, 58, 140);">开发</font> |  |
| <font style="color:rgb(0, 58, 140);">测试</font> |  |


## 12. 接口文档
> 描述各个环境下接口的公共基础信息，包括URL，加签验签方式等，文档详细内容复制[https://fshows.yuque.com/tech-ozd0u/bgaw8p/wcyk0d](https://fshows.yuque.com/tech-ozd0u/bgaw8p/wcyk0d#fpcX) 链接中的模板，并外链到本章节下
>



## 13. 发布计划
> 描述除了常规发布流程外特殊的关注点
>

## 14. 过程审查
> 保证技术分析过程质量，分析全面无遗漏
>
> 审查状态：<font style="background:#DBF1B7;color:#2A4200">通过</font><font style="background:#F8CED3;color:#70000D">不通过</font><font style="background:#EFF0F0;color:#262626">不涉及</font><font style="background:#C0DDFC;color:#00346B">待审核</font>
>

| **编号** | **过程审查问题** | **审查结果** | **审查备注** |
| --- | --- | --- | --- |
| 2.1.1 | 技术分析过程中如果出现需求变更，产品文档与技术文档是否及时更新完毕？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.1.2 | 技术方案是否已在技术会前进行过预审？（视情况而定） | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.1.3 | 必要的业务相关方是否参加？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.1.4 | 是否存在外部借调来的资源？且借调资源是否参加了技术会？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.2.1 | 前/后端技术方案是否进行了全链路的系统性分析？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.2.2 | 功能迭代时，老业务相关的重要文档链接是否补充进去？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.3.1 | <font style="color:#262626;">是否涉及老业务的改造？</font> | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.3.2 | 老业务改造是否设计灰度方案？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.3.3 | 灰度方案是否完整？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.3.4 | 是否需要设计回滚方案？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.3.5 | 回滚方案设计是否完整？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.3.6 | 是否涉及已有表的结构变更？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.3.7 | 表结构变更会不会影响到老业务？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.3.8 | 是否有考虑到新老版本差异，并设计完善的规避方案？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.4.1 | 前端的技术方案也要在技术会上同步？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.4.2 | 前端难点实现方案是否设计完善与合理？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.4.3 | <font style="color:#262626;">前端是否梳理了本次要新增的组件、修改的组件，且修改组件后对应的影响点？</font> | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.4.4 | 前端多人合作的项目，公共模块、公共组件、业务模块划分是否清晰，分工是否明确？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.5.1 | 有没有做整体风险分析与评估？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.5.2 | 各个模块有没有做单独的风险分析与评估？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.6.1 | 是否存在老接口的改动？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.7.2 | 会议纪要需要以评论形式记录于技术方案下方 | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |
| 2.8.1 | 是否需要重开技术会？ | <font style="background:#C0DDFC;color:#00346B">待审核</font> |  |






