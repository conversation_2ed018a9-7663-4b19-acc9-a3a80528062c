# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.

# gradle Jvm???jdk 11??????GC
org.gradle.jvmargs=-Xmx1536m -Dfile.encoding=UTF-8 -XX:+UseParallelGC
#org.gradle.jvmargs=-Xmx1536m -DsocksProxyHost=127.0.0.1 -DsocksProxyPort=1080
#org.gradle.daemon=true

# When configured, Gradle will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# ??Gradle????
# Modularise your project and enable parallel build
org.gradle.parallel=true

# Enable configure on demand.
#org.gradle.configureondemand=true