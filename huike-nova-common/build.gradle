plugins {
    id 'java-library'
}

group 'com.huike.nova'
version '1.0.0'

dependencies {
    testImplementation 'org.junit.jupiter:junit-jupiter-api:5.8.1'
    testRuntimeOnly 'org.junit.jupiter:junit-jupiter-engine:5.8.1'

    implementation 'org.apache.tomcat.embed:tomcat-embed-core:9.0.65'
    // 钉钉
    implementation("com.aliyun:alibaba-dingtalk-service-sdk:2.0.0")
    implementation 'org.springframework:spring-web:5.3.22'
}

test {
    useJUnitPlatform()
}