/**
 * <AUTHOR>
 * @date 2023/12/11 15:17
 * @version 1.0 DingTalkConstant
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version DingTalkConstant.java, v 0.1 2023-12-11 15:17
 */
public class DingTalkConstant {

    /**
     * 通知
     */
    public static final String NOTICE = "通知";

    /**
     * 告警
     */
    public static final String ALARM = "告警";

    /**
     * 本地生活业务统计及告警
     */
    public static final String LOCAL_BUSINESS_DING_TALK_URL = "https://oapi.dingtalk.com/robot/send?access_token=433785fd055d2fbce0210d2976a3fbdbc6341b9f430b8cefda2f1a2dfaca7a7c";


    /**
     * 授权服务商脚本通知
     */
    public static final String SERVICE_PROVIDER_DING_TALK_URL = "https://oapi.dingtalk.com/robot/send?access_token=f97da583f0be19c1b448ca52f8723ff9495e4edf840dfbb07c335156ab0b3b0a";

    /**
     * 抖音商品审核钉钉券的服务URL
     */
    public static final String PRODUCT_APPROVE_DING_TALK_SERVICE_URL = "https://oapi.dingtalk.com/robot/send?access_token=37557b8cc67563aad7714c32223ea446aab1443acbcba7eb280143fcb14a329c";

    /**
     * 佣金的告警webHookUrl
     */
    public static final String COMMISSION_WEB_HOOK_URL = "https://oapi.dingtalk.com/robot/send?access_token=7e1dc0f2502d813bfd83aab59c9e25266ed793ff2564fc045e385a5d01a5f96d";

    /**
     * 平台结算的告警webHookUrl
     */
    public static final String PLATFORM_SETTLED_ALERT_WEB_HOOK_URL = "https://oapi.dingtalk.com/robot/send?access_token=453f0a34bbb9f859e45ea137b1ff07cc61e4bb4fb571adab11f377b89dd38b6b";

    /**
     * 来逛呗对账单资金异常钉钉机器人
     */
    public static final String LGB_FUND_ABNORMAL_DING_TALK_URL = "https://oapi.dingtalk.com/robot/send?access_token=5b2f95d1c64653da9c1fc3191eb3f7942e041df9bdea31007b83c21a06db62db";

}