package com.huike.nova.common.util.net;

import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;

/**
 * 信任
 *
 * <AUTHOR> (<EMAIL>)
 * @version NovaTrustManager.java, v1.0 08/24/2023 16:55 John Exp$
 */
public class NovaTrustManager implements X509TrustManager {

    public static X509TrustManager instance() {
        return INSTANCE;
    }

    private static final NovaTrustManager INSTANCE = new NovaTrustManager();

    private NovaTrustManager() {

    }

    @Override
    public void checkClientTrusted(X509Certificate[] x509Certificates, String s) {

    }

    @Override
    public void checkServerTrusted(X509Certificate[] x509Certificates, String s) {

    }

    @Override
    public X509Certificate[] getAcceptedIssuers() {
        return new X509Certificate[0];
    }
}
