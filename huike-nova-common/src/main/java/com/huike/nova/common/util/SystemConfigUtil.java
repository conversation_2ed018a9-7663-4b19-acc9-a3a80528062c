package com.huike.nova.common.util;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.val;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Collection;
import java.util.Enumeration;
import java.util.List;

/**
 * 系统配置
 *
 * <AUTHOR> (<EMAIL>)
 * @version SystemConfigUtil.java, v1.0 12/17/2023 10:44 John Exp$
 */
@Slf4j
public class SystemConfigUtil {

    /**
     * 获得当前机器的IP列表
     *
     * @return IP列表，如果存在多个网卡，则返回多个；不含LoopBack地址(127.0.0.1)
     */
    public static List<String> getIPList() {
        List<String> ipList = CollectionUtil.newArrayList();
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface ni = interfaces.nextElement();
                Enumeration<InetAddress> addresses = ni.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    val address = addresses.nextElement();
                    if (!address.isLoopbackAddress() && address.getHostAddress().indexOf(':') == -1) {
                        ipList.add(address.getHostAddress());
                    }
                }
            }
        } catch (Exception e) {
            LogUtil.error(log, "获取IP地址SocketException异常,e={}", e);
        }
        return ipList;
    }
}
