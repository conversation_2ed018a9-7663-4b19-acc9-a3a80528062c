/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.common.util;

import cn.hutool.core.util.DesensitizedUtil;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version DesensitizationUtil.java, v 0.1 2024-02-29 3:28 PM ruanzy
 */
public class DesensitizationUtil {

    /**
     * 15位身份证数
     */
    private static final int FIFTEEN = 15;

    /**
     * 18位身份证数
     */
    private static final int EIGHTEEN = 18;

    /**
     * 身份证号脱敏
     *
     * @param idCard
     * @return
     */
    public static String maskIdCard(String idCard) {
        if (StringUtils.isNotEmpty(idCard)) {
            // 身份证号脱敏规则一：保留前六后三
            if (idCard.length() == FIFTEEN) {
                idCard = idCard.replaceAll("(\\w{6})\\w*(\\w{3})", "$1******$2");
            } else if (idCard.length() == EIGHTEEN) {
                idCard = idCard.replaceAll("(\\w{6})\\w*(\\w{3})", "$1*********$2");
            }
        }
        return idCard;
    }

    public static String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 11) {
            return phoneNumber;
        }
        String prefix = phoneNumber.substring(0, 3);
        String suffix = phoneNumber.substring(phoneNumber.length() - 4);
        return prefix + "****" + suffix;
    }

    /**
     * 银行卡加密
     *
     * @param bankCard 银行卡
     * @return 加密过的银行卡
     */
    public static String maskBankCard(String bankCard) {
        return DesensitizedUtil.bankCard(bankCard);
    }

    /**
     * 名字脱敏
     *
     * @param name 名字
     * @return 脱敏的名字
     */
    public static String maskName(String name) {
        if (StringUtils.isBlank(name)) {
            return StringPool.EMPTY;
        }
        if (name.length() <= 1) {
            return name;
        }
        if (name.length() == CommonConstant.INTEGER_TWO) {
            return name.charAt(0) + "*";
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < name.length(); ++i) {
            if (i == 0 || i == name.length() - 1) {
                sb.append(name.charAt(i));
            } else {
                sb.append(StringPool.ASTERISK);
            }
        }
        return sb.toString();
    }
}