package com.huike.nova.common.util;

import cn.hutool.core.util.ObjectUtil;
import com.huike.nova.common.constant.CommonConstant;
import org.apache.commons.compress.utils.Lists;

import javax.annotation.Nullable;
import javax.validation.constraints.Null;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 数字工具类
 *
 * <AUTHOR> (<EMAIL>)
 * @version NumberUtil.java, v1.0 08/09/2023 11:55 John Exp$
 */
public class NumberUtil {

    /**
     * 分转元
     *
     * @param cents 分转元
     * @return 结果 cents 分转元
     */
    public static BigDecimal centsToYuan(Integer cents) {
        if (ObjectUtil.isNull(cents)) {
            return BigDecimal.ZERO;
        }
        return centsToYuan(new BigDecimal(cents));
    }

    public static BigDecimal centsToYuan(@Nullable BigDecimal cents) {
        if (cents == null) {
            return BigDecimal.ZERO;
        }
        return cents.divide(CommonConstant.ONE_HUNDRED, 2, RoundingMode.HALF_UP);
    }

    /**
     * 转为金额的BigDecimal，金额的小数点最多保留到后2位
     * 如果存在.0 或者 .00 金额，自动转为整数
     *
     * @param value 原始数值
     * @return 金额数值
     */
    public static BigDecimal toAmountVal(@Nullable BigDecimal value) {
        if (Objects.isNull(value)) {
            return BigDecimal.ZERO;
        }
        return value.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros();
    }

    /**
     * 分摊金额计算
     *
     * @param value    需要计算的值
     * @param quantity 数量
     * @return 分摊金额列表
     */
    public static List<BigDecimal> calculateSharedAmount(@Nullable BigDecimal value, int quantity) {
        // 数量为0，返回
        if (quantity == CommonConstant.ZERO) {
            return Collections.emptyList();
        }
        // 分摊金额
        BigDecimal sharedAmount = ObjectUtil.defaultIfNull(value, BigDecimal.ZERO)
                .divide(BigDecimal.valueOf(quantity), 2, RoundingMode.HALF_UP);
        // 剩余分摊金额
        BigDecimal remainSharedAmount = ObjectUtil.defaultIfNull(value, BigDecimal.ZERO);

        List<BigDecimal> sharedAmountList = new ArrayList<>(quantity);
        for (int i = 0; i != quantity; ++i) {
            if (Objects.equals(sharedAmount, BigDecimal.ZERO)) {
                sharedAmountList.add(BigDecimal.ZERO);
                continue;
            }
            // 如果非最后一条数据, 添加分摊金额
            if (i < quantity - 1) {
                sharedAmountList.add(sharedAmount);
                remainSharedAmount = remainSharedAmount.subtract(sharedAmount);
            } else {
                sharedAmountList.add(remainSharedAmount);
            }
        }
        return sharedAmountList;
    }

    /**
     * 判断是否数字
     *
     * @param str 字符串
     * @return
     */
    public static boolean isNumeric(String str) {
        if (str == null) {
            return false;
        }
        // 定义数字的正则表达式模式
        String pattern = "^[-+]?\\d+(\\.\\d+)?$";
        return Pattern.matches(pattern, str);
    }
}
