package com.huike.nova.common.util;

import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.function.Consumer;
import java.util.function.Supplier;

public class StringUtil {

    public static boolean areNotEmpty(CharSequence str1, CharSequence str2) {
        return isNotEmpty(str1) && isNotEmpty(str2);
    }

    /**
     * <p>Checks if a CharSequence is empty ("") or null.</p>
     * <p>
     * <pre>
     * StringUtil.isEmpty(null)      = true
     * StringUtil.isEmpty("")        = true
     * StringUtil.isEmpty(" ")       = false
     * StringUtil.isEmpty("bob")     = false
     * StringUtil.isEmpty("  bob  ") = false
     * </pre>
     * <p>
     * <p>NOTE: This method changed in Lang version 2.0.
     * It no longer trims the CharSequence.
     * That functionality is available in isBlank().</p>
     *
     * @param cs the CharSequence to check, may be null
     * @return {@code true} if the CharSequence is empty or null
     */
    public static boolean isEmpty(final CharSequence cs) {
        return cs == null || cs.length() == 0;
    }

    /**
     * <p>Checks if a CharSequence is not empty ("") and not null.</p>
     * <p>
     * <pre>
     * StringUtil.isNotEmpty(null)      = false
     * StringUtil.isNotEmpty("")        = false
     * StringUtil.isNotEmpty(" ")       = true
     * StringUtil.isNotEmpty("bob")     = true
     * StringUtil.isNotEmpty("  bob  ") = true
     * </pre>
     *
     * @param cs the CharSequence to check, may be null
     * @return {@code true} if the CharSequence is not empty and not null
     * @since 3.0 Changed signature from isNotEmpty(String) to isNotEmpty(CharSequence)
     */
    public static boolean isNotEmpty(final CharSequence cs) {
        return !isEmpty(cs);
    }

    public static void assignmentStringIfNotBlank(final String cs, Consumer<String> setter) {
        if (StringUtils.isNotBlank(cs)) {
            setter.accept(cs);
        }
    }

    public static void assignmentStringIfNotBlank(final Supplier<String> getter, Consumer<String> setter) {
        if (StringUtils.isNotBlank(getter.get())) {
            setter.accept(getter.get());
        }
    }


    /**
     * B属性填充到A
     *
     * @param a
     * @param b
     * @throws IllegalAccessException
     */
    public static void mergeObjects(Object a, Object b) throws IllegalAccessException {
        // 获取A和B的类
        Class<?> classA = a.getClass();
        Class<?> classB = b.getClass();
        // 获取所有字段
        Field[] fieldsA = classA.getDeclaredFields();
        for (Field fieldA : fieldsA) {
            // 设置私有字段可访问
            fieldA.setAccessible(true);
            // 获取A中字段的值
            Object valueA = fieldA.get(a);
            // 获取B中对应字段
            try {
                Field fieldB = classB.getDeclaredField(fieldA.getName());
                fieldB.setAccessible(true);
                // 如果A中字段为空，且B中字段非空，则用B的值填充A
                if (valueA == null) {
                    Object valueB = fieldB.get(b);
                    if (valueB != null) {
                        fieldA.set(a, valueB);
                    }
                }
            } catch (NoSuchFieldException e) {
                // 如果B没有该字段，跳过
                continue;
            }
        }
    }
}
