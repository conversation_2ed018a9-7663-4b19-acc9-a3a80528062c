/*
 * ailike.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.huike.nova.common.metadata;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 分页参数通用类
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PageParam<T> {
    /**
     * 请求对象
     */
    T query;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 每页数量
     */
    private Integer pageSize;
}