package com.huike.nova.common.util.net.okhttp;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.annotations.Beta;
import com.huike.nova.common.util.net.IProxyConfigProvider;
import com.huike.nova.common.util.net.ProxyConfigProviderConfigBuilder;

import javax.net.SocketFactory;
import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.net.UnknownHostException;

/**
 * <AUTHOR> (<EMAIL>)
 * @version ProxySocketFactory.java, v1.0 08/24/2023 17:05 John Exp$
 */
@Beta
public class ProxySocketFactory extends SocketFactory {

    private final IProxyConfigProvider configProvider;

    public ProxySocketFactory(ProxyConfigProviderConfigBuilder configProvider) {
        this.configProvider = configProvider;
    }

    @Override
    public Socket createSocket() {
        if (ObjectUtil.isAllNotEmpty(configProvider, configProvider.getProxy())) {
            return new Socket(configProvider.getProxy());
        } else {
            return new Socket();
        }
    }

    public Socket createSocket(String host, int port) throws IOException {
        Socket socket = createSocket();
        try {
            socket.connect(new InetSocketAddress(host, port));
        } catch (IOException e) {
            socket.close();
            throw e;
        }
        return socket;
    }

    public Socket createSocket(InetAddress address, int port)
            throws IOException {
        Socket socket = createSocket();
        try {
            socket.connect(new InetSocketAddress(address, port));
        } catch (IOException e) {
            socket.close();
            throw e;
        }
        return socket;
    }

    public Socket createSocket(String host, int port, InetAddress clientAddress, int clientPort)
            throws IOException, UnknownHostException {
        Socket socket = createSocket();
        try {
            socket.bind(new InetSocketAddress(clientAddress, clientPort));
            socket.connect(new InetSocketAddress(host, port));
        } catch (IOException e) {
            socket.close();
            throw e;
        }
        return socket;
    }

    public Socket createSocket(InetAddress address, int port, InetAddress clientAddress, int clientPort)
            throws IOException {
        Socket socket = createSocket();
        try {
            socket.bind(new InetSocketAddress(clientAddress, clientPort));
            socket.connect(new InetSocketAddress(address, port));
        } catch (IOException e) {
            socket.close();
            throw e;
        }
        return socket;
    }
}