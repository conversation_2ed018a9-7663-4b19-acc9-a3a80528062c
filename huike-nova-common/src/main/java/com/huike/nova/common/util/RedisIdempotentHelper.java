package com.huike.nova.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import org.redisson.api.RedissonClient;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * redis幂等操作
 *
 * <AUTHOR> (<EMAIL>)
 * @version RedisIdempotentHelper.java, v1.0 2024/4/2 23:07 John Exp$
 */
public class RedisIdempotentHelper {

    public RedisIdempotentHelper(int sec) {
        this.idempotentDurationSec = sec;
    }

    public RedisIdempotentHelper() {
        this(60);
    }

    /**
     * Redis 客户端
     */
    private final RedissonClient redissonClient = SpringUtil.getBean(RedissonClient.class);

    /**
     * 幂等锁定时间
     */
    private final int idempotentDurationSec;

    /**
     * 获得幂等参数（抛异常）
     *
     * @param pattern Key
     * @param arguments 参数
     */
    public void acquire(String pattern, Object... arguments) {
        if (!tryAcquire(pattern, arguments)) {
            throw new CommonException(ErrorCodeEnum.CALL_LIMIT).detailMessage("操作频繁，请稍后再试");
        }
    }

    /**
     * 操作幂等（不抛异常）
     *
     * @param pattern Key
     * @param arguments 参数
     * @return true-未锁定, false-锁定
     */
    public boolean tryAcquire(String pattern, Object... arguments) {
        String redisKey = StrUtil.format(pattern,arguments);
        return Objects.isNull(redissonClient.<Integer>getBucket(redisKey).getAndSet(CommonConstant.ONE, idempotentDurationSec, TimeUnit.SECONDS));
    }
}
