package com.huike.nova.common.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.huike.nova.common.constant.StringPool;
import lombok.val;
import org.apache.commons.codec.net.URLCodec;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Map;

/**
 * 编码工具
 *
 * <AUTHOR> (<EMAIL>)
 * @version CodecUtil.java, v1.0 07/04/2023 18:54 John Exp$
 */
public class UrlEncodeUtil {

    /**
     * Url编码解码
     */
    private static final URLCodec urlCodec = new URLCodec();


    /**
     * 对指定的字符串做UrlEncode
     *
     * @param str 原始字符串
     * @param fallback 发生异常的默认值
     * @return UrlEncode的字符串或是默认字符串
     */
    @Nullable
    public static String urlEncode(String str, @Nullable String fallback) {
        String encodedStr;
        try {
            encodedStr = urlCodec.encode(str);
        } catch (Exception ignored) {
            encodedStr = fallback;
        }
        return encodedStr;
    }

    /**
     * 对指定的字符串做UrlEncode
     *
     * @param str 原始字符串
     * @return UrlEncode的字符串或是空字符串
     */
    public static String urlEncode(String str) {
        return urlEncode(str, StringPool.EMPTY);
    }

    /**
     * 对指定字符串做UrlDecode
     *
     * @param str UrlEncode过的字符串
     * @param fallback 失败后的默认字符串
     * @return 成功时返回Decoded后的字符串，失败则返回fallback字符串
     */
    @Nullable
    public static String urlDecode(String str, @Nullable String fallback) {
        try {
            return urlCodec.decode(str);
        } catch (Exception ex) {
            return fallback;
        }
    }

    public static String urlDecode(String str) {
        try {
            return urlCodec.decode(str);
        } catch (Exception ex) {
            return str;
        }
    }

    /**
     * 转为Url参数
     *
     * @param queryParamMap 参数Map
     * @return query字符串, ?后面的部分
     */
    public static String toUrlQueryStringParam(Map<String, Object> queryParamMap) {
        return toUrlQueryStringParam(queryParamMap, false);
    }


    /**
     * 转为Url参数
     *
     * @param queryParamMap 参数Map
     * @param urlEncoded 是否已经做了UrlEncoded
     * @return query字符串, ?后面的部分
     */
    public static String toUrlQueryStringParam(Map<String, Object> queryParamMap, boolean urlEncoded) {
        // 判断参数映射是否为空
        if (CollectionUtil.isEmpty(queryParamMap)) {
            return StringPool.EMPTY;
        }
        val sb = new StringBuilder();
        queryParamMap.forEach((key, value) -> {
            val keyStr = urlEncoded ? Convert.toStr(key) : urlEncode(key);
            val valueStr = urlEncoded ? Convert.toStr(value) : urlEncode(Convert.toStr(value));
            // 组成K=V&的形式
            sb.append(keyStr).append(StringPool.EQUALS_CHAR).append(valueStr).append(StringPool.AMPERSAND);
        });
        return sb.substring(0, sb.length() - 1);
    }

    /**
     * 转为UrlQueryString
     *
     * @param url 原始的Url
     * @param queryParamMap 需要进行组装的Map
     * @return 返回组装好的Url
     */
    public static String toUrlQueryString(@Nonnull String url, @Nullable Map<String, Object> queryParamMap) {
        if (CollectionUtil.isEmpty(queryParamMap)) {
            return url;
        }
        val builder = UriComponentsBuilder.fromHttpUrl(url);
        for (val entry : queryParamMap.entrySet()) {
            builder.queryParam(urlEncode(entry.getKey()), urlEncode(Convert.toStr(entry.getValue())));
        }
        return builder.build().toUriString();
    }

    /**
     * 转为UrlQueryString
     *
     * @param url 原始的Url
     * @param param 对象
     * @return 返回组装好的Url
     */
    public static String toUrlQueryStringByJsonParam(@Nonnull String url, @Nonnull Object param) {
        // 转为JSON对象
        val json = JSONObject.toJSONString(param);
        val queryParamMap = JSON.parseObject(json, new TypeReference<Map<String, Object>>() {});
        return toUrlQueryString(url, queryParamMap);
    }
}
