/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package
        com.huike.nova.common.constant;

import org.apache.commons.lang3.SystemUtils;

import java.math.BigDecimal;

/**
 * 对账相关常量
 *
 * <AUTHOR>
 * @version ReconciliationConstant.java, v 0.1 2022-04-07 14:24
 */
public class ReconciliationConstant {

    /**
     * 当前用户家目录
     */
    public final static String USER_HOME_PATH = SystemUtils.getUserHome().getAbsolutePath();

    /**
     * 临时文件目录下载临时文件路径
     */
    public final static String TEMP_FILE_PATH = USER_HOME_PATH + "/reconciliation_data/temp/{}/{}";

    /**
     * 临时文件根目录
     */
    public final static String TEMP_FILE_ROOT_PATH = USER_HOME_PATH + "/reconciliation_data/temp/";

    /**
     * 联动对账bucket的存储路径
     * 用户目录/reconciliation_data/{对账类型}/{日期}/bucket/{数据来源}/{分片编号}.txt
     */
    public final static String TXT_BUCKET_STORAGE_PATH
            = USER_HOME_PATH + "/reconciliation_data/{}/{}/bucket/{}/{}.txt";


    /**
     * 对账单存储路径
     * /reconciliation_data/{对账类型}/{日期}/{账单类型}/
     */
    public final static String BILL_PATH
            = USER_HOME_PATH + "/reconciliation_data/{}/{}/{}";



    /**
     * 分片ID格式
     * {对账类型}_{对账batchId}_{分片编号}
     */
    public final static String TXT_BUCKET_ID_FORMAT = "{}_{}_{}";


    /**
     * zip压缩文件后缀
     */
    public final static String ZIP_SUFFIX = ".zip";

    /**
     * 临时文件后缀
     */
    public final static String TMP_SUFFIX = ".tmp";

    /**
     * csv文件后缀
     */
    public final static String CSV_SUFFIX = ".csv";

    /**
     * csv字段分隔符
     */
    public final static char CSV_FIELD_SEPARATOR = ',';



    /**
     * csv 文本包装符
     */
    public final static char CSV_TEXT_DELIMITER = '"';


    /**
     * 建行处理后文件存放地址
     */
    public final static String CCB_PAY_BILL_SHORT_NAME = "ccb_bills";

    public final static String GOSH_WITHDRAW_BILL_NAME = "gosh_withdraw_bill";

}