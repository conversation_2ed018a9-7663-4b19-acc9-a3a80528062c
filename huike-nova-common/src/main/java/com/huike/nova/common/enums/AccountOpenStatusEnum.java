package com.huike.nova.common.enums;/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */

/**
 * 门店帐套开通状态枚举
 *
 * <AUTHOR>
 */
public enum AccountOpenStatusEnum {

    /**
     * 未开通
     */
    CLOSE(0, "未开通"),
    /**
     * 使用中
     */
    USED(1, "使用中"),
    /**
     * 已过期
     */
    OVERDUE(2, "已过期"),
    ;

    private Integer value;

    private String name;

    AccountOpenStatusEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static AccountOpenStatusEnum getByValue(Integer value) {
        AccountOpenStatusEnum[] valueList = AccountOpenStatusEnum.values();
        for (AccountOpenStatusEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
