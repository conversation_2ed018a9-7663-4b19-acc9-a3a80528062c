/*
 *
 *  * Hangzhou Huike Technology co.,ltd.
 *  * Copyright (C) 2013-2022 All Rights Reserved.
 *
 *
 */

package com.huike.nova.common.util;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;

import java.text.NumberFormat;

/**
 * 秒表工具
 *
 * <AUTHOR> (<EMAIL>)
 * @version StopWatchUtil.java, v1.0 11/19/2022 10:04 John Exp$
 */
public class StopWatchUtil {

    /**
     * 毫秒级简短摘要
     *
     * @param stopWatch 秒表对象
     * @return 秒表计时情况
     */
    private static String shortSummary(StopWatch stopWatch) {
        return StrUtil.format("StopWatch '{}': running time = {} ms", stopWatch.getId(), stopWatch.getTotalTimeMillis());
    }

    /**
     * 以毫秒打印秒表
     *
     * @param stopWatch 秒表对象
     * @return 秒表计时情况
     */
    public static String prettyPrint(StopWatch stopWatch) {
        StringBuilder sb = new StringBuilder();
        sb.append(shortSummary(stopWatch));
        sb.append(FileUtil.getLineSeparator());
        if (null == stopWatch.getTaskInfo()) {
            sb.append("No task info kept");
        } else {
            sb.append("---------------------------------------------").append(FileUtil.getLineSeparator());
            sb.append("ms         %     Task name").append(FileUtil.getLineSeparator());
            sb.append("---------------------------------------------").append(FileUtil.getLineSeparator());
            NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMinimumIntegerDigits(9);
            nf.setGroupingUsed(false);
            NumberFormat pf = NumberFormat.getPercentInstance();
            pf.setMinimumIntegerDigits(2);
            pf.setGroupingUsed(false);
            StopWatch.TaskInfo[] taskInfos = stopWatch.getTaskInfo();
            for (StopWatch.TaskInfo task : taskInfos) {
                sb.append(nf.format(task.getTimeMillis())).append("  ");
                sb.append(pf.format((double) task.getTimeNanos() / (double) stopWatch.getTotalTimeNanos())).append("  ");
                sb.append(task.getTaskName()).append(FileUtil.getLineSeparator());
            }
        }

        return sb.toString();
    }
}
