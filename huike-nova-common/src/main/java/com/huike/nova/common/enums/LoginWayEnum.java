package com.huike.nova.common.enums;/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */

/**
 * <AUTHOR>
 */

public enum LoginWayEnum {
    /**
     * 账密登录
     */
    ZM_LOGIN(1, "账密登录"),
    /**
     * 验证码登录
     */
    SMS_LOGIN(2, "验证码登录");

    private Integer value;

    private String name;

    LoginWayEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public static LoginWayEnum getByValue(Integer value) {
        LoginWayEnum[] valueList = LoginWayEnum.values();
        for (LoginWayEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
