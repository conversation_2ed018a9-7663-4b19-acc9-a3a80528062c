package com.huike.nova.common.util.net.apache;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.net.IProxyConfigProvider;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Objects;

/**
 * ApacheHttpClient工具类
 *
 * <AUTHOR> (<EMAIL>)
 * @version ApacheHttpClientUtil.java, v1.0 08/25/2023 11:08 John Exp$
 */
@Slf4j
@Data
@Accessors(chain = true)
public class ApacheHttpClientUtil {

    static {
        System.out.println("init http TLS protocol: TLSv1.1, TLSv1.2");
        System.setProperty("https.protocols", "TLSv1.1,TLSv1.2");
    }

    /** 常量定义：HTTP */
    private static final String HTTP = "http";

    /** 常量定义：HTTPS */
    private static final String HTTPS = "https";

    public ApacheHttpClientUtil(IProxyConfigProvider proxyConfigProvider) {
        this.proxyConfigProvider = proxyConfigProvider;
    }

    /** 连接超时时间，默认10s */
    private int connectTimeout = 10000;

    /** 发送接收超时时间，默认10s */
    private int readWriteTimeout = 10000;

    /** 代理管理类 */
    private final IProxyConfigProvider proxyConfigProvider;

    private CloseableHttpClient createHttpClient() {
        val requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(connectTimeout)
                .setSocketTimeout(readWriteTimeout)
                .build();
        val httpClientBuilder = HttpClients.custom()
                .setDefaultRequestConfig(requestConfig);

        // 如果已配置了代理对象
//        if (ObjectUtil.isAllNotEmpty(proxyConfigProvider, proxyConfigProvider.getProxy())) {
//            Registry<ConnectionSocketFactory> reg = RegistryBuilder.<ConnectionSocketFactory>create()
//                    .register(HTTP, new NovaConnectionSocketFactory())
//                    .register(HTTPS, new NovaSSLConnectionSocketFactory()).build();
//            val proxyConnectionManager = new PoolingHttpClientConnectionManager(reg, new FakeDnsResolver());
//            httpClientBuilder.setConnectionManager(proxyConnectionManager);
//        }
        return httpClientBuilder.build();
    }

    /**
     * 以JSON格式发送POST请求
     *
     * @param url 发送的URL地址
     * @param param 参数对象
     * @param headers 发送请求头
     * @return 转为字符串方式返回
     * @throws IOException IO异常
     */
    public String postAsJson(String url, Object param, Map<String, Object> headers) throws IOException {
        try (val httpClient = createHttpClient()) {
            HttpClientContext context = new HttpClientContext();
//            if (ObjectUtil.isAllNotEmpty(proxyConfigProvider, proxyConfigProvider.getProxy())) {
//                val proxy = proxyConfigProvider.getProxy();
//                context.setAttribute(CommonConstant.SOCKS_ADDRESS, proxy.address());
//            }
            val httpPost = new HttpPost(url);
            if (CollectionUtil.isNotEmpty(headers)) {
                headers.forEach((key, val) -> {
                    if (Objects.nonNull(val)) {
                        httpPost.addHeader(key, String.valueOf(val));
                    }
                });
            }
            String json = "{}";
            if (param instanceof String) {
                json = (String) param;
            } else {
                json = JSONObject.toJSONString(param);
            }
            httpPost.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
            val httpResponse = httpClient.execute(httpPost, context);
            return EntityUtils.toString(httpResponse.getEntity(), StandardCharsets.UTF_8);

        } catch (IOException ex) {
            LogUtil.warn(log, "ApacheHttpClientUtil.postAsJson >> 发生错误, ", ex);
            throw ex;
        }
    }
}
