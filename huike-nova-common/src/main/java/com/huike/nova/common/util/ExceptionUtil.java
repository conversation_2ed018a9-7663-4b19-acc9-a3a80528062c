package com.huike.nova.common.util;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 异常工具
 *
 * <AUTHOR> (<EMAIL>)
 * @version ExceptionUtil.java, v1.0 12/17/2023 17:04 John Exp$
 */
public class ExceptionUtil {

    /**
     * 获得错误信息
     *
     * @param throwable 异常
     * @return 错误信息
     */
    public static String getErrorMessage(Throwable throwable) {
        return StringUtils.defaultIfBlank(throwable.getMessage(), throwable.toString());
    }

    public static String getMessage(Throwable e) {
        return null == e ? "null" : StrUtil.format("{}: {}", new Object[]{e.getClass().getSimpleName(), e.getMessage()});
    }

    /**
     * 将任意异常转为CommonException
     *
     * @param throwable 异常
     * @return 错误
     */
    public static CommonException toCommonException(Throwable throwable, ErrorCodeEnum errorCodeEnum) {
        if (throwable instanceof CommonException) {
            return (CommonException) throwable;
        }
        return new CommonException(errorCodeEnum).detailMessage(getErrorMessage(throwable));
    }

    /**
     * 将任意异常转为CommonException
     *
     * @param throwable 异常
     * @return 错误
     */
    public static CommonException toCommonException(Throwable throwable) {
        return toCommonException(throwable, ErrorCodeEnum.SERVER_ERROR);
    }

    public static void checkParamIsNotBlank(String str, String field) {
        if (StringUtils.isBlank(str)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage(String.format("%s不能为空", field));
        }
    }

    public static void checkParamIsNotEmpty(List<?> list, String field) {
        if (CollectionUtil.isEmpty(list)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage(String.format("%s不能为空", field));
        }
    }
}
