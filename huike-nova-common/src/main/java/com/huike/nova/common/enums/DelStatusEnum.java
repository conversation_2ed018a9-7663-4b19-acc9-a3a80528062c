/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.enums;

/**
 * <AUTHOR>
 * @version DelStatusEnum.java, v 0.1 2022-09-19 4:11 PM ruanzy
 */
public enum DelStatusEnum {
    /**
     * 未删除
     */
    NOT_DEL("未删除", 1),
    /**
     * 已删除
     */
    DEL("已删除", 2),
    ;

    private String name;
    private Integer value;

    DelStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static DelStatusEnum getByValue(Integer value) {
        DelStatusEnum[] valueList = DelStatusEnum.values();
        for (DelStatusEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public Integer getValue() {
        return value;
    }
}