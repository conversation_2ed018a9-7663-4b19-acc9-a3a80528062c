/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version EnvEnum.java, v 0.1 2022-09-20 4:58 PM ruanzy
 */
@Getter
public enum EnvEnum {
    /**
     * 开发环境
     */
    DEV("开发环境", "DEV"),
    TEST("测试环境", "TEST"),
    BETA("预发环境", "BETA"),
    PROD("正式环境", "PROD");

    /**
     * -- GETTER --
     *  Getter method for property <tt>name</tt>.
     */
    private final String name;
    /**
     * -- GETTER --
     *  Getter method for property <tt>value</tt>.
     */
    private final String value;

    EnvEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public static EnvEnum getByValue(String value) {
        EnvEnum[] valueList = EnvEnum.values();
        for (EnvEnum v : valueList) {
            if (StrUtil.equalsIgnoreCase(v.getValue(), value)) {
                return v;
            }
        }
        return null;
    }

}