/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.huike.nova.common.excel.ExcelWidthStyleStrategy;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version ExportUtil.java, v 0.1 2023-03-29 5:10 PM ruanzy
 */
public class ExportUtil {

    /**
     * 生成导出文件在本地
     *
     * @param data
     * @param fileName
     * @param tClass
     * @param excludeColumnFiledNames 忽略的字段名
     * @return {@link String}
     * <AUTHOR>
     */
    public static <T> void createFileToLocal(List<T> data, String fileName, Class<T> tClass, Set<String> excludeColumnFiledNames) {
        try (ExcelWriter excelWriter = EasyExcel.write(fileName + ".xlsx").build()) {
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = processStyle();
            // 这里 需要指定写用哪个class去写
            WriteSheet writeSheet = EasyExcel.writerSheet(fileName)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .registerWriteHandler(new ExcelWidthStyleStrategy())
                    .excludeColumnFieldNames(excludeColumnFiledNames)
                    .head(tClass)
                    .build();
            excelWriter.write(data, writeSheet);
            // 关闭流
            excelWriter.finish();
        }
    }

    /**
     * 生成导出文件在本地
     *
     * @param data
     * @param fileName
     * @param tClass
     * @param excludeColumnFiledNames 忽略的字段名
     * @return {@link String}
     * <AUTHOR>
     */
    public static <T> void createExcelFileToLocal(List<T> data, String fileName, Class<T> tClass, Set<String> excludeColumnFiledNames) {
        try (ExcelWriter excelWriter = EasyExcel.write(fileName).build()) {
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = processStyle();
            // 这里 需要指定写用哪个class去写
            WriteSheet writeSheet = EasyExcel.writerSheet(fileName)
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .registerWriteHandler(new ExcelWidthStyleStrategy())
                    .excludeColumnFieldNames(excludeColumnFiledNames)
                    .head(tClass)
                    .build();
            excelWriter.write(data, writeSheet);
            // 关闭流
            excelWriter.finish();
        }
    }

    /**
     * 处理样式
     *
     * @param
     * @return {@link HorizontalCellStyleStrategy}
     * <AUTHOR>
     */
    public static HorizontalCellStyleStrategy processStyle() {
        // 处理excel样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 设置背景颜色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        // 设置头字体
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 13);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 设置头居中
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        // 内容策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置 水平居中
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }
}