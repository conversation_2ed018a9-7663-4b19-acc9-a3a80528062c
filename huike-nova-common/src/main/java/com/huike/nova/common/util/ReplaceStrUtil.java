/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.util;

import org.apache.commons.lang3.StringEscapeUtils;

/**
 * <AUTHOR>
 * @version ReplaceStrUtil.java, v 0.1 2022-11-10 4:42 PM ruanzy
 */
public class ReplaceStrUtil {

    /**
     * 移除多余符号
     */
    public static String replaceStrSymbol(String str) {
        return StringEscapeUtils.unescapeJava(str)
                .replace("\"{", "{")
                .replace("}\"", "}")
                .replace("\"[", "[")
                .replace("]\"", "]");
    }
}