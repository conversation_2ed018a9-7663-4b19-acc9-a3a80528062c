/**
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.common.constant.gosh;

/**
 * <AUTHOR>
 * @version RedisPrefixConstant.java, v 0.1 2022/5/26 11:46 上午 mayucong
 */
public class RedisPrefixConstant {

    /**
     * 导出限流
     */
    public static final String GROUP_MERCHANT_COMMISSION_CONFIRM_REDIS_KEY = "lock.export.limiting.{}";


    /**
     * uid自增主键redisKey
     */
    public static final String UID_INCR_BUSINESS_PRIMARY_KEY = "uid.incr.business.primary.key";

    /**
     * storeId自增主键redisKey
     */
    public static final String STORE_ID_INCR_BUSINESS_PRIMARY_KEY = "store.id.incr.business.primary.key";

    /**
     * 高德数据
     */
    public static final String GAODE_CODE_TREE_CACHE_KEY = "gaode.code.tree.cache.key.{}";

    /**
     * 高德树状数据查询锁定
     */
    public static final String GAODE_CODE_TREE_QUERY_KEY_LOCK = "gaode.code.tree.query.key.lock";


    /**
     * 结算单处理key
     */
    public static final String SETTLE_FORM_DEAL_REDIS_KEY = "settle.form.deal.redis.key";


}
