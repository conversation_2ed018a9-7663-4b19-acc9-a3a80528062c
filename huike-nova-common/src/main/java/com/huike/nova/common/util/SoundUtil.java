/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.util;

import com.huike.nova.common.constant.CommonConstant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version SoundUtil.java, v 0.1 2023-03-06 3:38 PM ruanzy
 */
public class SoundUtil {


    /**
     * 计算口播时长
     *
     * @param content
     * @return
     */
    public static BigDecimal countTime(String content) {
        char[] chs = content.toCharArray();
        int digitNum = 0;
        int letterNum = 0;
        for (char ch : chs) {
            if (Character.isLowerCase(ch) || Character.isUpperCase(ch)) {
                letterNum++;
                continue;
            }
            if (Character.isDigit(ch)) {
                digitNum++;
                continue;
            }
        }
        int hzNum = content.length() - content.replaceAll(CommonConstant.HZ_REGEX, "").length();
        BigDecimal result = BigDecimal.valueOf(Double.valueOf(CommonConstant.HZ_TIME))
                .multiply(BigDecimal.valueOf(Long.valueOf(hzNum)))
                .add(BigDecimal.valueOf(Double.valueOf(CommonConstant.SZ_ZM_TIME))
                        .multiply(BigDecimal.valueOf(Long.valueOf(digitNum + letterNum))))
                .setScale(2, BigDecimal.ROUND_HALF_UP);
        return result.stripTrailingZeros();
    }
}