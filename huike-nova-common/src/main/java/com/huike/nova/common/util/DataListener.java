/**
 * ailike.com
 * Copyright (C) 2021-2022 All Rights Reserved.
 */
package com.huike.nova.common.util;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 监听器
 *
 * <AUTHOR>
 * @version DataListener.java, v 0.1 2022-08-18 5:41 下午 mayucong
 */
@Slf4j
public class DataListener<T> extends AnalysisEventListener<T> {

    private final List<T> rows = new ArrayList<>();

    @Override
    public void invoke(T t, AnalysisContext analysisContext) {
        rows.add(t);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        LogUtil.info(log, "解析完成！读取{}行", rows.size());
    }

    public List<T> getRows() {
        return rows;
    }
}
