package com.huike.nova.common.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.exceptions.ExceptionUtil;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

public class ImageUtil {

    private static Logger logger = LoggerFactory.getLogger(ImageUtil.class);

    /**
     * 获取文件名称
     *
     * @param fileUrl
     * @return
     * @throws MalformedURLException
     */
    public static String getFileName(String fileUrl) throws MalformedURLException {
        URL url = new URL(fileUrl);
        String path = url.getPath();
        return path.substring(path.lastIndexOf("/") + 1);
    }

    /**
     * 文件转换成字节数组
     *
     * @param location
     * @return
     * @throws Exception
     */
    public static byte[] fileToByte(String location) throws Exception {
        URL url = new URL(location);
        InputStream is = null;
        byte[] bytes = null;
        try {
            is = url.openStream();
            bytes = IOUtils.toByteArray(is);
        } catch (IOException e) {
            logger.error(" fileToByte ---- >> fileToByte err, e = {}", ExceptionUtil.stacktraceToString(e));
        } finally {
            if (is != null) is.close();
        }
        return bytes;
    }

    /***
     * 将图片文件转化为字节数组字符串，并对其进行Base64编码处理
     * @param imgUrl 图片的网络URL
     * @return
     */
    public static String encodeImageToBase64(String imgUrl) {
        if (StringUtils.isBlank(imgUrl)) {
            return null;
        }

        URL url;
        HttpURLConnection conn;
        try {
            url = new URL(imgUrl);
            conn = (HttpURLConnection) url.openConnection();
            // 设置Http请求头为GET
            conn.setRequestMethod("GET");
            // 设置超时响应时间为5s
            conn.setConnectTimeout(5 * 1000);
            // 通过输入流获取图片资源
            InputStream is = conn.getInputStream();
            // 得到图片的二进制数据，以二进制封装得到数据
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            // 创建一个Buffer字符串
            byte[] buffer = new byte[1024];
            // 每次读取的字符串长度，如果为-1，代表全部读取完毕
            int len = 0;
            // 使用一个输入流从buffer里把数据读取出来
            while ((len = is.read(buffer)) != -1) {
                // 用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                os.write(buffer, 0, len);
            }
            // 关闭输入流
            is.close();
            byte[] data = os.toByteArray();
            // 对字节数组Base64编码
            return Base64.encodeUrlSafe(data);
        } catch (Exception e) {
            logger.error(" ImageUtil ---- >> encodeImageToBase64 err, e = {}", ExceptionUtil.stacktraceToString(e));
        }
        return null;
    }
}
