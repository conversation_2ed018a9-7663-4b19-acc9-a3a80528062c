/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.huike.nova.common.util.oss.param;

import cn.hutool.core.codec.Base64;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version OssWatermarkTextParam.java, v 0.1 2019-04-22 16:16 buhao
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OssWatermarkTextParam extends OssWatermarkCommonParam {
    /**
     * 参数意义：表示文字水印的文字内容
     */
    private String text;
    /**
     * 参数意义：表示文字水印的文字类型
     */
    private String type;
    /**
     * 参数意义：文字水印的文字的颜色
     * <p>
     * 参数的构成必须是：六个十六进制数，如：000000表示黑色。 000000每两位构成RGB颜色， FFFFFF表示的是白色
     * <p>
     * 默认值：000000黑色
     */
    private String color;
    /**
     * 参数意义：文字水印的文字大小(px)
     * <p>
     * 取值范围：(0，1000]
     * <p>
     * 默认值：40
     */
    private Integer size;
    /**
     * 参数意义：文字水印的阴影透明度
     * <p>
     * 取值范围：[0,100]
     */
    private Integer shadow;
    /**
     * 参数意义：文字顺时针旋转角度
     * <p>
     * 取值范围：[0,360]
     */
    private Integer rotate;
    /**
     * 参数意义：进行水印铺满的效果
     * <p>
     * 取值范围：[0,1]，1表示铺满，0表示效果无效
     */
    private Integer fill;

    /**
     * Setter method for property <tt>text</tt>.
     *
     * @param text value to be assigned to property text
     */
    public void setText(String text) {
        this.text = Base64.encodeUrlSafe(text);
    }
}