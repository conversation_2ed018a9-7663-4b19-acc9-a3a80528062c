/**
 * Copyright (c) 2017, <EMAIL>(BuHao) All Rights Reserved.
 */
package com.huike.nova.common.util;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.AuthSchemes;
import org.apache.http.client.config.CookieSpecs;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.huike.nova.common.constant.StringPool.CONTENT_FORM_HEADER_UTF_8;
import static com.huike.nova.common.constant.StringPool.CONTENT_TYPE;
import static com.huike.nova.common.constant.StringPool.UTF_8;

/**
 * <AUTHOR>
 * @version RequestUtil.java, v 0.1 2018-06-08 14:50 buhao
 */
@Slf4j(topic = "RequestLog")
public class RequestUtil {
    /**
     * 超时时间
     */
    private static final int TIMEOUT = 10000;
    /**
     * 最大响应内容大于1M则不打印日志
     */
    private static final Integer MAX_LOG_SIZE = 1024 * 1024;
    private static final String HTTPS = "https://";

    /**
     * post 请求
     *
     * @param url
     * @param params
     * @return
     */
    public static String post(String url, Map<String, String> params) {
        return post(url, params, TIMEOUT);
    }

    /**
     * post 请求
     *
     * @param url
     * @param params
     * @return
     */
    public static String post2(String url, Map<String, String> params, int timeout) {
        if (timeout > TIMEOUT) {
            timeout = TIMEOUT;
        }
        LogUtil.info(log, "begin request->url={},param={}", url, JSON.toJSONString(params));
        String content = null;
        long begin = SystemClock.millisClock().now();
        try {
            content = FsHttpUtil.post(url, params, UTF_8, CONTENT_FORM_HEADER_UTF_8, 3000, timeout, null);
        } catch (Exception ex) {
            LogUtil.error(log, "request error,url={},time={}", url, SystemClock.millisClock().now() - begin);
            throw new RuntimeException(ex.getMessage(), ex);
        }
        if (!StringUtils.isBlank(content) && content.length() < MAX_LOG_SIZE) {
            long end = SystemClock.millisClock().now();
            LogUtil.info(log, "end request->url={},param={},result={},cost={}ms", url, JSON.toJSONString(params), content, end - begin);
        }
        return content;
    }

    public static String post(String url, Map<String, String> params, int timeout) {
        if (timeout > TIMEOUT) {
            timeout = TIMEOUT;
        }
        Map<String, Object> form = Maps.newHashMap();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            form.put(entry.getKey(), entry.getValue());
        }
        LogUtil.info(log, "begin request->url={},param={}", url, JSON.toJSONString(params));
        long begin = SystemClock.millisClock().now();
        String content = HttpRequest.post(url).header(CONTENT_TYPE, CONTENT_FORM_HEADER_UTF_8).form(form).timeout(timeout).execute().body();
        if (!StringUtils.isBlank(content) && content.length() < MAX_LOG_SIZE) {
            long end = SystemClock.millisClock().now();
            LogUtil.info(log, "end request->url={},param={},result={},cost={}ms", url, JSON.toJSONString(params), content, end - begin);
        }
        return content;
    }

    /**
     * @description:get请求,自定义超时时间
     * @author: yangdw
     * @date: 2018/9/20 下午2:56
     * @param: * @param url
     * @return: java.lang.String
     */
    public static String get(String url, int timeout) {
        if (timeout > TIMEOUT) {
            timeout = TIMEOUT;
        }
        LogUtil.info(log, "begin request->url={}", url);
        long begin = SystemClock.millisClock().now();
        String content = HttpRequest.get(url).timeout(timeout).execute().body();
        if (!StringUtils.isBlank(content) && content.length() < MAX_LOG_SIZE) {
            long end = SystemClock.millisClock().now();
            LogUtil.info(log, "end request->url={},result={},cost={}ms", url, content, end - begin);
        }
        return content;
    }

    /**
     * @description:get请求,自定义超时时间
     * @author: yangdw
     * @date: 2018/9/20 下午2:56
     * @param: * @param url
     * @return: java.lang.String
     */
    public static String get2(String url, int timeout) {
        if (timeout > TIMEOUT) {
            timeout = TIMEOUT;
        }
        LogUtil.info(log, "begin request->url={}", url);
        long begin = SystemClock.millisClock().now();
        String content = null;
        try {
            content = FsHttpUtil.get(url, timeout);
        } catch (Exception ex) {
            LogUtil.error(log, "request error,url={},time={}", url, SystemClock.millisClock().now() - begin);
            throw new RuntimeException(ex.getMessage(), ex);
        }
        if (!StringUtils.isBlank(content) && content.length() < MAX_LOG_SIZE) {
            long end = SystemClock.millisClock().now();
            LogUtil.info(log, "end request->url={},result={},cost={}ms", url, content, end - begin);
        }
        return content;
    }

    /**
     * Post请求，支持https
     *
     * @param host
     * @param path
     * @param headers
     * @param queryMap
     * @param body
     * @return
     * @throws Exception
     */
    public static HttpResponse post(String host, String path,
                                    Map<String, String> headers,
                                    Map<String, String> queryMap,
                                    String body)
            throws Exception {
        try (CloseableHttpClient httpClient = wrapClient(host, path)) {
            HttpPost request = new HttpPost(buildUrl(host, path, queryMap));
            for (Map.Entry<String, String> e : headers.entrySet()) {
                request.addHeader(e.getKey(), e.getValue());
            }
            if (StringUtils.isNotBlank(body)) {
                request.setEntity(new StringEntity(body, "utf-8"));
            }
            return httpClient.execute(request);
        } catch (Throwable e) {
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * 将map转成url参数格式的字符串，例如：key1=value1&key2=value2&key3=value3
     *
     * @param dataMap
     * @return
     */
    public static String mapToURLParamStr(Map<String, String> dataMap) {
        StringBuilder params = new StringBuilder();
        for (Map.Entry<String, String> entry : dataMap.entrySet()) {
            params.append(entry.getKey())
                    .append("=")
                    .append(entry.getValue())
                    .append("&");
        }
        return params.substring(0, params.length() - 1);
    }

    /**
     * 获取 HttpClient
     *
     * @param host
     * @param path
     * @return
     */
    private static CloseableHttpClient wrapClient(String host, String path) {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        if (host != null && host.startsWith(HTTPS)) {
            return sslClient();
        } else if (StringUtils.isBlank(host) && path != null && path.startsWith(HTTPS)) {
            return sslClient();
        }
        return httpClient;
    }

    /**
     * 在调用SSL之前需要重写验证方法，取消检测SSL
     * 创建ConnectionManager，添加Connection配置信息
     *
     * @return HttpClient 支持https
     */
    private static CloseableHttpClient sslClient() {
        try {
            // 在调用SSL之前需要重写验证方法，取消检测SSL
            X509TrustManager trustManager = new X509TrustManager() {
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return null;
                }

                @Override
                public void checkClientTrusted(X509Certificate[] xcs, String str) {
                }

                @Override
                public void checkServerTrusted(X509Certificate[] xcs, String str) {
                }
            };
            SSLContext ctx = SSLContext.getInstance(SSLConnectionSocketFactory.TLS);
            ctx.init(null, new TrustManager[]{trustManager}, null);
            SSLConnectionSocketFactory socketFactory = new SSLConnectionSocketFactory(ctx, NoopHostnameVerifier.INSTANCE);
            // 创建Registry
            RequestConfig requestConfig = RequestConfig.custom().setCookieSpec(CookieSpecs.STANDARD_STRICT)
                    .setExpectContinueEnabled(Boolean.TRUE).setTargetPreferredAuthSchemes(Arrays.asList(AuthSchemes.NTLM, AuthSchemes.DIGEST))
                    .setConnectTimeout(TIMEOUT)
                    .setConnectionRequestTimeout(TIMEOUT)
                    .setSocketTimeout(TIMEOUT)
                    .setProxyPreferredAuthSchemes(Collections.singletonList(AuthSchemes.BASIC)).build();
            Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.INSTANCE)
                    .register("https", socketFactory).build();
            // 创建ConnectionManager，添加Connection配置信息
            PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
            return HttpClients.custom().setConnectionManager(connectionManager)
                    .setDefaultRequestConfig(requestConfig).build();
        } catch (KeyManagementException | NoSuchAlgorithmException ex) {
            throw new RuntimeException(ex);
        }
    }


    private static String buildUrl(String host, String path, Map<String, String> queryMap) throws UnsupportedEncodingException {
        StringBuilder sbUrl = new StringBuilder();
        sbUrl.append(host);
        if (!StringUtils.isBlank(path)) {
            sbUrl.append(path);
        }
        if (null != queryMap) {
            StringBuilder sbQuery = new StringBuilder();
            for (Map.Entry<String, String> query : queryMap.entrySet()) {
                if (0 < sbQuery.length()) {
                    sbQuery.append("&");
                }
                if (StringUtils.isBlank(query.getKey()) && !StringUtils.isBlank(query.getValue())) {
                    sbQuery.append(query.getValue());
                }
                if (!StringUtils.isBlank(query.getKey())) {
                    sbQuery.append(query.getKey());
                    if (!StringUtils.isBlank(query.getValue())) {
                        sbQuery.append("=");
                        sbQuery.append(URLEncoder.encode(query.getValue(), "utf-8"));
                    }
                }
            }
            if (0 < sbQuery.length()) {
                sbUrl.append("?").append(sbQuery);
            }
        }
        return sbUrl.toString();
    }


    /**
     * httpclent post请求 UrlEncodeForm
     *
     * @param host
     * @param path
     * @param headers
     * @param queryMap
     * @param formBody
     * @return
     * @throws Exception
     */
    public static HttpResponse formPost(String host, String path, Map<String, String> headers,
                                        Map<String, String> queryMap, Map<String, String> formBody)
            throws Exception {
        try (CloseableHttpClient httpClient = wrapClient(host, path)) {
            HttpPost request = new HttpPost(buildUrl(host, path, queryMap));
            if (null != headers) {
                for (Map.Entry<String, String> e : headers.entrySet()) {
                    request.addHeader(e.getKey(), e.getValue());
                }
            }
            if (formBody != null) {
                List<NameValuePair> nameValuePairList = new ArrayList<NameValuePair>();

                for (String key : formBody.keySet()) {
                    nameValuePairList.add(new BasicNameValuePair(key, formBody.get(key)));
                }
                UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(nameValuePairList, "utf-8");
                formEntity.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
                request.setEntity(formEntity);
            }
            return httpClient.execute(request);
        } catch (Throwable e) {
            throw ExceptionUtil.toCommonException(e);
        }
    }

    /**
     * httpClient Get请求
     *
     * @param host
     * @param headers
     * @param queryMap
     * @return
     * @throws Exception
     */
    public static HttpResponse httpClientGet(String host,String path,
                                             Map<String, String> headers,
                                             Map<String, String> queryMap)
            throws Exception {
        try (CloseableHttpClient httpClient = wrapClient(host, path)) {
            HttpGet request = new HttpGet(buildUrl(host, path, queryMap));
            for (Map.Entry<String, String> e : headers.entrySet()) {
                request.addHeader(e.getKey(), e.getValue());
            }
            return httpClient.execute(request);
        } catch (Throwable e) {
            throw ExceptionUtil.toCommonException(e);
        }
    }
}
