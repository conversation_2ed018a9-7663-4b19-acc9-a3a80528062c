package com.huike.nova.common.util;

import cn.hutool.core.util.StrUtil;
import com.google.common.io.Files;
import lombok.SneakyThrows;
import lombok.val;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.InputStream;

/**
 * 文件工具
 *
 * <AUTHOR> (<EMAIL>)
 * @version FileUtil.java, v1.0 12/28/2023 18:51 John Exp$
 */
public class FileUtil {
    @SneakyThrows
    public static File writeFile(String fileName, String folder, InputStream inputStream) {
        val file = new File(folder, fileName);
        Files.asByteSink(file).writeFrom(inputStream);
        return file;
    }

    @SneakyThrows
    public static File writeStreamTemporaryFile(String fileName, InputStream inputStream) {
        return writeFile(fileName, System.getProperty("java.io.tmpdir", ""), inputStream);
    }

    public static String generateRandomFileName(String fileName) {
        return StrUtil.format("{}-{}.{}", Files.getNameWithoutExtension(fileName), RandomStringUtils.randomAlphabetic(16), Files.getFileExtension(fileName));
    }
}
