package com.huike.nova.common.util.net.apache;

import com.huike.nova.common.constant.CommonConstant;
import org.apache.http.HttpHost;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.protocol.HttpContext;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.Socket;

/**
 * 普通的链接
 *
 * <AUTHOR> (<EMAIL>)
 * @version NovaConnectionSocketFactory.java, v1.0 08/25/2023 10:49 John Exp$
 */
public class NovaConnectionSocketFactory extends PlainConnectionSocketFactory {
    @Override
    public Socket createSocket(HttpContext context) {
        InetSocketAddress socksAddr = (InetSocketAddress) context.getAttribute(CommonConstant.SOCKS_ADDRESS);
        Proxy proxy = new Proxy(Proxy.Type.SOCKS, socksAddr);
        return new Socket(proxy);
    }

    @Override
    public Socket connectSocket(int connectTimeout, Socket socket, HttpHost host, InetSocketAddress remoteAddress, InetSocketAddress localAddress, HttpContext context) throws IOException {
        // Convert address to unresolved
        InetSocketAddress unresolvedRemote = InetSocketAddress
                .createUnresolved(host.getHostName(), remoteAddress.getPort());
        return super.connectSocket(connectTimeout, socket, host, unresolvedRemote, localAddress, context);
    }
}
