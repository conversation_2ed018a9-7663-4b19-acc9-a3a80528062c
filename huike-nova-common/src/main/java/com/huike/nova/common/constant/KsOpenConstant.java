/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version KsOpenConstant.java, v 0.1 2023-10-07 9:28 AM ruanzy
 */
public class KsOpenConstant {

    /**
     * 获取快手AccessToken的url
     */
    public static final String KS_ACCESS_TOKEN_URL = "https://open.kuaishou.com/oauth2/access_token?app_id={}&app_secret={}&code={}&grant_type=authorization_code";

    /**
     * 获取快手AccessToken的url
     */
    public static final String KS_REFRESH_ACCESS_TOKEN_URL = "https://open.kuaishou.com/oauth2/refresh_token?app_id={}&app_secret={}&refresh_token={}&grant_type=refresh_token";

    /**
     * 获取快手用户信息的url
     */
    public static final String KS_USER_INFO_URL = "https://open.kuaishou.com/openapi/user_info?app_id={}&access_token={}";

    /**
     * 查询单一视频详情
     */
    public static final String KS_USER_VIDEO_INFO_URL = "https://open.kuaishou.com/openapi/photo/info?app_id={}&access_token={}&photo_id={}";

    /**
     * 发起上传
     */
    public static final String KS_START_UPLOAD_URL = "https://open.kuaishou.com/openapi/photo/start_upload?access_token={}&app_id={}";

    /**
     * Multipart Form Data 方式上传视频
     */
    public static final String KS_UPLOAD_MULTIPART_URL = "http://{}/api/upload/multipart?upload_token={}";

    /**
     * 发布视频
     */
    public static final String KS_PUBLISH_USER_VIDEO_URL = "https://open.kuaishou.com/openapi/photo/publish?access_token={}&app_id={}&upload_token={}";

    /**
     * 快手获取网页登录授权地址
     */
    public static final String KS_AUTHORIZE_URL = "https://open.kuaishou.com/oauth2/authorize?app_id={}&scope={}&response_type=code&redirect_uri={}";

    /**
     * 快手视频链接地址
     */
    public static final String KS_VIDEO_URL = "https://www.kuaishou.com/short-video/{}";

    /**
     * 快手发布视频异步状态缓存
     */
    public static final String KS_PUBLISH_VIDEO_STATUS_REDIS_KEY = "ks.publish.video.status.redis.key.{}";
}