package com.huike.nova.common.util.net;

import lombok.Getter;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;

/**
 * 信任主机
 *
 * <AUTHOR> (<EMAIL>)
 * @version NovaHostnameVerifier.java, v1.0 08/24/2023 16:57 John Exp$
 */
@Getter
public class NovaHostnameVerifier implements HostnameVerifier {

    public static HostnameVerifier instance() {
        return INSTANCE;
    }

    private static final NovaHostnameVerifier INSTANCE = new NovaHostnameVerifier();

    private NovaHostnameVerifier() {

    }

    @Override
    public boolean verify(String s, SSLSession sslSession) {
        return true;
    }
}
