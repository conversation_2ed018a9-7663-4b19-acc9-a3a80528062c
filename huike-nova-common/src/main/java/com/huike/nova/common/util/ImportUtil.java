/*
 * zhuyanyoushu.com
 * Copyright (C) 2021-${YEAR} All Rights Reserved.
 *
 */
package com.huike.nova.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.excel.EasyExcel;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 导入公共服务
 *
 * <AUTHOR>
 * @version ImportCommonService.java, v 0.1 2022-05-09 19:33 卜浩
 */
@Slf4j
public class ImportUtil {
    /**
     * 下载文件基础dir
     */
    private static final File DOWNLOAD_FILE_BASE_DIR = FileUtil.file(FileUtil.getUserHomePath() + File.separator + "download" + File.separator);


    /**
     * 文件处理
     * warn: 数据量大的情况下，不要使用些方法，请通过监听器解析
     * 另外只支持简单头信息，不支持复杂头信息
     *
     * @param fileUrl  文件url
     * @param rowClass clazz列
     * @return {@link List }<{@link T }>
     * <AUTHOR>
     */
    @Deprecated
    public static <T> List<T> fileDealWithSync(String fileUrl, Class<T> rowClass) {
        File file = downloadFile(fileUrl);
        try {
            return parseFileWithSync(file, rowClass);
        } finally {
            if (file.exists()) {
                FileUtil.del(file);
            }
        }
    }

    /**
     * 生成失败文件
     *
     * @param failureList 客户信息列表失败
     * @return {@link File }
     * <AUTHOR>
     */
//    public static <T> String generateFailureFile(List<T> failureList, Class<T> rowFailureClass) {
//        // 生成错误文件
//        File writeFile = FileUtil.file(DOWNLOAD_FILE_BASE_DIR,
//                "导入失败数据_" + DateUtil.date().toString("yyyyMMddHHmmss") + ".xlsx");
//        EasyExcel.write(writeFile).head(rowFailureClass).sheet("错误数据").doWrite(failureList);
//        // 上传到阿里云
//        String url = SpringUtil.getBean(OssManager.class).uploadFile(writeFile, UploadPathTypeEnum.TEMP);
//        // 删除本地文件
//        if (writeFile.exists()) {
//            FileUtil.del(writeFile);
//        }
//        return url;
//    }

    /**
     * 下载文件
     *
     * @param url url
     * @return {@link File }
     * <AUTHOR>
     */
    private static File downloadFile(String url) {
        try {
            return HttpUtil.downloadFileFromUrl(url, FileUtil.file(DOWNLOAD_FILE_BASE_DIR,
                    RandomUtil.randomString(10) + ".xlsx"));
        } catch (Exception e) {
            LogUtil.error(log, "downloadFile >> 文件下载失败 >> ex = {},  url = {}", e, url);
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("文件下载失败");
        }
    }

    /**
     * 解析文件(同步)
     * warn: 数据量大的情况下，不要使用些方法，请通过监听器解析
     *
     * @param file  文件
     * @param clazz clazz
     * @return {@link List }<{@link T }>
     * <AUTHOR>
     */
    @Deprecated
    public static <T> List<T> parseFileWithSync(File file, Class<T> clazz) {
        List<T> result;
        try {
            result = ignoreNullRow(EasyExcel.read(file).ignoreEmptyRow(true).head(clazz).sheet().doReadSync());
        } catch (Exception e) {
            LogUtil.error(log, "parseFileWithSync >> 文件解析失败 >> ex = {},  file = {}, clazz = {}", e, file, clazz);
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("文件解析失败");
        }

        // 如果解析结果为空
        if (CollUtil.isEmpty(result)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("无可导入数据");
        }

        return result;
    }

    /**
     * 忽略空行
     * 底层判断有bug,要手动判断一下 https://github.com/alibaba/easyexcel/issues/2294
     *
     * @param rows 行
     * @return {@link List }<{@link T }>
     * <AUTHOR>
     */
    private static <T> List<T> ignoreNullRow(List<T> rows) {
        // 如果为空，直接返回
        if (CollUtil.isEmpty(rows)) {
            return Lists.newArrayList();
        }
        return rows.stream().filter(it -> Arrays.stream(ReflectUtil.getFieldsValue(it)).anyMatch(Objects::nonNull)).collect(Collectors.toList());
    }

    /**
     * 读取文件内容
     *
     * @param filePath 文件地址
     * @param clazz
     * @return {@link List<T>}
     * <AUTHOR>
     */
    public static <T> List<T> read(String filePath, final Class<?> clazz, String sheetNo) {
        File f = downloadFile(filePath);
        try (FileInputStream fis = new FileInputStream(f)) {
            return read(fis, clazz, sheetNo);
        } catch (FileNotFoundException e) {
            LogUtil.error(log, "文件{}不存在", filePath, e);
        } catch (IOException e) {
            LogUtil.error(log, "文件读取出错", e);
        }
        return null;
    }

    /**
     * 读取文件内容
     *
     * @param inputStream 文件流
     * @param clazz
     * @param sheetNo     sheet名称
     * @return {@link List<T>}
     * <AUTHOR>
     */
    public static <T> List<T> read(InputStream inputStream, final Class<?> clazz, String sheetNo) {
        if (inputStream == null) {
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("解析出错了，文件流是null");
        }
        // 有个很重要的点 DataListener 不能被spring管理，要每次读取excel都要new,然后里面用到spring可以构造方法传进去
        DataListener<T> listener = new DataListener<>();
        // 指定读用哪个class去读，然后读取sheet 文件流会自动关闭
        if (ObjectUtil.isNull(sheetNo)) {
            EasyExcel.read(inputStream, clazz, listener).ignoreEmptyRow(true).sheet().doRead();
        } else {
            EasyExcel.read(inputStream, clazz, listener).ignoreEmptyRow(true).sheet(sheetNo).doRead();
        }
        return ignoreNullRow(listener.getRows());
    }

}