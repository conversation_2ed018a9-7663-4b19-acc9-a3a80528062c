/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version CacheConstants.java, v 0.1 2022-11-17 11:28 wangyi
 */
public class CacheConstants {

    public static final String CHANNEL_INFO_REDIS_KEY = "channel.info.{}";

    public static final String CHANNEL_TIKTOK_CONFIG_KEY = "channel.tiktok.config.{}";

    public static final String CHANNEL_TAX_CONFIG_KEY = "channel.tax.config.{}";

    /**
     * 抖音小程序accessToken公共缓存key
     */
    public static final String TIKTOK_MINA_CLIENT_TOKEN_COMMON_KEY = "tiktok.mina.client.token.redis.common.key.{}";

    /**
     * 抖音小程序accessToken缓存时间
     */
    public static final long TIKTOK_MINA_CLIENT_TOKEN_KEY_TIME = 6600L;

    /**
     * 修改商品时缓存的商品key
     */
    public static final String MODIFY_PRODUCT_CACHE_DATA_KEY = "PRODUCT_ID_{}";

    /**
     * 微信小程序accessToken缓存时间
     */
    public static final long WX_MINA_CLIENT_TOKEN_KEY_TIME = 7100L;

    /**
     * 取消服务通知代理商KEY
     */
    public static final String MERCHANT_CANCEL_SERVICE_KEY = "SERVICE_ORDER_PHONE_NUMBER_{}";
}