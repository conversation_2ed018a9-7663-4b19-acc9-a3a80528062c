/**
 * <AUTHOR>
 * @date 2024/5/27 18:01
 * @version 1.0 FsFileUtils
 */
package com.huike.nova.common.util;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;

/**
 * <AUTHOR>
 * @version FsFileUtils.java, v 0.1 2024-05-27 18:01
 */
@Slf4j
public class FsFileUtils {

    /**
     * 下载文件到本地
     *
     * @param url
     * @return
     * @throws Exception
     */
    public static File getFile(String url) {
        //对本地文件命名
        String fileName = url.substring(url.lastIndexOf("."), url.length());
        if (fileName.contains("/")) {
            fileName = ".png";
        }
        File file = null;

        URL urlfile;
        InputStream inStream = null;
        OutputStream os = null;
        try {
            file = File.createTempFile("product", fileName);
            //下载
            urlfile = new URL(url);
            inStream = urlfile.openStream();
            os = new FileOutputStream(file);

            int bytesRead = 0;
            byte[] buffer = new byte[8192];
            while ((bytesRead = inStream.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            LogUtil.error(log, "getFile >> 下载文件异常1 >> url={}, msg={}", url, e.getStackTrace());
        } finally {
            try {
                if (null != os) {
                    os.close();
                }
                if (null != inStream) {
                    inStream.close();
                }

            } catch (Exception e) {
                LogUtil.error(log, "getFile >> 下载文件异常2 >> url={}, msg={}", url, e.getStackTrace());
            }
        }
        return file;
    }
}