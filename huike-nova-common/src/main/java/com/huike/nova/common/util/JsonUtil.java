package com.huike.nova.common.util;

import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.val;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;

/**
 * JSON工具
 *
 * <AUTHOR> (<EMAIL>)
 * @version JsonUtil.java, v1.0 06/18/2023 18:06 John Exp$
 */
public class JsonUtil {
    /** 空JSON对象 */
    public static final JSONObject EMPTY_JSON_OBJECT = new JSONObject();

    /**
     * 重新设置JSON，将args中的字符串转为JSON对象7
     *
     * @param jsonString json字符串
     * @param args 参数
     * @return 新的JSON
     */
    @SuppressWarnings("UnusedReturnValue")
    public static JSONObject assignSubJsonObject(String jsonString, String... args) {
        val json = JSONArray.parseObject(jsonString);
        for (val key: args) {
            checkAndAssignKeyObject(json, key);
        }
        return json;
    }

    /**
     * 重新设置JSON，将args中的字符串转为JSON对象7
     *
     * @param jsonString json字符串
     * @param args 参数
     * @return 新的JSON
     */
    public static JSONObject assignSubJsonObject(String jsonString, Collection<String> args) {
        val json = JSONArray.parseObject(jsonString);
        for (val key: args) {
            checkAndAssignKeyObject(json, key);
        }
        return json;
    }

    /**
     * 检查并设置Key对应的类型为object
     *
     * @param json json对象
     * @param key key对象
     * @return 新的JSON对象
     */
    @SuppressWarnings("UnusedReturnValue")
    private static JSONObject checkAndAssignKeyObject(JSONObject json, String key) {
        if (ObjectUtil.isNull(json)) {
            return EMPTY_JSON_OBJECT;
        }
        if (json.containsKey(key)) {
            // 获得Key对应的jsonString
            val childJsonString = StringUtils.defaultIfBlank(json.getString(key), StrPool.EMPTY_JSON);
            val childJson = JSONArray.parseObject(childJsonString);
            json.put(key, childJson);
        }
        return json;
    }
}
