/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.util;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.annimon.stream.function.Supplier;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.constant.SystemConstants;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.InetAddress;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version WebUtil.java, v 0.1 2022-09-03 4:01 PM ruanzy
 */
public class WebUtil {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    private static final Pattern PATTERN_HTTPS = Pattern.compile("(http|https)://[\\w\\-_]+(\\.[\\w\\-_]+)+([\\w\\-.,@?^=%&:/~+#]*[\\w\\-@?^=%&/~+#])?");

    static {
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    /**
     * 将对象转换为json字符串发送给客户端
     *
     * @param response
     * @param responseData
     * @throws JsonProcessingException
     */
    public static void sendResponse(HttpServletResponse response, Object responseData) throws JsonProcessingException {
        ServletUtil.write(response,
                OBJECT_MAPPER.writeValueAsString(responseData),
                SystemConstants.HTTP_APPLICATION_JSON_UTF8_VALUE);
    }

    /**
     * 检查Ip是否可用
     *
     * @return true可用，false不可用
     */
    private static boolean preCheckIpValid(CharSequence ip) {
        // 将空字符串或者IP转为UNKNOWN
        return !StringUtils.equalsIgnoreCase(
                StringUtils.defaultIfBlank(ip, SystemConstants.UNKNOWN),
                SystemConstants.UNKNOWN
        );
    }

    /**
     * 获得请求真实IP地址
     *
     * @return 真实IP地址，如果不存在则返回空
     */
    public static String getRequestIp() {
        return getRequestIp(null);
    }

    /**
     * 获得请求真实IP地址
     *
     * @param servletRequest 请求
     * @return 真实IP地址，如果不存在则返回空
     */
    public static String getRequestIp(@Nullable HttpServletRequest servletRequest) {
        try {
            val request = ObjectUtil.defaultIfNull(
                    servletRequest,
                    ((ServletRequestAttributes) Objects.requireNonNull(RequestContextHolder.getRequestAttributes())).getRequest()
            );
            // 检查squid 或者 nginx的代理头
            String ip = request.getHeader(SystemConstants.X_FORWARDED_FOR);
            if (!preCheckIpValid(ip)) {
                // 检查apache代理头
                ip = request.getHeader(SystemConstants.PROXY_CLIENT_IP);
            }
            if (!preCheckIpValid(ip)) {
                // 检查apache webLogic代理通
                ip = request.getHeader(SystemConstants.WL_PROXY_CLIENT_IP);
            }
            if (!preCheckIpValid(ip)) {
                // 直接获得地址
                ip = request.getRemoteAddr();
                if (StringUtils.equals(SystemConstants.LOOP_BACK_IP, ip)) {
                    // 根据网卡取本机配置的IP
                    ip = Supplier.Util.safe(() -> InetAddress.getLocalHost().getHostAddress(), StrUtil.EMPTY).get();
                }
            }
            // 通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
            if (StringUtils.isNotBlank(ip)) {
                if (ip.contains(StrUtil.COMMA)) {
                    return StringUtils.split(ip, StrUtil.COMMA)[0];
                } else {
                    return ip;
                }
            }
        } catch (Exception ignored) {
        }
        return StrUtil.EMPTY;
    }

    /**
     * 获得UserAgent
     *
     * @param servletRequest servletRequest
     * @return UA，如果不存在则返回空字符串
     */
    public static String getUserAgent(@Nullable HttpServletRequest servletRequest) {
        String userAgent = StringPool.EMPTY;
        try {
            HttpServletRequest httpServletRequest = servletRequest;
            if (Objects.isNull(httpServletRequest) && RequestContextHolder.getRequestAttributes() instanceof ServletRequestAttributes) {
                httpServletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            }
            Objects.requireNonNull(httpServletRequest);
            userAgent = httpServletRequest.getHeader(CommonConstant.HEADER_USER_AGENT);
        } catch (Exception ignored) {
        }
        return userAgent;
    }

    /**
     * 从短句子中获得URL
     *
     * @param phrase 包含短句的Url
     * @return url地址
     */
    @Nullable
    public static String getUrlFromPhrase(String phrase) {
        val n = PATTERN_HTTPS.matcher(phrase);
        if (n.find()) {
            return n.group(0);
        }
        return null;
    }
}