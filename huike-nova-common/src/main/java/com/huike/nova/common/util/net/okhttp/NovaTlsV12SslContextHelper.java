package com.huike.nova.common.util.net.okhttp;

import com.google.common.annotations.Beta;
import com.huike.nova.common.util.LogUtil;
import com.huike.nova.common.util.net.NovaTrustManager;
import lombok.extern.slf4j.Slf4j;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;

/**
 * <AUTHOR> (<EMAIL>)
 * @version NOP_TLSV12_SSL_CONTEXT.java, v1.0 08/24/2023 17:00 John Exp$
 */
@Slf4j
@Beta
public class NovaTlsV12SslContextHelper {

    public static SSLContext sslContext() {
        return new NovaTlsV12SslContextHelper().sslContext;
    }

    private SSLContext sslContext;

    private NovaTlsV12SslContextHelper() {
        try {
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[] { NovaTrustManager.instance() }, new java.security.SecureRandom());
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            LogUtil.info(log, "NovaTlsV12SslContextHelper.constructor() >> 创建SSL对象错误，", e);
        }
    }
}
