package com.huike.nova.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 林客通道权限码映射
 *
 * <AUTHOR> (<EMAIL>)
 * @version LinkConnectGrantCode2ChannelCodeConfig.java, v1.0 2025-07-21 20:11 John Exp$
 */
@Component
@Data
@ConfigurationProperties(prefix = "gosh.link-connect-grant-code-map")
public class LinkConnectGrantCode2ChannelCodeConfig {

    private Map<String, String> properties;
}
