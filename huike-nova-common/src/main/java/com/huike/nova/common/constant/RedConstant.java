/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.constant;


/**
 * <AUTHOR>
 * @version RedConstant.java
 */
public class RedConstant {

    /**
     * 小红书token缓存的前缀
     */
    public static final String RED_TOKEN_REDIS_PRE = "red.mina.access_token.{}";
    /**
     * 获取token的地址
     */
    public static final String METHOD_GET_TOKEN_URL = "https://miniapp.xiaohongshu.com/api/rmp/token";

    /**
     * 商品保存
     */
    public static final String METHOD_PRODUCT_SAVE_URL = "https://miniapp.xiaohongshu.com/api/rmp/mp/deal/poi/product/upsert?app_id={}&access_token={}";

    /**
     * 商品下架
     */
    public static final String METHOD_PRODUCT_OFFLINE_URL = "https://miniapp.xiaohongshu.com/api/rmp/mp/deal/product/sku/batch_offline?app_id={}&access_token={}";

    /**
     * 商品删除
     */
    public static final String METHOD_PRODUCT_DELETE_URL = "https://miniapp.xiaohongshu.com/api/rmp/mp/deal/product/sku/batch_delete?app_id={}&access_token={}";


    /**
     * 商品查询
     */
    public static final String METHOD_PRODUCT_QUERY_URL = "https://miniapp.xiaohongshu.com/api/rmp/mp/deal/product/get?app_id={}&access_token={}";

    /**
     * 结算数据查询
     */
    public static final String METHOD_SETTLE_DATA_QUERY_URL = "https://miniapp.xiaohongshu.com/api/rmp/mp/deal/check_settle_info?app_id={}&access_token={}";

    /**
     * 订单查询接口URL
     */
    public static final String ORDER_QUERY_URL = "https://miniapp.xiaohongshu.com/api/rmp/mp/deal/gpay_order/get?app_id={}&access_token={}";

    /**
     * 凭证核销接口URL
     */
    public static final String VOUCHER_VERIFY_URL = "https://miniapp.xiaohongshu.com/api/rmp/mp/deal/voucher/verify?app_id={}&access_token={}";
}