package com.huike.nova.common.constant;

/**
 * 企业应用常量
 *
 * <AUTHOR> (<EMAIL>)
 * @version EnterpriseConstant.java, v1.0 07/04/2023 14:18 John Exp$
 */
public class EnterpriseAppConstant {

    /**
     * 授权状态
     */
    public static final String EMPOWER_STATUS = "empowerStatus";

    /**
     * 错误原因
     */
    public static final String ERROR_REASON = "errReason";

    /**
     * 昵称
     */
    public static final String NICK_NAME = "nickName";

    /**
     * 商户Id
     */
    public static final String MERCHANT_ID = "merchantId";

    /**
     * 门店Id
     */
    public static final String STORE_ID = "storeId";

    /**
     * 绑定Id
     */
    public static final String SOURCE = "source";

    /**
     * 客户端Id
     */
    public static final String CLIENT_KEY = "clientKey";

    /**
     * 代理商Id
     */
    public static final String AGENT_MCN_ID = "agentMcnId";

    /**
     * 授权成功
     */
    public static final int AUTH_SUCCESS_CODE = 1;

    /**
     * 授权失败
     */
    public static final int AUTH_FAILED_CODE = 2;

    /**
     * 接口发布视频必要权限
     */
    public static final String[] BIND_SCOPES = {"video.create.bind", "video.data.bind"};

    /**
     * 视频发布权限
     */
    public static final String VIDEO_CREATE_BIND = "video.create.bind";

    /**
     * 视频查询权限
     */
    public static final String VIDEO_DATA_BIND = "video.data.bind";

    /**
     * 基本经营能力
     */
    public static final String BASIC_BC = "basic_bc";

    /**
     * oem类型
     */
    public static final String OEM_TYPE = "oemType";
}
