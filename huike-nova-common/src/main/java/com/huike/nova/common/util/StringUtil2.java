package com.huike.nova.common.util;

import com.alibaba.excel.util.StringUtils;
import com.huike.nova.common.constant.StringPool;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 字符串工具类
 *
 * <AUTHOR> (<EMAIL>)
 * @version StringUtil.java, v1.0 01/21/2024 16:21 John Exp$
 */
public class StringUtil2 {
    /**
     * 使用星号替换字符串，仅保留头尾字符
     *
     * @param input 输入字符串
     * @return 返回结果
     */
    public static String replaceWithAsterisk(String input) {
        if (StringUtils.isBlank(input)) {
            return StringPool.EMPTY;
        }
        if (input.length() == 1) {
            return StringPool.ASTERISK;
        } else if(input.length() == 2) {
            return input.charAt(0) + StringPool.ASTERISK;
        }
        String regex = "(?<=.).(?=.)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(input);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, "*");
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
}
