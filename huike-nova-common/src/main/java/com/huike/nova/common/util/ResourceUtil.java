/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.common.util;

import org.apache.tomcat.util.http.fileupload.IOUtils;

import java.io.ByteArrayOutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * <AUTHOR>
 * @version ResourceUtil.java, v 0.1 2022-09-09 14:31 zhangling
 */
public class ResourceUtil {

    private static final int MIN_SIZE = 1000;
    /**
     * 根据地址获得数据的输入流字节数
     * @param strUrl
     * @return
     */
    public static int getBytLengthByUrl(String strUrl){
        HttpURLConnection conn = null;
        try {
            //创建URL资源
            URL url = new URL(strUrl);
            //链接远程资源，并返回URLConnection
            conn = (HttpURLConnection)url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(2 * 1000);
            final ByteArrayOutputStream output = new ByteArrayOutputStream();
            IOUtils.copy(conn.getInputStream(),output);
            byte[] bytes = output.toByteArray();
            return bytes.length;
        } catch (Exception ignore) {
            return 0;
        }finally {
            try{
                if (conn != null) {
                    conn.disconnect();
                }
            }catch (Exception ignore){
            }
        }
    }

    /**
     * 判断URL资源是否有效(判断逻辑，当资源大于1K，则为有效资源，否则为无效资源)
     * @param resourceUrl
     * @return
     */
    public static boolean validateResourceUrl(String resourceUrl){
        int byteLength = getBytLengthByUrl(resourceUrl);
        //判断逻辑，当资源大于1K，则为有效资源，否则为无效资源
        if(byteLength > MIN_SIZE){
            return true;
        }
        return false;
    }
}