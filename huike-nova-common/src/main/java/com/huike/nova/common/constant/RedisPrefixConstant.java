/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.constant;

import org.redisson.client.codec.StringCodec;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @version RedisPrefixConstant.java, v 0.1 2022-09-01 11:44 AM ruanzy
 */
public class RedisPrefixConstant {

    /**
     * 默认的字符串编码
     */
    public static final StringCodec DEFAULT_REDIS_STRING_CODEC = new StringCodec(StandardCharsets.UTF_8);

    /**
     * 接口拦截器nonce-redis值
     */
    public static final String INTERFACE_NONCE_REDIS_KEY = "interface.nonce.redis.key.{}";
    /**
     * 闭环订单下单回调处理
     */
    public static final String LIFE_TRADE_ORDER_NOTIFY_KEY = "life.trade.order.notify.key.{}";
    /**
     * 闭环券核销回调
     */
    public static final String LIFE_TRADE_CERTIFICATE_NOTIFY_KEY = "life.trade.certificate.notify.key.{}";
    /**
     * 授权绑定通知回调
     */
    public static final String LIFE_SAAS_COOPERATE_AUTH_WITH_BIND = "life.saas.cooperate.auth.with.bind.notify.key.{}";

    /**
     * 推送更新达人信息的RedisQueueKey
     *
     * @deprecated 已过时，使用SysConfig.getRefreshUserInfoQueueKey()方法代替
     */
    @Deprecated
    public final static String REFRESH_CREATOR_INFO_KEY = "rpa.refresh.creator.info.quene.key";
    /**
     * 抖音client_token-redis值
     */
    public static final String TIKTOK_CLIENT_TOKEN_REDIS_KEY = "tiktok.client.token.redis.key";


    /**
     * 抖音service_client_token-redis值
     */
    public static final String TIKTOK_SERVICE_CLIENT_TOKEN_REDIS_KEY = "tiktok.service.client.token.redis.key.{}";


    /**
     * 抖音web_client_token-redis值
     */
    public static final String TIKTOK_WEB_CLIENT_TOKEN_REDIS_KEY = "tiktok.web.client.token.redis.key";

    /**
     * 抖音jsb_ticket-redis值
     */
    public static final String TIKTOK_JSB_TICKET_REDIS_KEY = "tiktok.jsb.ticket.redis.key";

    /**
     * 抖音open_ticket-redis值
     */
    public static final String TIKTOK_OPEN_TICKET_REDIS_KEY = "tiktok.open.ticket.redis.key";

    /**
     * 预设人声
     */
    public static final String PRESET_ICE_VOICE_KEY = "preset.ice.voice.key.{}";

    /**
     * 登录信息
     */
    public static final String KEY_LOGIN_INFO_ACCESS_TOKEN = "key.login.info.{}";

    /**
     * 抖音话题redis
     */
    public static final String TIKTOK_OPEN_TOPIC_REDIS_KEY = "tiktok.open.topic.redis.key";

    /**
     * 抖音热词redis
     */
    public static final String TIKTOK_OPEN_SENTENCE_REDIS_KEY = "tiktok.open.sentence.redis.key";

    /**
     * 门店信息更新同步锁
     */
    public static final String LOCK_SERVING_STORE_BATCH = "lock.serving.tiktok.store.batch";

    /**
     * 活动数据日统计同步锁
     */
    public static final String LOCK_SERVING_ACTIVITY_STATISTICS_DAY = "lock.serving.activity.statistics.day";

    /**
     * 商品数据日统计同步锁
     */
    public static final String LOCK_SERVING_GOODS_STATISTICS_DAY = "lock.serving.goods.statistics.day";

    /**
     * 核销数据日统计同步锁
     */
    public static final String LOCK_SERVING_VERIFY_STATISTICS_DAY = "lock.serving.verify.statistics.day";

    /**
     * 刷新抖音热词同步锁
     */
    public static final String LOCK_SERVICE_TIKTOK_SENTENCE = "lock.service.tiktok.sentence";

    /**
     * 核销回调同步锁
     */
    public static final String LOCK_VERIFY_CALL_BACK = "lock.verify.call.back:{}";

    /**
     * 撤销核销回调同步锁
     */
    public static final String LOCK_CANCEL_VERIFY_CALL_BACK = "lock.cancel.verify.call.back:{}";

    /**
     * 添加视频组合关系
     */
    public static final String LOCK_SERVICE_ADD_VIDEO_COMBINATION = "lock.service.add.video.combination.{}";

    /**
     * 刷新抖音refresh_token同步锁
     */
    public static final String LOCK_SERVING_REFRESH_TOKEN = "lock.serving.refresh.token";

    /**
     * 刷新用户抖音发布视频数据
     */
    public static final String LOCK_SERVING_REFRESH_USER_VIDEO = "lock.serving.refresh.user.video";

    /**
     * 刷新用户抖音发布视频数据
     */
    public static final String LOCK_SERVING_REFRESH_USER_VIDEO_V2 = "lock.serving.refresh.user.video.v2";

    /**
     * 同步活动数据
     */
    public static final String LOCK_SERVING_SYNC_ACTIVITY_DATA = "lock.serving.sync.activity.data";

    /**
     * 发布视频幂等
     */
    public static final String CREATE_SCHEMA_REDIS_KEY = "create.schema.redis.key.{}";

    /**
     * 抖音话题刷新同步锁
     */
    public static final String LOCK_SERVING_TIKTOK_TOPIC_REFRESH = "lock.serving.tiktok.topic.refresh";

    /**
     * 活动可用视频限制同步锁
     */
    public static final String LOCK_SERVING_ACTIVITY_VIDEO_LIMIT = "lock.serving.activity.video.limit.{}";

    /**
     * 获取预合成视频同步锁
     */
    public static final String LOCK_SERVING_ACTIVITY_PRE_VIDEO_GET_KEY = "lock.serving.activity.pre.video.get.key.{}";

    /**
     * 更新客资表同步锁
     */
    public static final String LOCK_SERVING_UPDATE_CUSTOMER_DATA_GET_KEY = "lock.serving.update.customer.data.synthesis.key.{}.{}";

    /**
     * 库存操作同步锁
     */
    public static final String LOCK_SERVING_OPERATE_STOCK_KEY = "lock.serving.operate.stock.key.{}";

    /**
     * 更新口播url锁
     */
    public static final String LOCK_BATCH_UPDATE_SOUND_URL_KEY = "lock.batch.update.sound.url.key.{}";

    /**
     * 更新智播通口播文字锁
     */
    public static final String LOCK_BATCH_UPDATE_ZBT_SOUND_CONTENT_KEY = "lock.batch.update.zbt.sound.content.key.{}";

    /**
     * 获取预合成视频同步锁
     */
    public static final String LOCK_SERVING_ACTIVITY_PRE_VIDEO_SYNTHESIS_KEY = "lock.serving.activity.pre.video.synthesis.key.{}";

    /**
     * 活动可用视频限制同步锁
     */
    public static final String LOCK_SERVING_RPA_ACTIVITY_VIDEO_LIMIT = "lock.serving.rpa.activity.video.limit.{}";

    /**
     * 活动可用视频限制同步锁
     */
    public static final String LOCK_ADD_STAR = "lock.add.star.{}";

    /**
     * rpa视频发布队列
     *
     * @deprecated 已过时，使用sysConfig.getPushRpaVideoQueueKey方法代替
     */
    @Deprecated
    public static final String RPA_VIDEO_RELEASE_QUEUE_KEY = "rpa.video.release.quene.key";

    /**
     * 活动可用视频数量redis
     */
    public static final String ACTIVITY_VIDEO_NUMBER_REDIS_KEY = "activity.video.number.redis.key.{}";

    /**
     * 查询视频合成进度最大次数
     */
    public static final String ACTIVITY_QUERY_RATE_REDIS_KEY = "activity.query.rate.redis.key.{}";

    /**
     * 顾客推抖音数据更新限制
     */
    public static final String SUP_ACTIVITY_TIKTOK_VIDEO_DATA_UPDATE_LIMIT_KEY = "sup.activity.tiktok.video.data.update.limit.key.{}.{}";

    /**
     * 短信发送缓存key(用于风控)
     * 使用时需要第一个替换成场景类型
     * 第二个替换成手机号
     */
    public static final String CACHE_KEY_SMS_REQUEST = "cache.sms.request.{}.{}";

    /**
     * 短信发送缓存key（用于验证）
     * 使用时需要第一个替换成场景类型
     * 第二个替换成手机号
     */
    public static final String CACHE_KEY_SMS_VERIFY = "cache.sms.verify.{}.{}";

    /**
     * 短信发送次数缓存key（用于风控）
     * 使用时需要第一个替换成场景类型
     * 第二个替换成手机号
     */
    public static final String CACHE_KEY_SMS_REQUEST_COUNT = "cache.sms.request.count.{}.{}";

    /**
     * 员工新增同步锁
     */
    public static final String LOCK_SERVING_EMPLOYEE_ADD = "lock.serving.employee.add.{}";

    /**
     * 订单轮巡推送缓存key
     * {0}->orderSn
     */
    public static final String ORDER_CYCLE_PUSHL_CACHE_KEY = "qrordering.order.cycle.push.{}";


    /**
     * 退款订单同步状态同步锁
     */
    public static final String LOCK_SERVING_REFUND_ORDER_SYNC_STATUS = "lock.serving.refund.order.sync.status";


    /**
     * 小程序抖音client_token-redis值
     */
    public static final String TIKTOK_MINA_CLIENT_TOKEN_REDIS_KEY = "tiktok.mina.client.token.redis.key";

    /**
     * 抖音小程序accessToken公共缓存key
     */
    public static final String TIKTOK_MINA_CLIENT_TOKEN_COMMON_KEY = CacheConstants.TIKTOK_MINA_CLIENT_TOKEN_COMMON_KEY;

    /**
     * 高德券核销锁
     */
    public static final String LOCK_KOUBEI_COUPON_VERIFY = "lock.koubei.coupon.verify.{}.{}";

    /**
     * 来团呗首页更新时间
     */
    public static final String APP_HOME_UPDATE_TIME = "app.home.update.time";

    /**
     * 微信小程序缓存前缀
     */
    public static final String CACHE_KEY_WECHAT_MINAPP = "CACHE_KEY_WECHAT_MINAPP";

    /**
     * 支付回调
     */
    public static final String ORDER_PAY_CALLBACK_LOCK_ORDER_SN = "mina.order.pay.callback.{}";

    /**
     * 给服务商来团呗账套数量锁
     */
    public static final String LOCK_RECHARGE_AGENT_LTB_ACCOUNT = "lock.recharge.agent.ltb.account.{}";

    /**
     * 服务商给商家充值来团呗账套（此值需要于智荐保持一致）
     */
    public static final String LOCK_RECHARGE_STORE_LTB_ACCOUNT = "lock.recharge.store.ltb.account.{}";

    /**
     * 达人分配操作锁
     */
    public static final String LOCK_STAR_DISTRIBUTION_HANDLE_AGENT_KEY = "lock.star.distribution.handle.agent.{}";

    /**
     * 达人分组操作锁（代理商维度）
     */
    public static final String LOCK_STAR_GROUP_HANDLE_AGENT_KEY = "lock.star.group.handle.agent.{}";

    /**
     * 达人分组操作锁（门店维度）
     */
    public static final String LOCK_STAR_GROUP_HANDLE_STORE_KEY = "lock.star.group.handle.store.{}";
    /**
     * 达人推防止平台达人超用（门店维度）
     */
    public static final String LOCK_STAR_PUSH_STORE_KEY = "lock.star.push.store.{}";
    /**
     * 达人推自动发布消息回调
     */
    public static final String LOCK_STAR_PUSH_AUTO_PUBLISH = "lock.star.push.auto.publish.{}";

    /**
     * 视频发布抖音-获取视频锁-剪辑任务维度
     */
    public static final String LOCK_VIDEO_PUBLISH_GET_KEY = "lock.video.publish.get.{}";
    /**
     * 达人推补充素材自动发布活动消息回调
     */
    public static final String LOCK_STAR_PUSH_SUPPLY_MATERIAL = "lock.star.push.supply.material.{}";
    /**
     * 达人推视频发布失败重试
     */
    public static final String LOCK_STAR_PUSH_FAILED_RETRY = "lock.star.push.failed.retry.{}";

    /**
     * 达人每天发布三次视频校验
     */
    public static final String LOCK_STAR_PUSH_VIDEO = "lock.star.push.video";

    /**
     * 撤销核销加锁
     */
    public static final String LOCK_AUTH_SERVICE_PROVIDER_CANCEL_VERIFY_COUPON = "lock.auth.service.provider.cancel.verify.coupon.{}";

    /**
     * 来团呗撤销核销加锁
     */
    public static final String LOCK_LTB_PROVIDER_CANCEL_VERIFY_COUPON = "lock.ltb.provider.cancel.verify.coupon.{}";

    /**
     * 核销加锁
     */
    public static final String LOCK_AUTH_SERVICE_PROVIDER_VERIFY_COUPON = "lock.auth.service.provider.verify.coupon.{}";

    /**
     * 顾客推抖音数据更新限制
     */
    public static final String SUP_ACTIVITY_TIKTOK_VIDEO_DATA_UPDATE_KEY = "sup.activity.tiktok.video.data.update.key.{}";


    /**
     * 商户账号使用次数
     */
    public static final String CHAT_GPT_API_ACCOUNT_COUNT_KEY = "chat.gpt.api.account.count.key.{}";

    /**
     * 小程序商品用户限购锁
     */
    public static final String LOCK_SERVING_GOODS_LIMIT_CUSTOMER_KEY = "lock.serving.goods.limit.customer.key.{}.{}";


    /**
     * 自增主键redisKey
     */
    public static final String HUIKE_INCR_BUSINESS_PRIMARY_KEY = "huike.incr.business.primary.key";


    /**
     * chatGPT的token列表
     */
    public static final String CHAT_GPT_API_TOKEN_LIST_KEY = "chat.gpt.api.token.list.key";

    /**
     * URL Ticket - URLTicket临时存放
     */
    public static final String URL_TICKET_MATRIX_BIND = "url.ticket.{}";

    /**
     * URL Ticket 生成锁，需保证Key唯一
     */
    public static final String LOCK_URL_TICKET = "lock.enterprise.url-ticket";

    /**
     * 阿里智能语音交互AccessToken
     */
    public static final String ALI_INTELLIGENT_SPEECH_INTERACTION_ACCESS_TOKEN_KEY = "ali.intelligent.speech.interaction.access.token.key";

    /**
     * 宝龙发货短信发送缓存key(用于风控)
     * 第一个替换成场景类型
     * 第二个替换成手机号
     */
    public static final String POWER_LONG_CACHE_KEY_SMS_REQUEST = "power.long.cache.sms.request.{}.{}";

    /**
     * 集团（包含宝龙和绿地）新增会员同步锁
     * 第一个替换channolCode
     * 第二个替换成手机号
     */
    public static final String POWER_LONG_LOCK_SERVING_SAVE_MEMBER_DATA_KEY = "power.long.lock.serving.save.member.data.key.{}.{}";

    /**
     * 宝龙发送短信锁
     */
    public static final String POWER_LONG_LOCK_SERVING_SEND_SMS_KEY = "power.long.lock.serving.send.sms.key.{}";

    /**
     * 宝龙h5提交发货单锁
     */
    public static final String POWER_LONG_LOCK_SERVING_SUBMIT_SHIPMENTS_KEY = "power.long.lock.serving.submit.shipments.key.{}";

    /**
     * 智播通口播回复内容key
     */
    public static final String ZBT_SOUND_REPLY_CONTENT_KEY = "zbt.sound.reply.content.key.{}";

    /**
     * 智播通门店保活key
     */
    public static final String ZBT_STORE_KEEP_ALIVE_KEY = "zbt.store.keep.alive.key.{}";


    /**
     * 智播通口播回复间隔key
     */
    public static final String ZBT_SOUND_REPLY_INTERVAL_KEY = "zbt.sound.reply.interval.key.{}";

    /**
     * 门店维度评论场控间隔key
     */
    public static final String STORE_AUTOMATIC_COMMENTS_KEY = "store.automatic.comments.key.{}";

    /**
     * 抖音EOS 用户关联的直播间信息   (直播状态)
     */
    public static final String EOS_USER_RELATED_ROOM_INFO = "rpa.eos.user-related-room-info.{}";

    /**
     * EOS 用户关键指标 (直播间数据)
     */
    public static final String EOS_ACCOUNT_ID_OF_LIVE_ROOM = "rpa.eos.live-room.key-index.{}";

    /**
     * 智播通客户端连接状态
     */
    public static final String ZBT_CLIENT_CONNECT_STATUS_KEY = "zbt.client.connect.status.key.{}";

    /**
     * 智播通RPA保活
     */
    public static final String ZBT_PRA_KEEP_ALIVE_KEY = "zbt.pra.keep.alive.key.{}";


    /**
     * 小程序回调加锁
     */
    public static final String LOCK_MINA_ORDER_CALLBACK_PAY_ORDER = "lock.mina.order.callback.pay.order.{}";

    /**
     * 小程序退款回调加锁
     */
    public static final String LOCK_MINA_ORDER_REFUND_CALLBACK_PAY_ORDER = "lock.mina.order.refund.callback.pay.order.{}";

    /**
     * 阿里云NLS的Token
     */
    public static final String ALIYUN_TTS_NLS_TOKEN_KEY = "aliyun.nls.token";

    /**
     * 千帆接口AccessToken
     */
    public static final String BCE_WEN_XIN_ACCESS_TOKEN_KEY = "bce.wen-xin.access-token";
    /**
     * 支付宝凭证核销锁
     */
    public static final String LOCK_ALIPAY_CERTIFICATE_VERIFY_COUPON = "lock.alipay.certificate.verify.coupon.{}";

    /**
     * 小红书凭证核销锁
     */
    public static final String LOCK_RED_COUPON_VERIFY_COUPON = "lock.red.coupon.verify.coupon.{}";

    /**
     * 支付宝团购优惠券锁定操作锁
     */
    public static final String LOCK_ALIPAY_GROUP_COUPON_LOCK = "lock.alipay.group.coupon.lock.{}";

    /**
     * 优先选择的GPT服务
     */
    public static final String GPT_PROVIDER_NOTIFICATION = "gpt.provider.priority.selection";

    /**
     * 来探呗导出中心执行
     */
    public static final String BLOCKING_QUEUE_STAR_TASK_EXPORT_CENTER_EXECUTING = "star-task.export-center.executing.{}";

    /**
     * 来探呗达人报名次数
     */
    public static final String STAR_DAILY_APPLY_NUMBER = "star.daily.apply.number.{}";

    /**
     * 支付宝团购广告入口流量
     */
    public static final String AD_SHOW_ENTRANCE_FLOW = "ad_show_entrance_flow.{}";

    /**
     * 支付宝团购广告曝光流量
     */
    public static final String AD_SHOW_EXPOSURE_FLOW = "ad_show_exposure_flow.{}";


    /**
     * 首页数据
     * Key：merchantId.storeId.Version
     */
    public static final String APP_HOME_DATA = "app.home-data.{}.{}";

    /**
     * saas openapi的前置
     */
    public static final String CACHE_SAAS_OPEN_API_PRE = "saas.clt.{}";

    /**
     * 微信视频号，商家的AccessToken；Key=商家视频号小程序的AppId
     */
    public static final String WECHAT_SHOP_ACCESS_TOKEN = "wx.shop.token.{}";

    /**
     * 微信视频号，创单锁
     */
    public static final String WECHAT_SHOP_ORDER_CREATE_LOCK = "wx.shop.order.create.lock.{}";

    /**
     * 微信视频号，退单锁
     */
    public static final String WECHAT_SHOP_ORDER_AFTER_SALE_LOCK = "wx.shop.order.after_sale.lock.{}";

    /**
     * 转码任务幂等
     */
    public static final String TRANSCODE_IDEMPOTENT_MPS_JOB_ID = "nova.transcode-job.idempotent.{}";

    /**
     * 根据活动Id转码
     */
    public static final String TRANS_CODE_SCENE_MATERIAL_BY_ACTIVITY_ID = "trans_code.scene_material.activity_id.{}";

    /**
     * 根据门店Id转门店素材
     */
    public static final String TRANS_CODE_STORE_MATERIAL_BY_STORE_ID = "trans_code.store_material.store_id.{}";

    /**
     * 达人推防止平台达人超用（代理商维度）
     */
    public static final String LOCK_STAR_PUSH_AGENT_KEY = "lock.star.push.agent.{}";

    /**
     * 视频发布统计任务
     */
    public static final String TASK_FIX_VIDEO_RELEASE_KEY = "task.fixVideoReleaseCount";

    /**
     * 导入商铺数据 key为requestId
     */
    public static final String IMPORT_SHOP_DATA_KEY = "import.shop.data.{}";

    /**
     * 导入商铺数量
     */
    public static final String IMPORT_SHOP_COUNT_KEY = "import.shop.count.{}";

    /**
     * 美团session的key
     */
    public static final String MEITUAN_PRODUCT_SESSION_REDIS_KEY = "meituan.product.session.redis.key.{}";


    /**
     * 微信小店token缓存
     */
    public static final String WX_SHOP_TOKEN_KEY = "wx.shop.token.{}";

    /**
     * 微信小店商家类目
     */
    public static final String WX_SHOP_MERCHANT_CATEGORY_KEY = "wx.shop.merchant.category.{}";


    /**
     * 商品操作锁
     */
    public static final String PRODUCT_OPERATE_LOCK_KEY = "product.operate.lock.key.{}";

    /**
     * 集团商户佣金确认redis key
     */
    public static final String GROUP_MERCHANT_COMMISSION_CONFIRM_REDIS_KEY = "group.merchant.commission.confirm.redis.key";

    /**
     * 商品回调幂等处理
     */
    public static final String LOCK_PRODUCT_AUDIT_CALLBACK = "log.id.{}";


    /**
     * 发起钱包转账redis key
     */
    public static final String WALLET_TRANSFER_INIT_LOCK_KEY = "wallet.transfer.init.lock.key.{}";

    /**
     * 钱包转账redis key
     */
    public static final String WALLET_TRANSFER_LOCK_KEY = "wallet.transfer.lock.key.{}";


    /**
     * 商品修改锁
     */
    public static final String LOCK_QYK_PRODUCT_INFO_ALTERATION = "lock.qyk.product-info.alteration.{}";

    /**
     * 结算信息修改lock
     */
    public static final String LOCK_PAYMENT_MODIFY_KEY = "lock.payment.modify.{}";


    /**
     * 操作出款对账
     */
    public static final String ACCT_RECONCILIATION_DEAL_KEY = "acct.reconciliation.deal";

    /**
     * 核销锁幂等
     */
    public static final String VERIFY_TOKEN_IDEMPOTENT_KEY = "lock.verify.token.idempotent.{}";
}