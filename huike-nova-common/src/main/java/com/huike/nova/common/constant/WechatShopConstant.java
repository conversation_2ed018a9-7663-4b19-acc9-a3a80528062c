package com.huike.nova.common.constant;

/**
 * 微信视频号，常量定义
 *
 * <AUTHOR> (<EMAIL>)
 * @version WechatShopConstant.java, v1.0 12/25/2023 10:18 John Exp$
 */
public class WechatShopConstant extends WechatMinaConstants {

    /**
     * 商家实时发券模式 - 发券接口
     */
    public static final String API_SEND_VOUCHER = "https://api.weixin.qq.com/channels/ec/voucher/send";

    /**
     * 查询购券订单详情
     */
    public static final String API_GET_ORDER = "https://api.weixin.qq.com/channels/ec/order/get";

    /**
     * 获得用户的卡券列表
     */
    public static final String API_GET_VOUCHER_LIST = "https://api.weixin.qq.com/channels/ec/voucher/get_list";

    /**
     * 查询团购券详情
     */
    public static final String API_GET_VOUCHER = "https://api.weixin.qq.com/channels/ec/voucher/get";

    /**
     * 核销卡券接口
     */
    public static final String API_CONSUME_VOUCHER = "https://api.weixin.qq.com/channels/ec/voucher/consume";

    /**
     * 券撤销核销
     */
    public static final String API_REVOKE_VOUCHER = "https://api.weixin.qq.com/channels/ec/voucher/revoke";

    /**
     * 获取售后详情
     */
    public static final String API_GET_AFTER_SALE_ORDER = "https://api.weixin.qq.com/channels/ec/aftersale/getaftersaleorder";

    /**
     * 退款审核回调
     */
    public static final String API_REFUND_AUDIT_NOTIFY = "https://api.weixin.qq.com/channels/ec/voucher/refund_audit_notify";
}
