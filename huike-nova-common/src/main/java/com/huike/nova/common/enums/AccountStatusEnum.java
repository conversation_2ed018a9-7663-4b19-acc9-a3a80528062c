/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.enums;

/**
 * <AUTHOR>
 * @version AccountStatusEnum.java, v 0.1 2022-11-28 2:47 PM ruanzy
 */
public enum AccountStatusEnum {
    /**
     * 正常
     */
    NORMAL("正常", 1),
    /**
     * 禁用
     */
    DISABLE("禁用", 2),
    ;

    private String name;
    private Integer value;

    AccountStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static AccountStatusEnum getByValue(Integer value) {
        AccountStatusEnum[] valueList = AccountStatusEnum.values();
        for (AccountStatusEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public Integer getValue() {
        return value;
    }
}