package com.huike.nova.common.util;

import com.github.rholder.retry.RetryException;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 重试工具
 *
 * <AUTHOR> (<EMAIL>)
 * @version RetryerUtil.java, v1.0 04/17/2024 10:29 John Exp$
 */
public class RetryerUtil {

    /**
     * 创建重试器
     *
     * @return 重试对象
     * @param attemptTimes 尝试次数
     * @param fixedWaitSec 等待时间
     * @param <T> 返回的类型
     */
    public static <T> Retryer<T> createRetryer(int attemptTimes, int fixedWaitSec) {
        return RetryerBuilder.<T>newBuilder()
                // retryIf 重试条件
                .retryIfException()
                .retryIfRuntimeException()
                .retryIfExceptionOfType(CommonException.class)
                // 等待策略：每次请求间隔1s
                .withWaitStrategy(WaitStrategies.fixedWait(fixedWaitSec, TimeUnit.SECONDS))
                // 停止策略 : 尝试请求3次
                .withStopStrategy(StopStrategies.stopAfterAttempt(attemptTimes))
                .build();
    }


    public static <T> Retryer<T> createRetryer() {
        return createRetryer(3, 1);
    }

    public static CommonException toCommonExceptionToCommon(Throwable throwable) {
        if (throwable instanceof RetryException) {
            RetryException ex = (RetryException) throwable;
            if (Objects.nonNull(ex.getCause()) && ex.getCause() instanceof CommonException) {
                return (CommonException) ex.getCause();
            }
        }
        return new CommonException(ErrorCodeEnum.RETRYER_FAILED_ERROR).detailMessage(throwable.getMessage());
    }
}
