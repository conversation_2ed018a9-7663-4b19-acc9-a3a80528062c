/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved
 */
package com.huike.nova.common.exception;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @version BaseException.java, v 0.1 2024-08-01-9:40 上午 liubo
 */
public abstract class BaseException extends RuntimeException{
    protected String msg;
    protected String code;

    public BaseException(String code, String msgFormat, Object... args) {
        super(MessageFormat.format(msgFormat, args));
        this.code = code;
        this.msg = MessageFormat.format(msgFormat, args);
    }

    public BaseException() {
    }

    public BaseException(String message, Throwable cause) {
        super(message, cause);
    }

    public BaseException(Throwable cause) {
        super(cause);
    }

    public BaseException(String message) {
        super(message);
    }

    public abstract BaseException newInstance(String var1, Object... var2);

    public String getMsg() {
        return this.msg;
    }

    public String getCode() {
        return this.code;
    }

    @Override
    public Throwable fillInStackTrace() {
        return this;
    }

    @Override
    public String getMessage() {
        return "[" + this.code + "]" + this.msg;
    }
}