/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.google.common.annotations.Beta;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import lombok.val;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version BizNoBuildUtil.java, v 0.1 2022-09-01 10:34 AM ruanzy
 */
public class BizNoBuildUtil {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
    private static final DateTimeFormatter FORMATTER_OF_FS_ORDER_ORDER_SN = DateTimeFormatter.ofPattern("yyMMddHHmmssSSS");
    private static final DateTimeFormatter FORMATTER_OF_FS_ORDER_DATETIME_SEC = DateTimeFormatter.ofPattern("yyMMddHHmmss");
    private static final String REDIS_KEY_BIZ_NO = "nova:biz:serial:{}";

    /**
     * 默认的KEY
     */
    private static final String DEFAULT_BIZ_KEY = "NOVA_BIZ";

    /**
     * 多少位的业务号从Redis中获取
     */
    private static final int MAX_BIZ_NO_DIGIT = 5;

    /**
     * 多少位的随机数
     */
    private static final int RANDOM_NO_DIGIT = 3;

    /**
     * 生成最大值多少后，重新进行计数
     */
    private static final int COERCE_AT_MOST_VAL = 100_000;

    /**
     * 格式，根据MAX_BIZ_NO_DIGIT进行补0
     */
    private static final String FORMAT_ATOMIC_STR = String.format("%%0%dd", MAX_BIZ_NO_DIGIT);

    /**
     * 总共的流水号位数
     */
    private static final int BIZ_NO_CAPACITY = RANDOM_NO_DIGIT + MAX_BIZ_NO_DIGIT + 12;

    private BizNoBuildUtil() {
    }

    /**
     * 业务主键默认年月日时分秒毫秒+5位随机数
     *
     * @return
     */
    @Beta
    public static String build() {
        LocalDateTime dateTime = LocalDateTime.now();
        return FORMATTER_OF_FS_ORDER_ORDER_SN.format(dateTime) + RandomStringUtils.randomNumeric(5);
    }

    /**
     * 创建唯一Id
     *
     * @return 唯一Id
     */
    public static String buildV2() {
        return buildV2(StringPool.EMPTY, null);
    }

    /**
     * 业务主键默认年月日时分秒+Redis自增5位+3位随机数
     *
     * @param prefix 标识前缀，传空将默认
     * @param bizName 业务标识
     * @return 唯一序列号
     */
    public synchronized static String buildV2(@Nonnull String prefix, @Nullable String bizName) {
        // 默认生成20位
        val sb = new StringBuilder(prefix.length() + BIZ_NO_CAPACITY);
        sb.append(prefix);
        // 添加日期：
        sb.append(FORMATTER_OF_FS_ORDER_DATETIME_SEC.format(LocalDateTime.now()));
        val redissonClient = SpringUtil.getBean(RedissonClient.class);
        val redisKey = StrUtil.format(REDIS_KEY_BIZ_NO, StringUtils.defaultIfBlank(bizName,  DEFAULT_BIZ_KEY));
        val atomicLong = redissonClient.getAtomicLong(redisKey);
        // 获得Redis存储的业务组件
        val bizCount = atomicLong.getAndIncrement();
        if (bizCount >= COERCE_AT_MOST_VAL) {
            atomicLong.set(CommonConstant.ZERO);
        }
        sb.append(String.format(FORMAT_ATOMIC_STR, bizCount));
        sb.append(RandomStringUtils.randomNumeric(3));
        return sb.toString();
    }

    /**
     * 业务主键默认年月日时分秒毫秒+5位随机数（补单用）
     *
     * @param timestamp 日期时间戳
     * @return
     */
    public static String build(int timestamp) {
        val dateTime = LocalDateTime.ofEpochSecond(timestamp, 0, CommonConstant.GMT_8);
        return FORMATTER_OF_FS_ORDER_ORDER_SN.format(dateTime) + RandomStringUtils.randomNumeric(5);
    }

    public static String buildWithParam(String param) {
        return param + build();
    }

    public static String buildUUID() {
        return UUID.randomUUID().toString().replace("-", StrUtil.EMPTY);
    }
}