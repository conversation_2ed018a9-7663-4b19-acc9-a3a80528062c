/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.enums;

/**
 * <AUTHOR>
 * @version AccountTypeEnum.java, v 0.1 2022-11-28 2:47 PM json
 */
public enum AccountTypeEnum {
    /**
     * 正常
     */
    ALL("全集团账号", 1),
    /**
     * 禁用
     */
    PART("部分集团账号", 2),
    ;

    private String name;
    private Integer value;

    AccountTypeEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static AccountTypeEnum getByValue(Integer value) {
        AccountTypeEnum[] valueList = AccountTypeEnum.values();
        for (AccountTypeEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public Integer getValue() {
        return value;
    }
}