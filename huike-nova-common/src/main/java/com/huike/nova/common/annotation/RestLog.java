package com.huike.nova.common.annotation;/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */

import java.lang.annotation.*;

/**
 * 控制层接口 日志
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Documented
public @interface RestLog {

    /**
     * 描述
     */
    String desc() default "";

    /**
     * 日志级别
     */
    Level level() default Level.DEBUG;

    enum Level {
        /***/
        TRACE, DEBUG, INFO, WARN, ERROR
    }
}
