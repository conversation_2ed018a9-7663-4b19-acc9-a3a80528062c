package com.huike.nova.common.constant.meituan;

import java.util.regex.Pattern;

/**
 * 美团团购常量定义
 *
 * <AUTHOR> (<EMAIL>)
 * @version MeiTuanGrouponConstants.java, v1.0 05/30/2024 17:09 John Exp$
 */
public class MeiTuanGrouponConstants {

    /**
     * 券码前缀
     */
    public static final String COUPON_PREFIX = "DP";

    /**
     * ItemOrder前缀
     */
    public static final String ITEM_ORDER_PREFIX = "DPI_";

    /**
     * 美团核券VerifyToken前缀
     */
    public static final String VERIFY_TOKEN_PREFIX = "MT-VERIFY-";

    /**
     * 退款前缀
     */
    public static final String REFUND_ORDER_PREFIX = "";

    /**
     * 正则 - 美团卡券规则
     */
    public static final Pattern MEITUAN_GROUPON_COUPON_CODE_PATTERN = Pattern.compile("^[a-zA-Z]{2}[a-zA-Z0-9]*$");
}
