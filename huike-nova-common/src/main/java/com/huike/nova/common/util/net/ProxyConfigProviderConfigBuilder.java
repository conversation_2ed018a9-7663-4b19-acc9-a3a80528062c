package com.huike.nova.common.util.net;

import com.huike.nova.common.constant.CommonConstant;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.val;
import okhttp3.Authenticator;
import okhttp3.Credentials;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.Route;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.net.InetSocketAddress;
import java.net.Proxy;


/**
 * OKHttp3 代理类
 *
 * <AUTHOR> (<EMAIL>)
 * @version ProxyConfigProviderConfigBuilder.java, v1.0 08/24/2023 11:47 John Exp$
 */
@Data
@Accessors(chain = true)
public class ProxyConfigProviderConfigBuilder implements IProxyConfigProvider {
    /** 是否启用 */
    private boolean enableProxy = false;

    /** 服务端 */
    private String proxyHost;

    /** 端口 */
    private int proxyPort;

    /**
     * 类型
     * 注意：区分SOCKS和HTTP代理，一个是5层代理，一个是7层代理
     * TODO: {@link Proxy.Type#HTTP} 目前不支持，仅支持{@link Proxy.Type#SOCKS}
     */
    private Proxy.Type proxyType;

    /** 用户名 */
    private String proxyUsername;

    /** 密码 */
    private String proxyPassword;

    /**
     * 生成代理对象
     *
     * @return 代理对象
     */
    @Override
    public Proxy getProxy() {
        return new Proxy(proxyType,  new InetSocketAddress(proxyHost, proxyPort));
    }

    /**
     * 设置代理的类型
     * @param proxyType 代理类型字符串:SOCKS, HTTP, NO_PROXY
     * @return 对象本身
     */
    @SuppressWarnings("UnusedReturnValue")
    public ProxyConfigProviderConfigBuilder setProxyType(String proxyType) {
        val name = StringUtils.defaultIfBlank( StringUtils.upperCase(proxyType), Proxy.Type.DIRECT.name());
        for (val proxyTypeVal: Proxy.Type.values()) {
            if (StringUtils.equals(proxyTypeVal.name(), name)) {
                this.proxyType = proxyTypeVal;
                break;
            }
        }
        return this;
    }

    /**
     * 生成代理认证对象（如果需要的话）
     *
     * @return 代理认证对象
     */
    @Nullable
    public Authenticator buildAuthenticator() {
        return new Authenticator() {
            @Nullable
            @Override
            public Request authenticate(@Nullable Route route, @NotNull Response response) {
                if (StringUtils.isBlank(proxyUsername)) {
                    return null;
                }
                String credential = Credentials.basic(proxyUsername, proxyPassword);
                return response.request().newBuilder()
                        .header(CommonConstant.PROXY_AUTHORIZATION, credential)
                        .build();
            }
        };
    }
}
