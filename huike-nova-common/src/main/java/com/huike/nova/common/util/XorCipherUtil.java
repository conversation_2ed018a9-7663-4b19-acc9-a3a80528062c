package com.huike.nova.common.util;

import com.annimon.stream.function.Supplier;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.constant.StringPool;
import org.apache.commons.codec.binary.Hex;

import java.nio.charset.StandardCharsets;

/**
 * XorCipherUtil 工具
 *
 * <AUTHOR> (lin<PERSON>@zentech-inc.com)
 * @since 2017/5/11
 */
public class XorCipherUtil {
    /**
     * xor 加密
     * @param str 原始串
     * @param iv 偏移量
     * @return 加密串
     */
    public static String xorEncode(String str, String iv) {
        byte[] strBytes = str.getBytes(StandardCharsets.UTF_8);
        byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);
        return Hex.encodeHexString(xorConvert(strBytes, ivBytes)).toUpperCase();
    }

    public static String xorEncode(String str) {
        return xorEncode(str, CommonConstant.XOR_IV);
    }

    /**
     * xor 解密
     *
     * @param hexString
     * @param iv
     * @return
     */
    public static String xorDecode(String hexString, String iv) {
        return Supplier.Util.safe(() -> {
            byte[] sourceBytes = Hex.decodeHex(hexString.toCharArray());
            byte[] ivBytes = iv.getBytes(StandardCharsets.UTF_8);
            return new String(xorConvert(sourceBytes, ivBytes), StandardCharsets.UTF_8);
        }, StringPool.EMPTY).get();
    }

    public static String xorDecode(String hexString) {
        return xorDecode(hexString, CommonConstant.XOR_IV);
    }

    private static byte[] xorConvert(byte[] sourceBytes, byte[] ivBytes) {
        byte[] buffer = new byte[sourceBytes.length];
        for (int i = 0; i != sourceBytes.length; ++i) {
            buffer[i] = (byte) ((sourceBytes[i] ^ ivBytes[i % ivBytes.length]) & 0xff);
        }
        return buffer;
    }
}
