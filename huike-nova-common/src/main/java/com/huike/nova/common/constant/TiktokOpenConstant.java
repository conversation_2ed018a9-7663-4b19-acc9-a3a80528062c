/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version TiktokOpenConstant.java, v 0.1 2022-09-01 3:03 PM ruanzy
 */
public class TiktokOpenConstant {

    /**
     * 抖音OAUTH 链接
     */
    public static final String TIKTOK_OAUTH_CONNECT_URL = "https://open.douyin.com/platform/oauth/connect/";

    /**
     * 抖音话题榜
     */
    public static final String TIKTOK_TOPIC_URL = "https://open.douyin.com/data/extern/billboard/topic/";

    /**
     * 抖音获取client_token
     */
    public static final String TIKTOK_CLIENT_TOKEN_URL = "https://open.douyin.com/oauth/client_token/";

    /**
     * 抖音获取jsb_ticket
     */
    public static final String TIKTOK_JS_TICKET_TOKEN_URL = "https://open.douyin.com/js/getticket/";

    /**
     * 抖音获取ticket
     */
    public static final String TIKTOK_OPEN_TICKET_TOKEN_URL = "https://open.douyin.com/open/getticket/";

    /**
     * 抖音热词榜
     */
    public static final String TIKTOK_HOT_SENTENCES_URL = "https://open.douyin.com/hotsearch/sentences/";

    /**
     * 抖音获取access_token
     */
    public static final String TIKTOK_ACCESS_TOKEN_URL = "https://open.douyin.com/oauth/access_token/";

    /**
     * 抖音刷新access_token
     */
    public static final String TIKTOK_NEW_ACCESS_TOKEN_URL = "https://open.douyin.com/oauth/refresh_token/";

    /**
     * 抖音刷新refresh_token
     */
    public static final String TIKTOK_NEW_REFRESH_TOKEN_URL = "https://open.douyin.com/oauth/renew_refresh_token/";

    /**
     * 抖音获取视频share_id
     */
    public static final String TIKTOK_SHARE_ID_URL = "https://open.douyin.com/share-id/?need_callback=true";

    /**
     * 查询特定视频的视频数据
     */
    public static final String TIKTOK_SPECIFIC_VIDEO_DATA_URL = "https://open.douyin.com/video/data/?open_id={}";

    /**
     * 查询特定视频的视频数据 video.data.bind
     */
    public static final String TIKTOK_SPECIFIC_VIDEO_DATA_BIND_URL = "https://open.douyin.com/api/douyin/v1/video/video_data/?open_id={}";

    /**
     * 抖音门店信息查询
     */
    public static final String TIKTOK_SHOP_POI_URL = "https://open.douyin.com/goodlife/v1/shop/poi/query/";

    /**
     * 抖音获取用户信息
     */
    public static final String TIKTOK_USER_INFO_URL = "https://open.douyin.com/oauth/userinfo/";

    /**
     * 刷新抖音refresh_token
     */
    public static final String TIKTOK_RENEW_REFRESH_TOKEN_URL = "https://open.douyin.com/oauth/renew_refresh_token/";

    /**
     * 生成分享 Schema
     */
    public static final String TIKTOK_SCHEMA_URL = "snssdk1128://openplatform/share";

    /**
     * 生成分享 ios Schema
     */
    public static final String TIKTOK_IOS_SCHEMA_URL = "snssdk1128://deeplink/openplatform";

    /**
     * 获取code2session
     */
    public static final String TIKTOK_GET_CODE_2_SESSION = "https://developer.toutiao.com/api/apps/v2/jscode2session";

    /**
     * 获取客服url
     */
    public static final String TIKTOK_GET_CUSTOMER_SERVICE_URL = "https://developer.toutiao.com/api/apps/chat/customer_service_url";

    /**
     * 获取客服url参数
     */
    public static final String TIKTOK_GET_CUSTOMER_SERVICE_URL_PARAM = "?appid={}&openid={}&type=1128&scene=1&im_type=group_buy&order_id=123";

    /**
     * 同步退款审核结果
     */
    public static final String MERCHANT_AUDIT_CALLBACK_URL = "https://open.douyin.com/api/apps/trade/v2/refund/merchant_audit_callback";

    /**
     * 查询订单信息
     */
    public static final String QUERY_ORDER_URL = "https://open.douyin.com/api/apps/trade/v2/order/query_order";

    /**
     * 验券准备
     */
    public static final String DELIVERY_PREPARE_URL = "https://open.douyin.com/api/apps/trade/v2/fulfillment/delivery_prepare";

    /**
     * 验券
     */
    public static final String DELIVERY_VERIFY_URL = "https://open.douyin.com/api/apps/trade/v2/fulfillment/delivery_verify";

    /**
     * 撤销核销
     */
    public static final String CANCEL_VERIFY_URL = "https://open.douyin.com/api/trade/v2/fulfillment/verify_cancel";

    /**
     * 查询劵状态信息
     */
    public static final String QUERY_COUPON_URL = "https://open.douyin.com/api/apps/trade/v2/order/query_item_order_info";

    /**
     * 服务商验券准备
     */
    public static final String CL_PREPARE_DELIVERY_URL = "https://open.douyin.com/goodlife/v1/fulfilment/certificate/prepare/";

    /**
     * 服务商订单查询
     */
    public static final String CLOSE_ORDER_QUERY = "https://open.douyin.com/goodlife/v1/trade/order/query/";

    /**
     * 闭环订单查询
     */
    public static final String CLOSE_AKTE_ORDER_QUERY = "https://open.douyin.com/goodlife/v1/akte/order/query/";

    /**
     * 卡券状态查询
     */
    public static final String CLOSE_CERTIFICATE_QUERY = "https://open.douyin.com/goodlife/v1/fulfilment/certificate/query/";

    /**
     * 服务商验券
     */
    public static final String SERVICE_DELIVERY_VERIFY_URL = "https://open.douyin.com/goodlife/v1/fulfilment/certificate/verify/";

    /**
     * 服务商撤销验券
     */
    public static final String SERVICE_CANCEL_VERIFY_URL = "https://open.douyin.com/goodlife/v1/fulfilment/certificate/cancel/";

    /**
     * 抖音外卖自配送-回传配送信息url
     */
    public static final String TAKEOUT_DISTRIBUTION_ORDER_URL = "https://open.douyin.com/goodlife/v1/fulfilment/distribution/order/sync_status/";

    /**
     * 商家处理退款消息url
     */
    public static final String TAKEOUT_MERCHANT_AUDIT_NOTIFY = "https://open.douyin.com/goodlife/v1/after_sale/audit/notify/";

    /**
     * 确认订单接口url
     */
    public static final String TAKEOUT_MERCHANT_CONFIRM_ORDER = "https://open.douyin.com/goodlife/v1/trade/buy/merchant_confirm_order/";

    /**
     * 拒绝接单接口url
     */
    public static final String TAKEOUT_MERCHANT_REJECT_ORDER = "https://open.douyin.com/goodlife/v1/after_sale/order/merchant_reject/";

    /**
     * 商家取消订单url
     */
    public static final String TAKEOUT_MERCHANT_APPLY_REFUND = "https://open.douyin.com/goodlife/v1/after_sale/order/apply_refund/";

    /**
     * 获取抖音话题url
     */
    public static final String TIKTOK_TOPIC_SEARCH_URL = "https://creator.douyin.com/aweme/v1/search/challengesug/?source=challenge_create&aid=2906&keyword=";

    /**
     * 抖音开发者发起退款
     */
    public static final String TIKTOK_DEVELOPER_OPERATE_REFUND = "https://open.douyin.com/api/apps/trade/v2/refund/create_refund";

    /**
     * 抖音POI查询详情
     */
    public static final String TIKTOK_POI_DETAIL = "https://aweme.snssdk.com/aweme/v1/poi/detail_web/?poi_id=";

    /**
     * 抖音搜索POI名称
     */
    public static final String TIKTOK_SEARCH_POI = "https://creator.douyin.com/web/api/media/poi/search/?keyword=${keyword}&page_size=12&latitude=${latitude}&longitude=${longitude}&type=2&page=0&cookie_enabled=true&screen_width=1280&screen_height=720&browser_language=en-US&browser_platform=Win32&browser_name=Mozilla&browser_version=5.0+%28Windows+NT+10.0%3B+Win64%3B+x64%29+AppleWebKit%2F537.36+%28KHTML%2C+like+Gecko%29+Chrome%2F109.0.0.0+Safari%2F537.36&browser_online=true&timezone_name=Asia%2FShanghai&aid=112";


    /** 视频ID占位符 */
    public static final String VIDEO_ID_PLACEHOLDER = "$VIDEO_ID$";

    /**
     * 抖音视频链接地址
     */
    public static final String TIKTOK_VIDEO_URL = "https://www.douyin.com/video/" + VIDEO_ID_PLACEHOLDER;

    public static final String TIKTOK_CLIENT_KEY = "client_key";

    public static final String TIKTOK_RESPONSE_TYPE = "response_type";

    public static final String TIKTOK_SCOPE = "scope";

    public static final String TIKTOK_REDIRECT_URI = "redirect_uri";

    public static final String TIKTOK_STATE = "state";

    public static final String CODE = "code";

    public static final String STATUS_CODE = "status_code";
}