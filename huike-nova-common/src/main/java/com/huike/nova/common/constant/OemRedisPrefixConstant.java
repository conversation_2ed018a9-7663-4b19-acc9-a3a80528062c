/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version OemRedisPrefixConstant.java, v 0.1 2023-08-01 10:03 AM ruanzy
 */
public class OemRedisPrefixConstant {

    /**
     * 短信发送缓存key（用于验证）
     * 第一个替换成oem配置id
     * 第二个替换成场景类型
     * 第三个替换成手机号
     */
    public static final String OEM_CACHE_KEY_SMS_VERIFY = "oem.cache.sms.verify.{}.{}.{}";

    /**
     * 短信发送次数缓存key（用于风控）
     * 第一个替换成oem配置id
     * 第二个替换成场景类型
     * 第三个替换成手机号
     */
    public static final String OEM_CACHE_KEY_SMS_REQUEST_COUNT = "oem.cache.sms.request.count.{}.{}.{}";

    /**
     * 短信发送缓存key(用于风控)
     * 第一个替换成oem配置id
     * 第二个替换成场景类型
     * 第三个替换成手机号
     */
    public static final String OEM_CACHE_KEY_SMS_REQUEST = "oem.cache.sms.request.{}.{}.{}";

    /**
     * 商户类目
     */
    public static final String KEY_MERCHANT_CATEGORY_RESULT = "key.merchant.category.result";

    /**
     * 门店类目
     */
    public static final String KEY_STORE_CATEGORY_RESULT = "key.store.category.result";

    /**
     * oem新增代理商锁
     */
    public static final String OEM_LOCK_OPERATION_ADD_AGENT_KEY = "oem.lock.operation.add.agent.key.{}";

    /**
     * oem运营后台充值套餐锁
     */
    public static final String OEM_LOCK_OPERATION_RECHARGE_PACKAGE_KEY = "oem.lock.operation.recharge.package.key.{}";

    /**
     * oem新增合伙人锁
     */
    public static final String OEM_LOCK_AGENT_ADD_PARTNER_KEY = "oem.lock.agent.add.partner.key.{}";

    /**
     * oem新增员工锁
     */
    public static final String OEM_LOCK_ADD_EMPLOYEE_KEY = "oem.lock.add.employee.key.{}";

    /**
     * oem门店账套操作锁
     */
    public static final String OEM_LOCK_STORE_ACCOUNT_OPERATION_KEY = "oem.lock.store.account.operation.Key.{}.{}";

    /**
     * 支付宝订单结果通知锁
     */
    public static final String LOCK_ALIPAY_ORDER_RESULT_NOTIFICATION_KEY = "lock.alipay.order.result.notification.key.{}";

    /**
     * 凭证消息通知锁
     */
    public static final String LOCK_ALIPAY_CERTIFICATE_MSG_NOTIFICATION_KEY = "lock.alipay.certificate.msg.notification.key.{}";

}