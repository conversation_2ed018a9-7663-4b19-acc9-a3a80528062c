/**
 * fshows.com
 * Copyright (C) 2013-2018 All Rights Reserved.
 */
package com.huike.nova.common.util;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version SnCodeCheckUtils.java, v 0.1 2018-08-15 16:53 Admin
 */
public class SnCodeCheckUtils {

    public static Boolean checkSnCode(String snCode) {
        //判断字符串长度
        try {
            if (StringUtils.isBlank(snCode)) {
                return false;
            }
            if (snCode.length() != 13 && snCode.length() != 16) {
                return false;
            }
            //是不是以F或G开头
            if (!snCode.startsWith("F") && !snCode.startsWith("G")) {
                return false;
            }
            // 校验sn的年月日部分
            if (Integer.valueOf(snCode.substring(2, 4)) >= 18 &&
                    Integer.valueOf(snCode.substring(4, 6)) >= 1 && Integer.valueOf(snCode.substring(4, 6)) <= 12 &&
                    Integer.valueOf(snCode.substring(6, 8)) >= 1 && Integer.valueOf(snCode.substring(6, 8)) <= 31) {
                return true;
            }
            return false;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}