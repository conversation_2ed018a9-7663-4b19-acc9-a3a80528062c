/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.exception;

import cn.hutool.core.util.StrUtil;
import com.huike.nova.common.enums.ErrorCodeEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version CommonException.java, v 0.1 2022-09-01 3:11 PM ruanzy
 */
@Getter
public class CommonException extends RuntimeException {

    /**
     * 异常信息
     */
    protected String msg;

    /**
     * 具体异常码
     */
    private String code;

    /**
     * 错误枚举
     */
    private ErrorCodeEnum errorCodeEnum;

    /**
     * 额外的错误信息，该字段不为空时，会进行告警
     */
    private String extra;

    /**
     * 子错误码
     */
    private String subCode;

    /**
     * 子错误信息
     */
    private String subMsg;

    /**
     * 异常构造器
     */
    public CommonException() {
        super();
    }

    protected CommonException(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    /**
     * 异常构造器
     *
     * @param errorEnums
     */
    public CommonException(ErrorCodeEnum errorEnums) {
        this(errorEnums.getCode(), errorEnums.getMessage());
        this.errorCodeEnum = errorEnums;
    }

    /**
     * 子错误
     *
     * @param code 子错误码
     * @param msg 错误消息
     * @return 当前对象
     */
    public CommonException subError(String code, String msg) {
        this.subCode = code;
        this.subMsg = msg;
        return this;
    }

    /**
     * 子错误吗
     * @param code
     * @param msg
     * @return
     */
    public CommonException subError(Integer code, String msg) {
        return this.subError(String.valueOf(code), msg);
    }

    /**
     * 实例化异常
     *
     * @param msgFormat
     * @param args
     * @return 异常类
     */
    public CommonException detailMessage(String msgFormat, Object... args) {
        this.msg = StrUtil.format(msgFormat, args);
        return this;
    }

    /**
     * 设置额外信息
     *
     * @param extra 额外数据
     * @param args 组合额外数据的参数
     * @return 错误内容
     */
    public CommonException extra(String extra, Object... args) {
        if (StringUtils.isNotBlank(extra)) {
            this.extra = StrUtil.format(extra, args);
        }
        return this;
    }

    @Override
    public String getMessage() {
        return "[" + this.code + "]" + this.msg;
    }

}