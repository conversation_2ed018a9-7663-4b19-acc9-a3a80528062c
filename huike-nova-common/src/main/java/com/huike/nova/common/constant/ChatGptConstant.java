/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.constant;

import lombok.val;

/**
 * <AUTHOR>
 * @version ChatGptConstant.java, v 0.1 2023-04-18 4:24 PM ruanzy
 */
public class ChatGptConstant {

    /**
     * 接口url
     */
    public static final String CHAT_COMPLETIONS_URL = "http://api-gpt.jtuanke.com/v1/chat/completions";

    /**
     * 接口地址
     */
    public static final String API_HOST = "https://api.openai.com";

    /**
     * 接口名称
     */
    public static final String PATH_V1_CHAT_COMPLETIONS = "/v1/chat/completions";

    /**
     * 模型
     */
    public static final String MODEL = "gpt-3.5-turbo";

    /**
     * 用户角色
     */
    public static final String USER_ROLE = "user";

    /**
     * model-key
     */
    public static final String MODEL_KEY = "model";

    /**
     * messages-key
     */
    public static final String MESSAGES_KEY = "messages";

    /**
     * Authorization-key
     */
    public static final String AUTHORIZATION_KEY = "Authorization";

    /**
     * n-key
     */
    public static final String N_KEY = "n";

    /**
     * application/json
     */
    public static final String APPLICATION_JSON = "application/json";

    /**
     * 正则表达式
     */
    public static final String REG = "[\\\\\n\\r]";


    /**
     * insufficient_quota
     */
    public static final String INSUFFICIENT_QUOTA = "insufficient_quota";

    /**
     * ChatGPT流式接口结束标识
     */
    public static final String STREAM_DONE_FLAG = "[DONE]";
}