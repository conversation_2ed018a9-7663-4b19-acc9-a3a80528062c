/**
 * <AUTHOR>
 * @date 2024/5/18 13:50
 * @version 1.0 MeituanContants
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version MeituanContants.java, v 0.1 2024-05-18 13:50
 */
public class MeituanContants {

    /**
     * 成功code
     */
    public static final Integer SUCCESS_CODE = 200;

    /**
     * 数据格式
     */
    public static final String DATA_FORMAT = "JSON";

    /**
     * 加密方式
     */
    public static final String SIGN_METHOD = "MD5";

    /**
     * 接口版本号
     */
    public static final String METHOD_VERSION = "1";

    /**
     * 美团节假日
     */
    public static final String HOLIDAY = "HOLIDAY";

    /**
     * 场内店类别 MallShopAttrDTO.shopType
     */
    public static final String SHOP_TYPE = "购物";


    /**
     * 美团商品来源
     */
    public static final String SOURCE = "shoppingMall";

    /**
     * 卡券生成使用RedisKey
     */
    public static final String MEITUAN_GROUP_COUPON_GENERATE_KEY = "biz.meituan.groupon.coupon-code-gen";

    /**
     * 券码前缀
     */
    public static final String COUPON_PREFIX = "DP";

    /**
     * 需要校验商品map的不能为空的属性
     */
    public static final String CHECK_PRODUCT_MAP_ATTR = "detail,images,textArea,richText,effectiveDate,exceptDateType,exceptDateList,exceptWeekdayList,exceptHolidayList,saleLimitType,saleLimitType,saleLimitMax,promotionsIsOrNot";


    /**
     * appKey
     */
    public static final String APP_KEY = "af10676183f6f6f6";

    /**
     * appSecret
     */
    public static final String APP_SECRET = "c93a21e4df34d84a097903a4a11395fc723c9e19";

    /**
     * 测试session
     */
    public static final String SESSION = "8fa953cbd59cb415f9806d799c83c5ec30fc943b";
    /**
     * 美团HOST
     */
    public static final String MEITUAN_HOST = "https://openapi.dianping.com";

    /**
     * 获取请求接口的token
     */
    public static final String METHOD_TOKEN_QUERY = "/router/oauth/token";

    /**
     * 上传图片素材
     */
    public static final String METHOD_IMAGE_UPLOAD = "/router/material/image/upload";

    /**
     * 团单创建
     */
    public static final String METHOD_PRODUCT_CREATE = "/router/product/dealgroup/create";

    /**
     * 团单修改
     */
    public static final String METHOD_PRODUCT_MODIFY = "/router/product/dealgroup/submit";

    /**
     * 团单上架
     */
    public static final String METHOD_PRODUCT_ON_LINE = "/router/product/dealgroup/online";

    /**
     * 团单下架
     */
    public static final String METHOD_PRODUCT_OFF_LINE = "/router/product/dealgroup/offline";

    /**
     * 团单库存修改
     */
    public static final String METHOD_PRODUCT_UPDATE_STOCK = "/router/product/dealgroup/updatestock";

    /**
     * 商户下面团单查询
     */
    public static final String METHOD_MERCHANT_PRODUCT_QUERY = "/tuangou/deal/queryshopdeal";

    /**
     * 指定团单查询
     */
    public static final String METHOD_PRODUCT_QUERY = "/router/product/dealgroup/query";

    /**
     * 券查询
     */
    public static final String METHOD_COUPON_QUERY = "/router/receipt/queryreceiptstatus";

    /**
     * 撤销核销
     */
    public static final String METHOD_REVERSE_CONSUME = "/router/dzopen/thirdpartyreceipt/reverseconsume";

    /**
     * 账期查询
     */
    public static final String METHOD_PAY_PLAN_QUERY = "/router/finance/query/payplan";

    /**
     * 账期明细查询
     */
    public static final String METHOD_INCOME_DETAIL_QUERY = "/router/finance/income/detail";
}