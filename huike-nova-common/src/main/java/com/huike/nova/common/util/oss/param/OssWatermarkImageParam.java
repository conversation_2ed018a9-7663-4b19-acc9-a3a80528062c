/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.huike.nova.common.util.oss.param;

import cn.hutool.core.codec.Base64;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 图片水印
 *
 * <AUTHOR>
 * @version OssWatermarkImageParam.java, v 0.1 2019-04-22 16:15 buhao
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OssWatermarkImageParam extends OssWatermarkCommonParam {
    /**
     * 参数意义： 水印图片为当前的Bucket下Object，直接针对Object名称进行base64编码。
     */
    private String image;

    /**
     * Setter method for property <tt>image</tt>.
     *
     * @param image value to be assigned to property image
     */
    public void setImage(String image) {
        this.image = Base64.encodeUrlSafe(image);
    }

    /**
     * 添加备注
     *
     * @param image
     * @param extParam
     */
    public void setImage(String image, String extParam) {
        this.image = Base64.encodeUrlSafe(image) + extParam;
    }
}