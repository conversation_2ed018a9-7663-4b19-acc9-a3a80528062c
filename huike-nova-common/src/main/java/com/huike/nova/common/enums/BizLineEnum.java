/**
 * ailike.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.enums;

/**
 * 商品业务线
 *
 * <AUTHOR>
 * @version DelFlagEnum.java, v 0.1 2022-02-08 14:16 zhaoxumin
 */
public enum BizLineEnum {
    RPA("RPA", 0),
    CLOSE("闭环自研开发者", 1),
    MINA("抖音开环", 5),
    KOUBEI("阿里", 55);

    private String name;
    private Integer value;

    BizLineEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static BizLineEnum getByValue(Integer value) {
        BizLineEnum[] valueList = BizLineEnum.values();
        for (BizLineEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public Integer getValue() {
        return value;
    }
}