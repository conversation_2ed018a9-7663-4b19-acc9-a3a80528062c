package com.huike.nova.common.util;

import com.huike.nova.common.constant.CommonConstant;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.Map;

public class SignUtil {

    /**
     * signTopRequest
     *
     * @param params
     * @param appSecret
     * @param signMethod
     * @return
     * @throws IOException
     * @throws NoSuchAlgorithmException
     */
    public static String signTopRequest(Map<String, String> params, String appSecret, String signMethod) throws IOException, NoSuchAlgorithmException {
        // 第一步：检查参数是否已经排序
        String[] keys = params.keySet().toArray(new String[0]);
        Arrays.sort(keys);

        // 第二步：把所有参数名和参数值串在一起
        StringBuilder query = new StringBuilder();

        if (StringUtils.isNotEmpty(appSecret)) {
            query.append(appSecret);
        }

        for (String key : keys) {
            String value = params.get(key);
            if (StringUtil.areNotEmpty(key, value)) {
                query.append(key).append(value);
            }
        }

        // 第三步：使用MD5/HMAC加密
        byte[] bytes;
        if (CommonConstant.SIGN_METHOD_HMAC.equals(signMethod)) {
            bytes = encryptHMAC(query.toString(), appSecret);
        } else {
            query.append(appSecret);
            bytes = encryptMD5(query.toString());
        }

        // 第四步：把二进制转化为小写的十六进制
        return byte2hex(bytes);
    }

    /**
     * encryptHMAC
     *
     * @param data
     * @param secret
     * @return
     */
    public static byte[] encryptHMAC(String data, String secret) {
        byte[] bytes;
        try {
            SecretKey secretKey = new SecretKeySpec(secret.getBytes(CommonConstant.UTF8), "HmacMD5");
            Mac mac = Mac.getInstance(secretKey.getAlgorithm());
            mac.init(secretKey);
            bytes = mac.doFinal(data.getBytes(CommonConstant.UTF8));
        } catch (Exception e) {
            throw new RuntimeException("Encrypt HMAC error", e);
        }

        return bytes;
    }


    /**
     * encryptMD5
     *
     * @param info
     * @return
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     */
    public static byte[] encryptMD5(String info) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        byte[] infoBytes = info.getBytes("UTF-8");
        md5.update(infoBytes);
        return md5.digest();
    }


    /**
     * byte2hex
     *
     * @param bytes
     * @return
     */
    public static String byte2hex(byte[] bytes) {
        StringBuilder sign = new StringBuilder();
        for (int i = 0; i < bytes.length; i++) {
            String hex = Integer.toHexString(bytes[i] & 0xFF);
            if (hex.length() == 1) {
                sign.append("0");
            }
            sign.append(hex.toLowerCase());
        }
        return sign.toString();
    }
}
