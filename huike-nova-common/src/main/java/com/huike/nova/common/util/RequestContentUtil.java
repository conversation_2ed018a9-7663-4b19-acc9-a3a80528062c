/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.util;

import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version RequestContentGetUtil.java, v 0.1 2022-10-22 9:48 AM ruanzy
 */
@Slf4j
public class RequestContentUtil {

    /**
     * 获取请求信息
     *
     * @param request
     * @return
     */
    public static String getContent(HttpServletRequest request) {
        BufferedReader br = null;
        StringBuilder sb = new StringBuilder();
        try {
            br = request.getReader();
            String str;
            while ((str = br.readLine()) != null) {
                sb.append(str);
            }
            br.close();
        } catch (IOException e) {
            LogUtil.info(log, "RequestContentUtil.getContent >> msg={}", e.getMessage());
        } finally {
            if (null != br) {
                try {
                    br.close();
                } catch (IOException e) {
                    LogUtil.error(log, "RequestContentUtil.getContent >> msg={}", e.getMessage());
                }
            }
        }
        return sb.toString();
    }
}