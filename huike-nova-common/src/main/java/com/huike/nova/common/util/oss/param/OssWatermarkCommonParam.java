/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.huike.nova.common.util.oss.param;

import lombok.Data;

/**
 * <AUTHOR>
 * @version OssWatermarkCommonParam.java, v 0.1 2019-04-22 16:12 buhao
 */
@Data
public class OssWatermarkCommonParam {
    /**
     * 参数意义：透明度, 如果是图片水印，就是让图片变得透明，如果是文字水印，就是让水印变透明。
     * <p>
     * 默认值：100， 表示 100%（不透明）
     * <p>
     * 取值范围: [0-100]
     */
    private String t;
    /**
     * 参数意义：位置，水印打在图的位置，详情参考下方区域数值对应图。
     * <p>
     * 取值范围：[nw,north,ne,west,center,east,sw,south,se]
     */
    private String g;
    /**
     * 参数意义：水平边距, 就是距离图片边缘的水平距离， 这个参数只有当水印位置是左上，左中，左下， 右上，右中，右下才有意义。
     * <p>
     * 默认值：10
     * <p>
     * 取值范围：[0 – 4096]
     * <p>
     * 单位：像素（px）
     */
    private Integer x;
    /**
     * 参数意义：垂直边距, 就是距离图片边缘的垂直距离， 这个参数只有当水印位置是左上，中上， 右上，左下，中下，右下才有意义
     * 默认值：10
     * <p>
     * 取值范围：[0 – 4096]
     * <p>
     * 单位：像素(px)
     */
    private Integer y;
    /**
     * 参数意义： 中线垂直偏移，当水印位置在左中，中部，右中时，可以指定水印位置根据中线往上或者往下偏移
     * 默认值：0
     * <p>
     * 取值范围：[-1000, 1000]
     * <p>
     * 单位：像素(px)
     */
    private Integer voffset;
}