package com.huike.nova.common.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.google.common.hash.Hashing;
import com.huike.nova.common.constant.StringPool;
import com.huike.nova.common.metadata.DingtalkMessageForm;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.util.Objects;

/**
 * 钉钉告警 Util
 *
 * <AUTHOR>
 * @version DingTalkUtils.java, v 0.1 2022-07-22 15:41 liangyuanping
 */
@Slf4j
public class DingTalkUtil {

    public interface PostHandler {
        /**
         * 发送JSON数据
         *
         * @param url url地址
         * @param body 消息体
         * @return 发送内容
         */
        String postJson(String url, String body);
    }

    private static final String URL = "https://oapi.dingtalk.com/robot/send?access_token={}";


    /**
     * 可自行接管发送消息
     */
    public static PostHandler postHandler = null;

    /**
     * 发送钉钉告警
     *
     * @param token
     * @param message
     */
    public static void sendAlarm(String token, String message) {
        HttpPost httppost = new HttpPost(StrUtil.format(URL, token));
        httppost.addHeader("Content-Type", "application/json; charset=utf-8");
        StringEntity se = new StringEntity(message, "UTF-8");
        httppost.setEntity(se);
        try (CloseableHttpClient httpclient = HttpClients.createDefault()) {
            HttpResponse response = httpclient.execute(httppost);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String result = EntityUtils.toString(response.getEntity(), "UTF-8");
                LogUtil.info(log, "告警成功,message={},response={}", message, result);
            }
        } catch (IOException e) {
            LogUtil.error(log, "告警失败,message={},exception={}", message, e);
        }
    }

    /**
     * 发送钉钉告警
     *
     * @param token token
     * @param secret 密钥
     * @param message 消息内容
     */
    public static void sendAlarm(String token, String secret, String message) {
        try {
            String url = getWebHookUrlByTokenAndSecret(token, secret);
            String result = StringPool.EMPTY;
            if (Objects.nonNull(postHandler)) {
                result = postHandler.postJson(url, message);
            } else {
                HttpPost httppost = new HttpPost(url);
                httppost.addHeader("Content-Type", "application/json; charset=utf-8");
                StringEntity se = new StringEntity(message, "UTF-8");
                httppost.setEntity(se);
                try (CloseableHttpClient httpclient = HttpClients.createDefault()) {
                    HttpResponse response = httpclient.execute(httppost);
                    if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                        result = EntityUtils.toString(response.getEntity(), "UTF-8");
                    }
                } catch (IOException e) {
                    LogUtil.error(log, "告警失败,message={},exception={}", message, e);
                }
            }
            LogUtil.info(log, "告警成功, message={}, response={}", message, result);

        } catch (Exception ex) {
            LogUtil.error(log, "告警失败, 告警内容:{}, 错误:{}", message, ex);
        }
    }

    public static String getWebHookUrlByAccessToken(String accessToken) {
        return StrUtil.format(URL, accessToken);
    }

    /**
     * 获得请求地址
     *
     * @param token token
     * @param secret 密钥
     * @return 带签名的密钥
     */
    public static String getWebHookUrlByTokenAndSecret(String token, String secret) {
        // 组合钉钉的接口地址
        String url = StrUtil.format(URL, token);
        if (StringUtils.isBlank(secret)) {
            return url;
        }
        return getSignedUrl(url, secret);
    }

    /**
     * 构建发票拉取异常 钉钉消息json
     *
     * @param message 告警内容
     * @return
     */
    public static String buildMessage(String message) {
        JSONObject messageJson = new JSONObject();
        JSONObject contentJson = new JSONObject();
        JSONObject atJson = new JSONObject();
        contentJson.put("content", message);
        atJson.put("isAtAll", true);
        messageJson.put("msgtype", "text");
        messageJson.put("text", contentJson.toJSONString());
        messageJson.put("at", atJson.toJSONString());
        return messageJson.toJSONString();
    }

    /**
     * 生成签名
     *
     * @param url
     * @param secret 钉钉密文
     * @return {@link String}
     * <AUTHOR>
     */
    public static String getSignedUrl(String url, String secret) {
        try {
            Long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + secret;
            byte[] signBytes = Hashing.hmacSha256(secret.getBytes(StandardCharsets.UTF_8)).hashString(stringToSign, StandardCharsets.UTF_8).asBytes();
            String sign = URLEncoder.encode(Base64.encodeBase64String(signBytes), "UTF-8");
            return url + "&timestamp=" + timestamp + "&sign=" + sign;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 生成签名
     *
     * @param url
     * @param secret 钉钉密文
     * @return {@link String}
     * <AUTHOR>
     */
    public static String getSign(String url, String secret) {
        try {
            Long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
            return url + "&timestamp=" + timestamp + "&sign=" + sign;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * webhook推送钉钉通知
     *
     * @param form
     * @return
     */
    public static String sendDingDingWebHookMessage(DingtalkMessageForm form) {
        DingTalkClient client = new DefaultDingTalkClient(form.getWebHookUrl());
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setIsAtAll(true);
        request.setAt(at);

        request.setMsgtype("markdown");
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle(form.getTitle());
        markdown.setText(form.getContent());
        request.setMarkdown(markdown);
        try {
            OapiRobotSendResponse response = client.execute(request);
            return JSONObject.toJSONString(response);
        } catch (ApiException e) {
            LogUtil.error(log, "sendDingDingWebHookMessage === 发送钉钉通知失败 form:{}", form, e);
            return JSONObject.toJSONString(e);
        }

    }


    /**
     * 发送支付宝团购告警
     *
     * @param token   钉钉Token
     * @param title   标题
     * @param message 内容
     */
    public static void sendAlipayGroupPurchaseMessage(String token, String title, String message) {
        LogUtil.info(log, "DingDingCommonServiceImpl.sendAlipayGroupPurchaseMessage >> 支付宝团购告警 >> title = {},message = {}", title, message);
        String text = MessageFormat.format(title + "   \n   {0}", message);
        sendAlipayDingDingWebHookMessage(buildDingUrlWithSign(token, StringPool.EMPTY), title, text);
    }

    /**
     * 发送支付宝团购广告数据
     *
     * @param token   钉钉Token
     * @param title   标题
     * @param message 内容
     */
    public static void sendAlipayGroupAdMessage(String token, String title, String message) {
        LogUtil.info(log, "DingDingCommonServiceImpl.sendAlipayGroupAdMessage >> 发送支付宝团购广告数据 >> title = {},message = {}", title, message);
        String text = MessageFormat.format(title + "   \n   {0}", message);
        sendAlipayDingDingWebHookMessage(buildDingUrlWithSign(token, "SEC33b5cb06317608f2cefdfe53a33e9e29e0e6eb98c4729093010c2220a7615a49"), title, text);
    }


    /**
     * 发送代签名的钉钉Webhook通知
     *
     * @param webHookUrl 钉钉webhook地址
     * @param title      标题
     * @param content    内容
     * @return 钉钉返回结果
     */
    public static String sendAlipayDingDingWebHookMessage(String webHookUrl, String title, String content) {
        DingTalkClient client = new DefaultDingTalkClient(webHookUrl);
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setIsAtAll(false);
        request.setAt(at);

        request.setMsgtype("markdown");
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle(title);
        markdown.setText(content);
        request.setMarkdown(markdown);
        try {
            OapiRobotSendResponse response = client.execute(request);
            return JSONObject.toJSONString(response);
        } catch (ApiException e) {
            LogUtil.error(log, "sendDingDingWebHookMessage === 发送钉钉通知失败 title:{} content:{}", title, content, e);
            return JSONObject.toJSONString(e);
        }
    }

    /**
     * 构建带签名的钉钉url
     *
     * @return
     */
    private static String buildDingUrlWithSign(String accessToken, String secret) {
        try {
            Long timestamp = System.currentTimeMillis();
            String stringToSign = timestamp + "\n" + secret;
            Mac mac = Mac.getInstance("HmacSHA256");
            mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
            byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
            String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
            String webHook = MessageFormat.format("https://oapi.dingtalk.com/robot/send?access_token={0}", accessToken);
            return webHook + "&timestamp=" + timestamp + "&sign=" + sign;
        } catch (Exception e) {
            LogUtil.info(log, "构建带签名的钉钉url异常 e = {}", e);
        }

        return null;
    }

}
