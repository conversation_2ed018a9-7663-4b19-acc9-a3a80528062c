/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.constant.starTask.web;

/**
 * <AUTHOR>
 * @version StarTaskWebRedisPrefixConstant.java, v 0.1 2023-12-06 9:39 AM ruanzy
 */
public class StarTaskWebRedisPrefixConstant {

    /**
     * 新增员工接口幂等缓存
     */
    public static final String STAR_TASK_WEB_CACHE_KEY_ADD_EMPLOYEE = "star.task.web.cache.key.add.employee.{}.{}";

    /**
     * 员工操作接口幂等缓存
     */
    public static final String STAR_TASK_WEB_CACHE_KEY_OPERATE_EMPLOYEE = "star.task.web.cache.key.operate.employee.{}";

    /**
     * 后台账户充值锁
     */
    public static final String STAR_TASK_WEB_KEY_RECHARGE_LOCK = "star.task.web.key.recharge.lock.{}";

    /**
     * 报名清单状态操作
     */
    public static final String STAR_TASK_WEB_KEY_APPLY_STATUS_OPERATION = "star.task.web.key.apply.status.operation.{}";

    /**
     * 后台体现申请操作锁
     */
    public static final String STAR_TASK_WEB_KEY_APPLY_WITHDRAWAL = "star.task.web.key.apply.withdrawal.{}";

}