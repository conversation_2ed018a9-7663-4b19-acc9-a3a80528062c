package com.huike.nova.common.enums;

/**
 * 智荐惊喜页面，逻辑值
 *
 * <AUTHOR> (<EMAIL>)
 * @version AilikeBoolEnum.java, v1.0 08/23/2023 15:51 John Exp$
 */
public enum AilikeBoolEnum {

    /**
     * 背景音乐
     */
    YES("是", 1),
    /**
     * 原视频音频
     */
    NO("否", 2);

    AilikeBoolEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    private final String name;

    private final Integer value;

    public static AilikeBoolEnum getByValue(Integer value) {
        AilikeBoolEnum[] valueList = AilikeBoolEnum.values();
        for (AilikeBoolEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }


    public String getName() {
        return name;
    }

    public Integer getValue() {
        return value;
    }
}
