package com.huike.nova.common.enums;

/**
 * mq枚举类
 *
 * <AUTHOR>
 * @version ProduceEnum.java, v 0.1 2022/3/10 4:05 下午 mayucong
 */
public enum ProduceEnum {
    /**
     * 导出中心
     */
    COMMON_EXPORT("导出中心", "COMMON_EXPORT"),
    SETTLEMENT_DEAL("结算处理", "SETTLEMENT_DEAL"),
    TRANSFER_QUERY("转账查询", "TRANSFER_QUERY"),
    WITHDRAW_APPLY("提现申请", "WITHDRAW_APPLY"),
    WITHDRAW_QUERY("提现查询", "WITHDRAW_QUERY"),
    WALLET_TRANSFER_APPLY("钱包转账申请", "WALLET_TRANSFER_APPLY"),
    WALLET_TRANSFER_QUERY("钱包转账结果查询", "WALLET_TRANSFER_QUERY"),
    /**
     * 手动提现补单
     */
    MANUAL_WITHDRAW_REPLENISHMENT("手动提现补单", "MANUAL_WITHDRAW_REPLENISHMENT"),
    ;

    private final String name;
    private final String value;

    ProduceEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public static ProduceEnum getByValue(String value) {
        ProduceEnum[] valueList = ProduceEnum.values();
        for (ProduceEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public String getValue() {
        return value;
    }
}