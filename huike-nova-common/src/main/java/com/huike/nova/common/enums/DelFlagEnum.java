/**
 * ailike.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.enums;

/**
 * 删除标记枚举类
 *
 * <AUTHOR>
 * @version DelFlagEnum.java, v 0.1 2022-02-08 14:16 zhangling
 */
public enum DelFlagEnum {
    /**
     * 未删除
     */
    NOT_DEL("未删除", 0),
    /**
     * 已删除
     */
    DEL("已删除", 1);

    private String name;
    private Integer value;

    DelFlagEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static DelFlagEnum getByValue(Integer value) {
        DelFlagEnum[] valueList = DelFlagEnum.values();
        for (DelFlagEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public Integer getValue() {
        return value;
    }
}