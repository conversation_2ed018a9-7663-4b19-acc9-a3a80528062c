package com.huike.nova.common.exception;


import com.huike.nova.common.enums.ReconciliationApiErrorEnum;

import java.text.MessageFormat;

/**
 * 拉卡拉交易异常
 *
 * <AUTHOR>
 * @version LakalaTradeException.java, v 0.1 2020-09-03 18:45 liluqing
 */
public class ReconciliationException extends BaseException {

    private static final long serialVersionUID = -8163047963952861904L;

    /**
     * 联动业务错误码
     */
    protected String bizCode;

    /**
     * 异常构造器
     */
    public ReconciliationException() {

    }


    public ReconciliationException(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ReconciliationException(String code, String msg, String bizCode) {
        this(code, msg);
        this.bizCode = bizCode;
    }

    /**
     * 异常构造器
     *
     * @param errorEnums
     */
    public ReconciliationException(ReconciliationApiErrorEnum errorEnums) {
        this(errorEnums.getCode(), errorEnums.getMsg());
    }

    /**
     * 实例化异常
     *
     * @param msgFormat
     * @param args
     * @return 异常类
     */
    @Override
    public ReconciliationException newInstance(String msgFormat, Object... args) {
        return new ReconciliationException(code, MessageFormat.format(msgFormat, args));
    }

    /**
     * 实例化包装异常
     *
     * @param msg
     * @param bizCode
     * @return 异常类
     */
    public ReconciliationException newBizErrorInstance(String msg, String bizCode) {
        return new ReconciliationException(code, msg, bizCode);
    }

    /**
     * Getter method for property <tt>bizCode</tt>.
     *
     * @return property value of bizCode
     */
    public String getBizCode() {
        return bizCode;
    }

    /**
     * Setter method for property <tt>bizCode</tt>.
     *
     * @param bizCode value to be assigned to property bizCode
     */
    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }
}