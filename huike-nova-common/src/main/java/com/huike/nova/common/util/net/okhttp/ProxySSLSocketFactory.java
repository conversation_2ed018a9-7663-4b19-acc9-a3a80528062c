package com.huike.nova.common.util.net.okhttp;

import com.google.common.annotations.Beta;
import com.huike.nova.common.util.net.ProxyConfigProviderConfigBuilder;

import javax.net.ssl.SSLSocketFactory;
import java.io.IOException;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.Socket;

/**
 * 为OKHttp增加Socks5代理
 *
 *
 * <AUTHOR> (<EMAIL>)
 * @version ProxySSLSocketFactory.java, v1.0 08/24/2023 16:39 John Exp$
 */
@Beta
public class ProxySSLSocketFactory extends SSLSocketFactory {
    private final ProxyConfigProviderConfigBuilder configProvider;
    private final SSLSocketFactory socketFactory;

    public ProxySSLSocketFactory(ProxyConfigProviderConfigBuilder configProvider, SSLSocketFactory socketFactory) {
        this.configProvider = configProvider;
        this.socketFactory = socketFactory;
    }

    @Override
    public String[] getDefaultCipherSuites() {
        return socketFactory.getDefaultCipherSuites();
    }

    @Override
    public String[] getSupportedCipherSuites() {
        return socketFactory.getSupportedCipherSuites();
    }

    public Socket createSocket() throws IOException {
        if (configProvider != null && configProvider.isEnableProxy()) {
            return new Socket(configProvider.getProxy());
        } else {
            return new Socket();
        }
    }

    public Socket createSocket(String host, int port)
            throws IOException {
        Socket socket = createSocket();
        try {
            return socketFactory.createSocket(socket, host, port, true);
        } catch (IOException e) {
            socket.close();
            throw e;
        }
    }

    public Socket createSocket(Socket s, String host, int port, boolean autoClose) throws IOException {
        //TODO 无法代理
        return socketFactory.createSocket(s, host, port, autoClose);
    }

    public Socket createSocket(InetAddress address, int port) throws IOException {
        Socket socket = createSocket();
        try {
            return socketFactory.createSocket(socket, address.getHostAddress(), port, true);
        } catch (IOException e) {
            socket.close();
            throw e;
        }
    }

    public Socket createSocket(String host, int port, InetAddress clientAddress, int clientPort) throws IOException {
        Socket socket = createSocket();
        try {
            socket.bind(new InetSocketAddress(clientAddress, clientPort));
            return socketFactory.createSocket(socket, host, port, true);
        } catch (IOException e) {
            socket.close();
            throw e;
        }
    }

    public Socket createSocket(InetAddress address, int port, InetAddress clientAddress, int clientPort) throws IOException {
        Socket socket = createSocket();
        try {
            socket.bind(new InetSocketAddress(clientAddress, clientPort));
            return socketFactory.createSocket(socket, address.getHostAddress(), port, true);
        } catch (IOException e) {
            socket.close();
            throw e;
        }
    }
}