/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version SystemConstants.java, v 0.1 2022-09-03 4:09 PM ruanzy
 */
public class SystemConstants {

    /**
     * json 请求头
     */
    public static final String HTTP_APPLICATION_JSON_UTF8_VALUE = "application/json;charset=utf-8";

    /**
     * 加密查询最小长度
     */
    public static final int ENCRYPT_QUERY_MIN_LENGTH = 3;

    /**
     * 发送短信最大次数(30分钟)
     */
    public static final int SEND_SMS_MAX_COUNT = 3;

    /**
     * 阿里云合成视频字体url
     */
    public static final String FONT_URL = "https://fs-cashier-mina-resources.oss-cn-hangzhou.aliyuncs.com/operation/font/%E6%96%87%E6%82%A6%E6%96%B0%E9%9D%92%E5%B9%B4%E4%BD%93.ttf";

    /**
     * 正则表达式去掉标点符号
     */
    public static final String TITLE_REGEX = "[\\pP\\pS\\pZ]";

    /**
     * 正则表达式去掉[，, 。～~；;!！？?]
     */
    public static final String SOUND_REGEX = "[，, 。；;!！？?……]";

    /**
     * 钉钉消息url
     */
    //public static final String URL = "da7bb9612b4aa4d3fe55f93b1b979e641c720a1afa7f21639949f640928df50f";

    /**
     * XXLjob钉钉通知url
     */
    //public static final String XXL_JOB_DING_URL = "https://oapi.dingtalk.com/robot/send?access_token=845a027e5c1fc203dc8b8521b4f60ea89f74ae21b1146cf5e64402a25febb700";

    /**
     * nginx / squid 代理头
     */
    public static final String X_FORWARDED_FOR = "x-forwarded-for";

    /**
     * apache 代理头
     */
    public static final String PROXY_CLIENT_IP = "Proxy-Client-IP";

    /**
     * apache webLogic 代理头
     */
    public static final String WL_PROXY_CLIENT_IP= "WL-Proxy-Client-IP";

    /**
     * 未知请求头
     */
    public static final String UNKNOWN = "unknown";

    /**
     * 回环地址
     */
    public static String LOOP_BACK_IP = "127.0.0.1";

    /**
     * 本应用的包名前缀
     */
    public static String HUIKE_NOVA_PAGE_PREFIX = "com.huike.nova.";
}