/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version AmapCommonConstant.java, v 0.1 2022-12-13 9:47 AM ruanzy
 */
public class AmapCommonConstant {


    /**
     * 同步发码验签失败
     */
    public final static String CALLBACK_FAILURE = "{\"biz_msg\":\"Illegal request\",\"biz_success\":\"{}\",\"biz_code\":\"{}\"}";


    /**
     * 同步发码成功
     */
    public final static String SEND_TICKET_SUCCESS = "{\"biz_msg\":\"交易已被支付\",\"biz_code\":\"success\",\"ma_list\":{},\"biz_success\":\"success\",\"sync_deliver\":\"true\",\"can_retry\":\"true\"}";

    /**
     * 高德查询券信息成功
     */
    public final static String QUERY_TICKET_SUCCESS = "{\"biz_msg\":\"成功\",\"biz_code\":\"success\",\"ma_list\":{},\"biz_success\":\"success\"}";

    /**
     * 地理编码API
     */
    public static final String API_GEOCODE = "https://restapi.amap.com/v3/geocode/geo";

    /**
     * 逆地理编码API
     */
    public static final String API_RE_GEOCODE = "https://restapi.amap.com/v3/geocode/regeo";

    /**
     * 高德Key
     * 用户在高德地图官网申请Web服务API类型Key
     */
    public static final String KEY = "key";

    /**
     * 结构化地址信息
     */
    public static final String ADDRESS = "address";

    /**
     * 指定查询的城市
     */
    public static final String CITY = "city";

    /**
     * 数字签名
     */
    public static final String SIG = "sig";

    /**
     * 状态
     */
    public static final String STATUS = "status";

    /**
     * 成功
     */
    public static final String STATUS_SUCCESS = "1";

    /**
     * 经纬度
     */
    public static final String LOCATION = "location";

}