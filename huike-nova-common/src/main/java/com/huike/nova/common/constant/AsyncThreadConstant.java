/*
 * ailike.com
 * Copyright (C) 2022-2022 All Rights Reserved.
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version AyncThreadContant.java, v 0.1 2022-09-08 09:27 zhangling
 */
public class AsyncThreadConstant {

    /**
     * 【异步统计视频播放量】异步线程名称
     */
    public static final String SYNC_PLAY_COUNT_EXECUTOR = "syncPlayCountExecutor";

    /**
     * 【异步统计视频播放量】异步线程名称
     */
    public static final String SEND_SUBSCRIBE_MESSAGE_EXECUTOR = "sendSubscribeMessageExecutor";


    /**
     * 【视频异步合成】异步线程名称
     */
    public static final String VIDEO_SYNC_SYNTHESIS = "videoSyncSynthesis";

    /**
     * 数据库日志
     */
    public static final String DB_ASYNC_LOG_EXECUTOR = "dbAsyncLogExecutor";

    /**
     * 告警线程池
     */
    public static final String ALARM_TASK_EXECUTOR = "alarmTaskExecutor";

    /**
     * 口播url补充线程池
     */
    public static final String SYNC_SOUND_URL_FILL_EXECUTOR = "syncSoundUrlFillExecutor";

    /**
     * 来探呗导出
     */
    public static final String STAR_TASK_EXPORT_EXECUTOR = "starTaskExportExecutor";

    /**
     * 重新分发Executor
     */
    public static final String REPUBLISH_EXECUTOR = "republishExecutor";

    /**
     * 转码Executor
     */
    public static final String MEDIA_TRANSCODE_EXECUTOR = "mediaTransCodeExecutor";

    /**
     * 请求抖音用户数据执行器
     */
    public static final String REQUEST_DOUYIN_USER_DATA_EXECUTOR = "requestDouyinUserDataExecutor";

    /**
     * 导入商铺线程池
     */
    public static final String IMPORT_SHOP_EXECUTOR = "importShopExecutor";

    /**
     * 商品变更通知执行器
     */
    public static final String PRODUCT_ALTERATION_NOTIFY_EXECUTOR = "productAlterationExecutor";
}