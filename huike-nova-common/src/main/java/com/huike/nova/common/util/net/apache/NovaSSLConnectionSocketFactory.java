package com.huike.nova.common.util.net.apache;

import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.util.net.NovaHostnameVerifier;
import com.huike.nova.common.util.net.NovaTrustManager;
import org.apache.http.HttpHost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContexts;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.Socket;
import java.util.Objects;

/**
 * APACHE连接管理
 *
 * <AUTHOR> (<EMAIL>)
 * @version NovaSSLConnectionSocketFactory.java, v1.0 08/25/2023 10:37 John Exp$
 */
public class NovaSSLConnectionSocketFactory extends SSLConnectionSocketFactory {

    @SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
    public static SSLContext getSSLContext() {
        SSLContext sslContext;
        try {
            sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{ NovaTrustManager.instance() }, null);

        } catch (Exception ex) {
            sslContext = SSLContexts.createSystemDefault();
        }
        return sslContext;
    }

    public NovaSSLConnectionSocketFactory() {
        this(getSSLContext());
    }

    public NovaSSLConnectionSocketFactory(SSLContext sslContext) {
        super(sslContext, NovaHostnameVerifier.instance());
    }

    @Override
    public Socket createSocket(HttpContext context) throws IOException {
        InetSocketAddress socksAddr = (InetSocketAddress) context.getAttribute(CommonConstant.SOCKS_ADDRESS);
        if (Objects.nonNull(socksAddr)) {
            // * SocketConnection仅支持5层代理
            Proxy proxy = new Proxy(Proxy.Type.SOCKS, socksAddr);
            return new Socket(proxy);
        } else {
            return new Socket();
        }
    }

    @Override
    public Socket connectSocket(int connectTimeout, Socket socket, HttpHost host, InetSocketAddress remoteAddress, InetSocketAddress localAddress, HttpContext context) throws IOException {
        // Convert address to unresolved
        InetSocketAddress unresolvedRemote = InetSocketAddress.createUnresolved(host.getHostName(), remoteAddress.getPort());
        return super.connectSocket(connectTimeout, socket, host, unresolvedRemote, localAddress, context);
    }
}
