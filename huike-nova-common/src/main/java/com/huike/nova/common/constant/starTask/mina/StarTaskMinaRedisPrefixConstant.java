/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.constant.starTask.mina;

/**
 * <AUTHOR>
 * @version StarTaskMinaRedisPrefixConstant.java, v 0.1 2023-11-24 2:48 PM ruanzy
 */
public class StarTaskMinaRedisPrefixConstant {

    /**
     * 短信发送缓存key（用于验证）
     * 第一个替换成小程序id
     * 第二个替换成场景类型
     * 第三个替换成手机号
     */
    public static final String STAR_TASK_MINA_CACHE_KEY_SMS_VERIFY = "star.task.mina.cache.sms.verify.{}.{}.{}";

    /**
     * 短信发送次数缓存key（用于风控）
     * 第一个替换成oem配置id
     * 第二个替换成场景类型
     * 第三个替换成手机号
     */
    public static final String STAR_TASK_MINA_CACHE_KEY_SMS_REQUEST_COUNT = "star.task.mina.cache.sms.request.count.{}.{}.{}";

    /**
     * 短信发送缓存key(用于风控)
     * 第一个替换成oem配置id
     * 第二个替换成场景类型
     * 第三个替换成手机号
     */
    public static final String STAR_TASK_MINA_CACHE_KEY_SMS_REQUEST = "star.task.mina.cache.sms.request.{}.{}.{}";

    /**
     * 报名清单更新状态幂等缓存
     * 第一个替换成报名id
     */
    public static final String STAR_TASK_MINA_CACHE_KEY_OPERATE_APPLY_LIST = "star.task.mina.cache.key.operate.apply.list.{}";

    /**
     * 发布任务修改接口幂等缓存
     */
    public static final String STAR_TASK_MINA_CACHE_KEY_UPDATE_STAR_TASK = "star.task.mina.cache.key.update.star.task.{}";

    /**
     * 任务更新状态幂等缓存
     * 第一个替换成任务id
     */
    public static final String STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR_TASK = "star.task.mina.cache.key.operate.star.task.{}";

    /**
     * 更新达人数据接口幂等缓存
     */
    public static final String STAR_TASK_MINA_CACHE_KEY_OPERATE_STAR = "star.task.mina.cache.key.operate.star.{}";

    /**
     * 账户余额更新幂等缓存
     * 第一个替换成身份id
     */
    public static final String STAR_TASK_MINA_CACHE_KEY_APPLY_WITHDRAWAL = "star.task.mina.cache.key.apply.withdrawal.{}";

    /**
     * 更新佣金计划数量幂等缓存
     */
    public static final String STAR_TASK_MINA_CACHE_KEY_UPDATE_PARTICIPATION_COUNT = "star.task.mina.cache.key.update.participation.count.{}";

    /**
     * 达人激励广场，支付一次Token
     */
    public static final String STAR_TASK_PAYMENT_PAY_TOKEN = "star.task.payment.pay.token.{}";

    /**
     * 达人激励广场 小程序access_token
     */
    public static final String STAR_TASK_MINA_ACCESS_TOKEN = "star.task.mina.access_token.{}";

    /**
     * 达人激励广场，小程序配置
     */
    public static final String STAR_TASK_MINA_CONFIG = "star.task.mina.config.{}";

    /**
     * rpa操作达人缓存
     */
    public static final String STAR_TASK_MINA_CACHE_KEY_RPA_OPERATE_STAR = "star.task.mina.cache.key.rpa.operate.star.{}";

    /**
     * 达人激励广场，订单支付成功
     */
    public static final String STAR_TASK_MINA_ORDER_LOCK = "star.task.mina.order.{}";

    /**
     * 首字母地区排序缓存
     */
    public static final String STAR_TASK_MINA_CACHE_KEY_REGION_SORTER = "star.task.mina.cache.key.region.sorter";

    /**
     * 达人广场
     */
    public static final String STAR_TASK_MINA_CACHE_STAR_SQUARE_SUMMARY = "star.task.mina.cache.star_square_summary";

    /**
     * 身份认证
     * 第1个替换成身份id
     */
    public static final String STAR_TASK_MINA_CACHE_AUTHENTICATE = "star.task.mina.cache.authenticate.{}";
}