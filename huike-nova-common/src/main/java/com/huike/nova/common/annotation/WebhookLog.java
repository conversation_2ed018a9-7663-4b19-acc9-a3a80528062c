package com.huike.nova.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 *
 * <AUTHOR>
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Documented
public @interface WebhookLog {

    /**
     * 描述
     */
    String desc() default "";
}
