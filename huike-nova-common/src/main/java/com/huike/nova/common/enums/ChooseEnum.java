package com.huike.nova.common.enums;

/**
 * 枚举类型，表示选择或不选择状态。
 *
 * <AUTHOR>
 */
public enum ChooseEnum {
    /**
     * 选择状态。
     */
    SELECT(1, "选择"),

    /**
     * 不选择状态。
     */
    NOT_SELECT(2, "不选择");

    /**
     * 枚举常量对应的整数值。
     */
    private final int value;

    /**
     * 枚举常量对应的状态描述。
     */
    private final String desc;

    /**
     * 构造函数。
     *
     * @param value 整数值
     * @param desc  状态描述
     */
    ChooseEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 获取枚举常量对应的整数值。
     *
     * @return 整数值
     */
    public int getValue() {
        return value;
    }

    /**
     * 获取枚举常量对应的状态描述。
     *
     * @return 状态描述
     */
    public String getDesc() {
        return desc;
    }

}