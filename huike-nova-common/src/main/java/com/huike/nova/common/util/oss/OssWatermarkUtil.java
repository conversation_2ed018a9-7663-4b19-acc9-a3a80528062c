/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.huike.nova.common.util.oss;

import cn.hutool.core.util.ReflectUtil;
import com.huike.nova.common.util.oss.param.OssWatermarkCommonParam;

import java.lang.reflect.Field;
import java.util.List;

/**
 * <AUTHOR>
 * @version OssWatermarkUtil.java, v 0.1 2019-04-22 15:53 buhao
 */
public class OssWatermarkUtil {

    public static <T extends OssWatermarkCommonParam> String buildOssWatermarkParam(List<T> ossParams) {

        // 参数头
        StringBuilder ossParamText = new StringBuilder("?x-oss-process=image");
        // 拼接参数
        for (T param : ossParams) {
            StringBuilder sb = new StringBuilder("/watermark,");
            Field[] fields = ReflectUtil.getFields(param.getClass());
            for (Field field : fields) {
                String name = field.getName();
                Object value = ReflectUtil.getFieldValue(param, field);
                if (value != null) {
                    sb.append(name).append("_").append(value).append(",");
                }
            }
            ossParamText.append(sb.substring(0, sb.length() - 1));
        }
        return ossParamText.toString();
    }
}