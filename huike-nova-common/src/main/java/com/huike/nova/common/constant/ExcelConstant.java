/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version ExcelConstant.java, v 0.1 2023-03-30 9:19 AM ruanzy
 */
public class ExcelConstant {

    /**
     * 顾客推明细导出文件名前缀
     */
    public final static String EXPORT_VIDEO_DATA_PREFIX = "来团呗-视频发布明细统计";

    /**
     * 视频数据导出模版
     */
    public final static String TEMPLATE_VIDEO_FILE_NAME = "video-detail-template.xlsx";

    /**
     * 客资宝导出文件名前缀
     */
    public final static String EXPORT_CUSTOMER_DATA_PREFIX = "客资导出列表";

    /**
     * 客资宝数据导出模版
     */
    public final static String TEMPLATE_CUSTOMER_DATA_FILE_NAME = "customer-data-template-20231129.xlsx";

    /**
     * 达人矩阵导出文件名前缀
     */
    public final static String EXPORT_STAR_PREFIX = "达人矩阵导出";

    /**
     * 报名清单导出模板文件名称
     */
    public final static String TEMPLATE_APPLY_LIST_FILE_NAME = "报名清单导出模板.xlsx";

    /**
     * 商家战队成员导出模板文件名称
     */
    public final static String TEMPLATE_MERCHANT_TEAM_FILE_NAME = "商家战队成员.xlsx";

    /**
     * 达人团队成员导出模板文件名称
     */
    public final static String TEMPLATE_STAR_TEAM_FILE_NAME = "达人团队成员.xlsx";

    /**
     * 商家战队-返还明细导出模板文件名称
     */
    public final static String TEMPLATE_MERCHANT_REWARD_DETAIL_FILE_NAME = "商家战队-返还明细.xlsx";

    /**
     * 达人团队-返还明细导出模板文件名称
     */
    public final static String TEMPLATE_STAR_REWARD_DETAIL_FILE_NAME = "达人团队-返还明细.xlsx";
}