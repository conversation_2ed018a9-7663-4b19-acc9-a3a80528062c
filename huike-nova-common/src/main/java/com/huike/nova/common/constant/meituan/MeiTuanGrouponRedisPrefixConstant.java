package com.huike.nova.common.constant.meituan;

/**
 * 美团团购Redis配置
 *
 * <AUTHOR> (<EMAIL>)
 * @version MeiTuanGrouponRedisPrefixConstant.java, v1.0 05/30/2024 17:09 John Exp$
 */
public interface MeiTuanGrouponRedisPrefixConstant {

    /**
     * 美团点评AppKey密钥
     */
    String DIAN_PING_APP_KEY_SECRET = "meituan.dianping.app-secret.{}";

    /**
     * 三方码券操作，传参美团点评的订单号
     */
    String DIAN_PING_THIRD_PARTY_RECEIPT_OPERATE_KEY = "meituan.dianping.receipt-operate.{}";

    /**
     * 卡券生成使用RedisKey
     */
    String MEITUAN_GROUPON_COUPON_GENERATE_KEY = "biz.meituan.groupon.coupon-code-gen";

    /**
     * 美团点评接口的SessionKey
     * 参数使用集团的AppId
     */
    String DZ_OPEN_API_SESSION_KEY = "biz.meituan.product.session.redis.key.{}";

    /**
     * 美团点评团购券码核销，Key为美团点评的订单Id
     */
    String MEITUAN_CERTIFICATE_VERIFY_KEY = "biz.meituan.certificate.verify.{}";
}
