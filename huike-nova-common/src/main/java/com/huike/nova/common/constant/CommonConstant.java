/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.constant;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.math.BigDecimal;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version CommonContants.java, v 0.1 2022-09-05 7:05 PM ruanzy
 */
public class CommonConstant {

    /**
     * 锁等待时间
     */
    public final static Integer ONE_SECOND = 1;

    public final static Integer THREE_SECOND = 3;

    public final static Integer TEN_SECOND = 10;

    public static final Integer THIRTY = 30;

    public static final Integer TRUE = 1;

    public static final Integer FALSE = 0;

    /**
     * 锁等待时间
     */
    public final static Integer EMPLOYEE_ADD_THREE_SECOND = 3;

    public final static Integer EMPLOYEE_ADD_TEN_SECOND = 10;

    public final static Integer VIDEO_REPEAT_COUNT_TWO_SECOND = 2;

    public static final String NEWLINE = "\n";
    /**
     * 五分钟秒数
     */
    public final static Integer FIVE_MINUTE_SECONDS = 300;

    /**
     * 业务True
     */
    public static final Integer BIZ_TRUE = 1;

    /**
     * 业务False
     */
    public static final Integer BIZ_FALSE = 2;

    /**
     * 零
     */
    public final static Integer ZERO = 0;

    /**
     * 常量1
     */
    public final static Integer INTEGER_ONE = 1;

    /**
     * 常量1String
     */
    public final static String STRING_ONE = "1";

    /**
     * 常量2
     */
    public final static Integer INTEGER_TWO = 2;

    /**
     * 常量3
     */
    public final static Integer INTEGER_THREE = 3;

    /**
     * 常量4
     */
    public final static Integer INTEGER_FOUR = 4;

    /**
     * 常量5
     */
    public final static Integer INTEGER_FIVE = 5;

    /**
     * 常量6
     */
    public final static Integer INTEGER_SIX = 6;

    /**
     * 常量7String
     */
    public final static String STRING_SEVEN = "7";

    /**
     * 数字7
     */
    public final static Integer INTEGER_SEVEN = 7;

    /**
     * 常量8
     */
    public final static Integer INTEGER_EIGHT = 8;

    /**
     * 常量10
     */
    public final static Integer INTEGER_TEN = 10;

    /**
     * 常量28
     */
    public final static Integer INTEGER_TWENTY_EIGHT = 28;

    /**
     * 常量50
     */
    public final static Integer INTEGER_FIFTY = 50;

    /**
     * 常量17
     */
    public final static Integer INTEGER_SEVENTEEN = 17;
    /**
     * 常量100
     */
    public final static Integer INTEGER_HUNDRED = 100;
    /**
     * 常量255
     */
    public final static Integer INTEGER_255 = 255;
    /**
     * 常量2000
     */
    public final static Integer INTEGER_TWO_THOUSAND = 2000;

    /**
     * 常量20
     */
    public final static Integer INTEGER_TWENTY = 20;

    /**
     * playCount 播放数
     */
    public final static String PLAY_TOTAL = "playTotal";

    /**
     * browseCount 浏览数
     */
    public final static String BROWSE_TOTAL = "browseTotal";

    /**
     * visitCount 访问数
     */
    public final static String VISIT_TOTAL = "visitTotal";

    /**
     * 视频发布数
     */
    public final static String RELEASE_TOTAL = "releaseTotal";

    /**
     * 视频参数数
     */
    public final static String PARTICIPATE_TOTAL = "participateTotal";
    /**
     * 门店名称
     */
    public final static String STORE_NAME = "storeName";

    /**
     * 视频总数
     */
    public final static String VIDEO_TOTAL = "total";
    /**
     * 评论数
     */
    public final static String COMMENT_TOTAL = "commentTotal";
    /**
     * 分享数
     */
    public final static String SHARE_TOTAL = "shareTotal";
    /**
     * 订单数
     */
    public final static String ORDER_TOTAL = "orderTotal";
    /**
     * 点赞数
     */
    public final static String LIKE_TOTAL = "likeTotal";

    /**
     * 时间
     */
    public final static String TIME = "time";

    /**
     * 视频url前缀
     */
    public final static String VIDEO_URL_PREFIX = "https://www.iesdouyin.com/share/video/";

    /**
     * 默认REFRESH_TOKEN截止时间
     */
    public final static Integer REFRESH_TOKEN_EXPIRY = 60 * 60 * 24 * 29;
    /**
     * 抖音视频可见范围 公开状态
     */
    public final static Integer TIKTOK_VIDEO_STATUS = 5;

    /**
     * AccessToken
     */
    public static final String ACCESS_TOKEN = "access-token";

    /**
     * sign
     */
    public final static String SIGN = "sign";

    /**
     * timestamp
     */
    public final static String TIMESTAMP = "timestamp";

    /**
     * nonce
     */
    public final static String NONCE = "nonce";

    /**
     * salt
     */
    public final static String SALT = "salt";

    /**
     * utf-8
     */
    public final static String UTF8 = "utf-8";

    /**
     * url
     */
    public final static String URL = "url";

    /**
     * jsapi_ticket
     */
    public final static String JSAPI_TICKET = "jsapi_ticket";

    /**
     * nonce_str
     */
    public final static String NONCE_STR = "nonce_str";

    /**
     * ticket
     */
    public final static String TICKET = "ticket";

    /**
     * content
     */
    public final static String SIGN_CONTENT = "content";

    /***********  回调常量 *************/

    /**
     * 抖音回调content
     */
    public final static String CONTENT = "content";

    /**
     * 抖音回调event
     */
    public final static String EVENT = "event";

    /**
     * 抖音回调challenge
     */
    public final static String CHALLENGE = "challenge";

    /**
     * 抖音回调item_id
     */
    public final static String ITEM_ID = "item_id";

    /**
     * 抖音回调share_id
     */
    public final static String SHARE_ID = "share_id";

    /**
     * 抖音回调video_id
     */
    public final static String VIDEO_ID = "video_id";

    /**
     * 视频合成回调MessageBody
     */
    public final static String MESSAGE_BODY = "MessageBody";

    /**
     * 视频合成回调JobId
     */
    public final static String JOB_ID = "JobId";

    /**
     * 视频合成回调Status
     */
    public final static String STATUS = "Status";


    /***********  获取Schema常量 *************/

    /**
     * Schema常量-share_type
     */
    public final static String SHARE_TYPE = "share_type";

    /**
     * Schema常量-H5
     */
    public final static String H5 = "h5";

    /**
     * Schema常量-client_key
     */
    public final static String CLIENT_KEY = "client_key";

    /**
     * 用户Id（openId）
     */
    public static final String FROM_USER_ID = "from_user_id";

    /**
     * Schema常量-signature
     */
    public final static String SIGNATURE = "signature";

    /**
     * Schema常量-state
     */
    public final static String STATE = "state";

    /**
     * Schema常量-video_path
     */
    public final static String VIDEO_PATH = "video_path";

    /**
     * Schema常量-image_list_path
     */
    public final static String IMAGE_LIST_PATH = "image_list_path";

    /**
     * Schema常量-share_to_publish
     */
    public final static String SHARE_TO_PUBLISH = "share_to_publish";

    /**
     * Schema常量-1
     */
    public final static String ONE_STR = "1";

    /**
     * 常量-1
     */
    public final static String NEGATIVE_ONE = "-1";

    /**
     * 常量-1
     */
    public final static Integer INTEGER_NEGATIVE_ONE = -1;

    /**
     * Schema常量-hashtag_list
     */
    public final static String HASHTAG_LIST = "hashtag_list";

    /**
     * Schema常量-title
     */
    public final static String TITLE = "title";

    /**
     * Schema常量-poi_id
     */
    public final static String POI_ID = "poi_id";

    /**
     * 阿里云输出成品的目标配置
     */
    public final static String OUT_PUT_MEDIA_CONFIG = "{\"MediaURL\":\"{}\",\"Width\":\"{}\",\"Height\":\"{}\",\"Bitrate\":\"{}\"}";

    /**
     * 阿里云剪辑工程的元数据信息
     */
    public final static String PROJECT_METADATA = "{\"title\":\"{}\"}";

    /**
     * 阿里云剪辑工程的自定义设置
     */
    public final static String USER_DATA = "{\"NotifyAddress\":\"{}\"}";

    /**
     * 输出成品的宽
     */
    public final static Integer WIDTH = 1080;
    /**
     * 抖音视频排序字段-playCount
     */
    public final static String PLAY_COUNT = "playCount";

    /**
     * 抖音视频排序字段-likeCount
     */
    public final static String LIKE_COUNT = "likeCount";

    /**
     * 抖音视频排序字段-commentCount
     */
    public final static String COMMENT_COUNT = "commentCount";

    /**
     * 抖音视频排序字段-shareCount
     */
    public final static String SHARE_COUNT = "shareCount";

    /**
     * 抖音视频排序字段-orderCount
     */
    public final static String ORDER_COUNT = "orderCount";

    /***********  小程序回调常量 *************/

    /**
     * 枚举值
     */
    public final static String TYPE = "type";

    /**
     * 错误码
     */
    public final static String ERR_NO = "err_no";

    /**
     * 错误提示
     */
    public final static String ERR_TIPS = "err_tips";

    /**
     * JSON 字符串
     */
    public final static String MSG = "msg";

    /**
     * 返回数据信息
     */
    public final static String DATA = "data";

    /**
     * 成功信息
     */
    public final static String SUCCESS = "success";

    /**
     * 失败
     */
    public static final String FAILED = "failed";

    /**
     * 绿地会员返回开卡中状态
     */
    public final static Integer CARD_OPENING = 30018;
    /**
     * 绿地会员返回未开卡状态
     */
    public final static Integer CARD_NOT_OPENING = 30016;
    /**
     * 15秒的时间
     */
    public final static Integer FIFTEEN_SECONDS = 15000;

    /**
     * 成功值
     */
    public final static Integer SUCCESS_CODE = 0;

    public final static Integer SUCCESS_STATUS_CODE = 200;

    public static final Integer PAGE_SIZE_200 = 200;

    public static final Integer PAGE_SIZE_2000 = 2000;

    public static final Integer PAGE_SIZE_500 = 500;

    public final static Integer SUCCESS_REGISTER_MEMBER_CODE = 3010;

    public static final String CODE = "code";

    public static final String MESSAGE = "message";

    /**
     * 错误值，403
     */
    public final static Integer SIGNATURE_VERIFY_FAILED = 403;

    /**
     * 抖音视频排序字段-shareCount
     */
    public final static String RELEASE_TIME = "releaseTime";

    /**
     * 输出成品的高
     */
    public final static Integer HEIGHT = 1920;

    /**
     * 输出成品的码率
     */
    public final static Integer BITRATE = 5000;
    /**
     * 排序规则-正序
     */
    public final static String ASC = "asc";
    /**
     * 排序规则-倒序
     */
    public final static String DESC = "desc";

    /**
     * oss缩略图后缀--图片
     */
    public final static String OSS_SUFFIX = "?x-oss-process=style/thumbnail";

    /**
     * x-oss-process
     */
    public final static String X_OSS_PROCESS = "x-oss-process";

    /**
     * oss缩略图后缀--视频
     */
    public final static String OSS_VIDEO_SUFFIX = "?x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast";

    /**
     * 读一个汉字的时间
     */
    public final static String HZ_TIME = "0.21";

    /**
     * 读一个数字或字母的时间
     */
    public final static String SZ_ZM_TIME = "0.7";

    /**
     * 匹配汉字正则表达式
     */
    public final static String HZ_REGEX = "[\u4e00-\u9fa5]";

    /**
     * 是否长期有效
     */
    public final static String IS_LONG = "长期有效";

    /**
     * 常量1
     */
    public final static int ONE = 1;

    public final static int SECOND = 2;

    public final static int THREE = 3;

    public static final String EMPTY_STR = "null";

    /**
     * 默认日期
     */
    public static final Date DEFAULT_DATE = DateUtil.parseDateTime("1970-01-01 00:00:00");

    public static final Map<String, String> SORT_FIELD_MAP = Maps.newHashMap();

    public static final CharSequence V_DOUYIN_COUPON_URL = "https://v.douyin.com/";
    /**
     * message模板:活动/点单订单 0-单号 1-是否外卖
     */
    public final static String PUSH_ONCE_MESSAGE = "您有一笔新订单[=dan1],请及时处理";
    /**
     * message模板:打印漏单 0-单号 1-是否外卖
     */
    public final static String PRINT_MISSING_MESSAGE = "您的 外[=wai4]麦 订单[=dan1]尚未打印，请检查";
    /**
     * message模板:未处理订单(轮巡播报) 0-单号 1-是否外卖
     */
    public final static String PUSH_CYCLE_MESSAGE = "您的 外[=wai4]麦 订单[=dan1]尚未处理，请注意";
    /**
     * message模板:退款订单播报
     */
    public final static String PUSH_REFUND_MESSAGE = "有用户发起退款，请及时处理";
    public final static String DEFAULT_TAKEOUT = "001";
    /**
     * 换行
     */
    public final static String LINE_FEED_LABEL = "<BR>";
    /**
     * 自定义字体大小标签
     */
    public final static String CUSTOM_FONT_SIZE = "<CS:{}>";
    public final static String HTTP_PREFIX = "http";
    /**
     * 常量-千
     */
    public final static Long LONG_THOUSAND = 1000L;
    /**
     * 常量-Long零
     */
    public final static Long LONG_ZERO = 0L;
    /**
     * 常量-Long 1
     */
    public final static Long LONG_ONE = 1L;
    /**
     * 常量-Long 20
     */
    public final static Long LONG_TWO_ZERO = 20L;
    /**
     * 常量-Float零
     */
    public final static Float FLOAT_ZERO = 0F;
    /***
     * 分割线
     */
    public final static String SEPARATOR = "------------------------";
    /**
     * 字符串0
     */
    public final static String STR_ZERO = "0";
    /**
     * 字符串000
     */
    public static final String STR_TRIPLE_ZERO = "000";
    /**
     * 结账单打印小票默认配置
     */
    public static final String DEFAULT_FRONT_RECEIPT_CONFIG = "[{\"content\":\"\",\"fontSize\":1,\"label\":\"STORE_NAME\",\"name\":\"门店名称\",\"switchStatus\":1},{\"content\":\"\",\"fontSize\":3,\"label\":\"TAKEOUT_NO\",\"name\":\"外卖号\",\"switchStatus\":1},{\"content\":\"\",\"fontSize\":3,\"label\":\"SERVE_TIME\",\"name\":\"送达时间\",\"switchStatus\":1},{\"content\":\"\",\"fontSize\":3,\"label\":\"REMAKE\",\"name\":\"备注\",\"switchStatus\":1},{\"content\":\"\",\"fontSize\":1,\"label\":\"GOODS_INFO\",\"name\":\"商品信息\",\"switchStatus\":1},\n" +
            "{\"content\":\"\",\"fontSize\":2,\"label\":\"ORDER_AMOUNT\",\"name\":\"订单金额\",\"switchStatus\":1},{\"content\":\"\",\"fontSize\":2,\"label\":\"CONSIGNEE_INFO\",\"name\":\"收货人信息\",\"switchStatus\":1},{\"content\":\"欢迎再次点单\",\"fontSize\":1,\"label\":\"END_WOEDS\",\"name\":\"结束语\",\"switchStatus\":1}]";
    /**
     * 后厨单打印小票默认配置
     */
    public static final String DEFAULT_KITCHEN_RECEIPT_CONFIG = "[\n" +
            "  {\n" +
            "     \"label\": \"STORE_NAME\",\n" +
            "     \"name\": \"门店名称\",\n" +
            "     \"switch_status\": 2,\n" +
            "     \"font_size\": 3,\n" +
            "     \"content\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"label\": \"TAKEOUT_NO\",\n" +
            "    \"name\": \"外卖号\",\n" +
            "    \"switch_status\": 1,\n" +
            "    \"font_size\": 3,\n" +
            "    \"content\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"label\": \"SERVE_TIME\",\n" +
            "    \"name\": \"送达时间\",\n" +
            "    \"switch_status\": 1,\n" +
            "    \"font_size\": 3,\n" +
            "    \"content\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"label\": \"REMAEK\",\n" +
            "    \"name\": \"备注\",\n" +
            "    \"switch_status\": 1,\n" +
            "    \"font_size\": 2,\n" +
            "    \"content\": \"\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"label\": \"GOODS_INFO\",\n" +
            "    \"name\": \"商品信息\",\n" +
            "    \"switch_status\": 1,\n" +
            "    \"font_size\": 3,\n" +
            "    \"content\": \"\"\n" +
            "  }\n" +
            "]";
    /**
     * 门店素材默认分组数量
     */
    public static final String STORE_MATERIAL_DEFAULT_GROUP_COUNT = "[\n" +
            "    {\n" +
            "        \"materialGroupId\":\"-1\",\n" +
            "        \"materialGroupName\":\"全部\",\n" +
            "        \"count\":0,\n" +
            "        \"sort\":\"-1\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"materialGroupId\":\"0\",\n" +
            "        \"materialGroupName\":\"未分组\",\n" +
            "        \"count\":0,\n" +
            "        \"sort\":\"99999\"\n" +
            "    }\n" +
            "]";
    /**
     * 视频贴图列表
     */
    public static final String[] VIDEO_IMAGE_LIST = {"https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/10.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/12.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/16.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/17.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/2.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/22.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/24.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/27.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/29.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/3.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/31.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/35.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/36.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/41.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/43.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/44.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/46.gif", "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/image/8.gif"};
    /**
     * 分页偏移量
     */
    public static final Integer PAGE_LIMIT = 1000;
    public static final Integer NUMBER_FIVE = 5;

    public static final Integer NUMBER_ZERO = 0;

    public static final BigDecimal ONE_HUNDRED = new BigDecimal(100);
    /**
     * 默认口播声音
     */
    public static final String DEFAULT_ICE_VOICE = "YunXi";
    /**
     * 手动合成-批次上限
     */
    public final static Integer MANUAL_SYNTHESIS_LOT_LIMIT = 100;
    /**
     * 视频封面缩略图后缀
     */
    public final static String VIDEO_COVER_THUMBNAIL_SUFFIX = "{}?x-oss-process=style/thumbnail";
    /**
     * Http GET方法
     */
    public static final String HTTP_METHOD_GET = "GET";
    /**
     * Http POST方法
     */
    public static final String HTTP_METHOD_POST = "POST";
    /**
     * 默认的JSON对象
     */
    public static final JSONObject DEFAULT_JSON_OBJECT = new JSONObject();
    /**
     * 默认的JSON数组
     */
    public static final JSONArray DEFAULT_JSON_ARRAY = new JSONArray();
    /**
     * 钉钉Url Scheme Prefix
     */
    public static final String DING_TALK_URL_SCHEME_PREFIX = "dingtalk://dingtalkclient/page/link?url=";
    /**
     * 时区设置：GMT+8
     */
    public static final ZoneOffset GMT_8 = ZoneOffset.ofHours(8);
    /**
     * 日志TraceId
     */
    public static final String TRACE_ID = "TRACE_ID";
    /**
     * 来团呗TraceId
     */
    public static final String LTB_TRACE_ID = "ltb-trace-id";
    /**
     * 来团呗广告标识ossUrl
     */
    public static final String AD_LOGO_OSS_URL = "https://laituanbei.oss-cn-hangzhou.aliyuncs.com/commonBasic/clip/image/promptImage/ad.png";
    /**
     * 心动大牌展示标识
     */
    public static final String PRODUCT_ACTIVITY_BACKGROUND_SHOW_MARK = "【心动616】";
    /**
     * 应用名称
     */
    public static final String APPLICATION_NAME = "huike-nova";
    /**
     * 空行标志
     */
    public static final char[] BLANK_SYMBOLS = "\r\n\t ".toCharArray();
    /**
     * 简单XOR加密解密用
     */
    public static final String XOR_IV = "5b1hnQK8GVHQFJqUruJ5bHW6ZDGeLthH";
    /**
     * 一次导出的最大数据
     */
    public final static Integer EXPORT_MAX_COUNT = 100000;
    /**
     * 阿里妈妈黑体
     */
    public static final String ALIMAMA_SHU_HEI_TI = "Alimama ShuHeiTi";
    /**
     * 默认的URLPattern
     */
    public static final String EXPRESSION_URL_NONE = "";
    /**
     * 正则表达式：移动端的UserAgent
     */
    public static final String EXPRESSION_MOBILE_USER_AGENT = "(Android|iPhone OS)";
    /**
     * 正则表达式：URL链接
     */
    public static final String EXPRESSION_HTTP_URL = "^https?://";
    /**
     * 正则：移动端的UserAgent
     */
    public static final Pattern PATTERN_MOBILE_USER_AGENT = Pattern.compile(EXPRESSION_MOBILE_USER_AGENT);
    /**
     * 正则URL
     */
    public static final Pattern PATTERN_HTTP_URL = Pattern.compile(EXPRESSION_HTTP_URL);
    /**
     * 代理商Id
     */
    public static final String AGENT_MCN_ID = "agentMcnId";
    /**
     * 商户Id
     */
    public static final String MERCHANT_ID = "merchantId";
    /**
     * 门店Id
     */
    public static final String STORE_ID = "storeId";
    /**
     * 绑定Id
     */
    public static final String SOURCE = "source";
    /**
     * 用户UA
     */
    public static final String HEADER_USER_AGENT = "User-Agent";
    /**
     * 网易音乐前缀
     */
    public static final String NET_EASE_MUSIC_PREFIX = "https://music.163.com/song/media/outer/url?id={}.mp3";
    /**
     * 语音处理URL
     */
    public static final String SPEECH_PROCESS_URL = "https://nls-gateway.cn-shanghai.aliyuncs.com/stream/v1/FlashRecognizer?appkey={}&token={}&format={}&sample_rate={}";
    /**
     * 语音处理AppKey
     */
    public static final String SPEECH_PROCESS_APP_KEY = "qc9XZcd8JS8XGvdV";
    /**
     * 来团呗抖音应用测试环境域名
     */
    public static final String TEST_LTB_TIKTOK_CLIENT_DOMAIN_NAME = "sp-test.51youdian.com";
    /**
     * 来团呗抖音应用生产环境域名
     */
    public static final String PROD_LTB_TIKTOK_CLIENT_DOMAIN_NAME = "sp.51youdian.com";
    /**
     * 商户视频合成批次限制
     */
    public static final Integer MERCHANT_CONFIG_BATCH_LIMIT = 20;
    /**
     * 商户视频合成总量限制
     */
    public static final Integer MERCHANT_CONFIG_TOTAL_LIMIT = 10000;
    /**
     * 时间戳初始格式字符串
     */
    public static final String INITIAL_TIME_STR = "1970-01-01 00:00:00";
    /**
     * 宝龙入会后缀
     */
    public static final String POWER_LONG_REGISTER_MEMBER_SUFFIX = "/ext/member/register";
    /**
     * 宝龙查询会员信息后缀
     */
    public static final String POWER_LONG_GET_MEMBER_BASE_SUFFIX = "/ext/member/getMemberBaseInfo";
    /**
     * 宝龙发送收货信息短信后缀
     */
    public static final String POWER_LONG_SEND_ADDRESS_SUFFIX = "/ext/message/sendRcvAddress";
    /**
     * 代理认证
     */
    public static final String PROXY_AUTHORIZATION = "Proxy-Authorization";
    /**
     * 连接地址
     */
    public static final String SOCKS_ADDRESS = "socks.address";
    /**
     * 空JSON对象
     */
    public static final JSONObject EMPTY_JSON_OBJECT = new JSONObject();
    /**
     * 空的json数组
     */
    public static final JSONArray EMPTY_JSON_ARRAY_OBJECT = new JSONArray();
    /**
     * 空对象
     */
    public static final Object EMPTY_OBJ = new Object();
    /**
     * 空的JSON数组字符串
     */
    public static final String EMPTY_JSON_ARRAY_STR = "[]";
    /**
     * 空的JSON对象
     */
    public static final String EMPTY_JSON_OBJECT_STR = "{}";
    /**
     * 需要交易权限的导航栏
     */
    public static final Collection<String> TRADE_PERMISSION_NAV_ITEMS = Lists.newArrayList("团购", "外卖");
    /**
     * 需要开通交易权限的
     */
    public static final Collection<String> TRADE_PERMISSION_MINE_RIBBONS_ITEMS = Lists.newArrayList("开票管理");
    /**
     * 顿号
     */
    public static final String DUN_HAO = "、";
    /**
     * 支付宝三方凭证发放路径
     */
    public static final String ALIPAY_THIRD_PARTY_CERTIFICATE_SEND_PATH = "/mina-order-callback/alipay-marketing-certificate-certification-send";
    /**
     * 支付宝三方凭证发放路径-测试
     */
    public static final String ALIPAY_THIRD_PARTY_CERTIFICATE_SEND_PATH_NEW = "/mina-order-callback/alipay-marketing-certificate-certification-send-new";
    /**
     * 服务原因
     */
    public static final String SERVICE_NAME = "fshows-gosh";
    /**
     * 换行符 CRLF
     */
    public static final String PATTERN_CRLF = "[\r\n\"]";
    /**
     * 达人过期原因：主动解绑
     */
    public static final String STAR_EXPIRED_REASON_IS_UNBIND_POSITIVE = "对方于 {} 解除{}授权";
    /**
     * 达人过期原因：授权过期
     */
    public static final String STAR_EXPIRED_REASON_IS_EXPIRED = "账号授权于 {} 过期";
    /**
     * 达人过期原因：账号临时过期
     */
    public static final String STAR_EXPIRED_REASON_EXPIRED_TEMPORARY = "账号授权过期, 等待自动续期";
    /**
     * 开始时分秒
     */
    public static final String START_TIME = " 00:00:00";
    /**
     * 结束时分秒
     */
    public static final String END_TIME = " 23:59:59";
    /**
     * 空格
     */
    public static final String SPACE = " ";
    /**
     * 同一天
     */
    public static final String SAME_DAY = "SAME_DAY";
    /**
     * 次日
     */
    public static final String NEXT_DAY = "NEXT_DAY";
    /**
     * GPT回复错误
     */
    public static final String GPT_REPLY_ERROR = "非常抱歉，获取回答时出现了问题，请稍后再重试";
    /**
     * GPT服务忙
     */
    public static final String GPT_REPLY_SERVER_PROVIDER_BUSY = "非常抱歉，目前系统繁忙，无法立即回答您的问题，请重新提问下试试";
    /**
     * 临时文件目录
     */
    public static final String TEMP_DIR = System.getProperty("java.io.tmpdir");
    /**
     * https:// 前缀
     */
    public static final String HTTPS_PREFIX = "https://";
    /**
     * http:// 前缀
     */
    @SuppressWarnings("HttpUrlsUsage")
    public static final String HTTP_SLASH_PREFIX = "http://";
    /**
     * .jpg后缀
     */
    public static final String JPG_SUFFIX = ".jpg";
    /**
     * .jpeg
     */
    public static final String JPEG_SUFFIX = ".jpeg";
    /**
     * .png
     */
    public static final String PNG_SUFFIX = ".png";
    /**
     * 默认日期
     */
    public static final String DEFAULT_TIME = "1970-01-01";
    public static final String WECHAT_API_SEND_SUBSCRIBE_MESSAGE_URL = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={0}";
    /**
     * 小程序/视频号 支付通道: 微信
     */
    public static final int MINA_ORDER_PAY_CHANNEL_WECHAT = 1;
    /**
     * 履约成功
     */
    public static final String VERIFY_COUPON_SUCCESS = "履约成功";
    /**
     * 转码
     */
    public static final String TRANSCODE = "Transcode";
    /**
     * 绿地会员注册
     */
    public static final String GREEN_LAND_MEMBER_MEMBER_REGISTER_HOST = "https://openapi10.mallcoo.cn/User/MallCard/v1/Open/ByMobile/";
    /**
     * 绿地会员查询
     */
    public static final String GREEN_LAND_MEMBER_MEMBER_QUERY_HOST = "https://openapi10.mallcoo.cn/User/AdvancedInfo/v1/Get/ByMobile/";
    /**
     * 数据源名称：只读库
     */
    public static final String READ_ONLY_DB_DATA_SOURCE = "read";
    public static final String MASTER = "master";
    /**
     * 权益卡商品类型
     */
    public static final Integer qykProductType = 111;
    /**
     * 反斜杠
     */
    public static final String SLASH = "/";
    /**
     * CONTENT_TYPE
     */
    public final static String CONTENT_TYPE_JSON = "application/json";
    /**
     * 抖音商家应用Token有效期:秒 5分钟提前10秒
     */
    public final static Long SERVICE_TOKEN_EXPIRED_IN_SEC = 60 * 5 - 10L;
    /**
     * 安卓userAgent
     */
    public final static String USER_AGENT_ANDROID = "com.bytedance.ls.merchant/80300 (Linux; U; Android 6.0; zh_CN; Redmi Note 4; Build/MRA58K; Cronet/TTNetVersion:7e01029a 2022-02-10 QuicVersion:68cae75d 2021-08-12)accept-encoding: gzip, deflate, br";
    /**
     * 签名方法
     */
    public final static String SIGN_METHOD_HMAC = "HMAC";
    /**
     * 调用支付宝成功code
     */
    public static final String ALIPAY_SUCCESS_CODE = "10000";
    /**
     * nginx / squid 代理头
     */
    public static final String X_FORWARDED_FOR = "x-forwarded-for";
    /**
     * apache 代理头
     */
    public static final String PROXY_CLIENT_IP = "Proxy-Client-IP";
    /**
     * apache webLogic 代理头
     */
    public static final String WL_PROXY_CLIENT_IP = "WL-Proxy-Client-IP";
    /**
     * http连接超时错误描述
     */
    public final static String CONNECT_TIMED_OUT_ERROR_MSG = "connect timed out";
    /**
     * 未知请求头
     */
    public static final String UNKNOWN = "unknown";
    /**
     * 显示通道
     */
    public static final String SHOW_CHANNEL_KEY = "show_channel";

    /**
     * 使用规则
     */
    public static final String USE_DATE = "use_date";
    
    /**
     * 用户自助核销列表显示
     */
    public static final String VERIFICATION_OF_USERS = "用户自助核销";
    /**
     * 香港，澳门，台湾 地址code列表
     */
    public static final List<String> HK_MO_TW_CODE_LIST = Arrays.asList("810000", "820000", "710000");
    /**
     * a-z 字符
     */
    public static final String ALPHABETIC_A_TO_Z = "abcdefghijklmnopqrstuvwxyz";
    /**
     * 打款提示模版
     */
    public static final String SETTLE_STAT_MSG_TEMPLATE = "【来逛呗】{} 批量出库脚本任务执行完成\n" +
            "> - 备注：{}\n" +
            "> - 结算日期：{}\n" +
            "> - 未打款数量：{}\n" +
            "> - 打款中数量：{}\n" +
            "> - 打款失败数量：{}\n" +
            "> - 打款成功数量：{}\n" +
            "> - 未开户数量：{}";
    /**
     * 外部入金提示模版
     */
    public static final String SETTLE_INCOME_MSG_TEMPLATE = "【来逛呗】钱包入金通知\n" +
            "> - 钱包备注：{}\n" +
            "> - 平台：{}\n" +
            "> - 金额：{}\n" +
            "> - 日期：{}\n" +
            "> - 打款备注：{}\n" +
            "> - 外部流水号：{}";
    /**
     * 外部入金提示模版
     */
    public static final String RECONCILIATION_SUCCESS_MSG_TEMPLATE = "【来逛呗】结算单对账完成\n" +
            "> - 对账类型：{}\n" +
            "> - 平台类型：{}\n" +
            "> - 集团备注：{}\n" +
            "> - 总结算金额：{}\n" +
            "> - 普通券结算金额：{}\n" +
            "> - 权益卡冻结金额：{}\n" +
            "> - 权益卡结算金额：{}\n\n" +
            "对账无差异，系统自动完成出款确认";
    /**
     * 外部入金提示模版
     */
    public static final String RECONCILIATION_WAIT_TRANSFER_ERROR_MSG_TEMPLATE = "【来逛呗】结算单转账处理失败\n" +
            "> - 对账类型：{}\n" +
            "> - 平台类型：{}\n" +
            "> - 集团备注：{}\n" +
            "> - 总结算金额：{}\n" +
            "> - 普通券结算金额：{}\n" +
            "> - 权益卡冻结金额：{}\n" +
            "> - 权益卡结算金额：{}\n\n" +
            "**失败原因【{}】，请尽快介入排查🔥🔥🔥**";
    /**
     * 外部入金提示模版
     */
    public static final String RECONCILIATION_WAIT_TRANSFER_MSG_TEMPLATE = "【来逛呗】结算单对账完成\n" +
            "> - 对账类型：{}\n" +
            "> - 平台类型：{}\n" +
            "> - 集团备注：{}\n" +
            "> - 总结算金额：{}\n" +
            "> - 普通券结算金额：{}\n" +
            "> - 权益卡冻结金额：{}\n" +
            "> - 权益卡结算金额：{}\n\n" +
            "对账无差异，等待转账完成后自动完成出款确认";
    /**
     * 外部入金提示模版
     */
    public static final String RECONCILIATION_INCOME_NOT_MATCH_MSG_TEMPLATE = "【来逛呗】入金对账异常\n" +
            "> - 对账类型：{}\n" +
            "> - 平台类型：{}\n" +
            "> - 集团备注：{}\n" +
            "> - 外部入金金额：{}\n" +
            "> - 普通券结算金额：{}\n" +
            "> - 权益卡冻结金额：{}\n" +
            "> - 佣金差异金额：{}\n\n" +
            "> - 掉单差异金额：{}\n\n" +
            "> - 外部商品金额：{}\n\n" +
            "> - 正向差异金额：{}={}(普)+{}(母)\n\n" +
            "> - 逆向差异金额：{}={}(普)+{}(母)\n\n" +
            "**入金数据与结算数据不一致，差异金额{}，请尽快介入排查🔥🔥🔥**";
    /**
     * 外部入金提示模版
     */
    public static final String RECONCILIATION_PRIVILEGE_NOT_MATCH_MSG_TEMPLATE = "【来逛呗】商圈权益卡对账异常\n" +
            "> - 对账类型：{}\n" +
            "> - 平台类型：{}\n" +
            "> - 异常结算单号：{}\n\n" +
            "**权益卡待结算金额不足或余额账户不存在，请尽快介入排查🔥🔥🔥**";
    /**
     * 转款余额不足提示模版
     */
    public static final String TRANSFER_BALANCE_NOT_ENOUGH_MSG_TEMPLATE = "【来逛呗】商户结算单处理失败，转账余额不足，商户结算单号：{}";
    /**
     * 提现余额不足提示模版
     */
    public static final String WITHDRAW_BALANCE_NOT_ENOUGH_MSG_TEMPLATE = "【来逛呗】商户结算单处理失败，提现余额不足，商户结算单号：{}";
    /**
     * 对账提示
     */
    public static final String RECONCILIATION_DIFF_TITLE = "【来逛呗】{}日{}对账完成\n";
    /**
     * 核销来源：来逛呗
     */
     public static final String QYK = "QYK";
    public static final String AL = "AL";
    /**
     * 测试环境的webHook
     */
    public static final String TEST_WEB_HOOK = "https://oapi.dingtalk.com/robot/send?access_token=7e11a1af6639e2ba2cc88c97f1941124093811d05d506e2086cdcfb4d279c039";
    /**
     * 广场标识
     */
    public static final String SQUARE_SIGN = "广场";

    /**
     * 默认的HttpClient
     */
    public static final String GENERIC_HTTP_CLIENT = "generic-http-client";

    /**
     * 位置
     */
    public static final String LOCATION = "location";
    /**
     * 回环地址
     */
    public static String LOOP_BACK_IP = "127.0.0.1";
    /**
     * 对账差异明细项
     */
    public static String RECONCILIATION_DIFF_ITEM = "\n {}-----:{} \n ";

    /**
     * 开发环境
     */
    public static final String DEV = "dev";

    /**
     * 30天秒数
     */
    public static final Long SECONDS_30_DAYS = TimeUnit.DAYS.toSeconds(30);

    /**
     * 7天秒数
     */
    public static final Long SECONDS_7_DAYS = TimeUnit.DAYS.toSeconds(7);

    /**
     * 服务商林客一键登录权限
     */
    public static final String GRANT_LIFE_PARTNER_LINK_CONNECT = "lifePartnerLinkConnect";

    static {
        SORT_FIELD_MAP.put(RELEASE_TIME, "release_time");
        SORT_FIELD_MAP.put(PLAY_COUNT, "play_count");
        SORT_FIELD_MAP.put(LIKE_COUNT, "like_count");
        SORT_FIELD_MAP.put(COMMENT_COUNT, "comment_count");
        SORT_FIELD_MAP.put(SHARE_COUNT, "share_count");
        SORT_FIELD_MAP.put(ORDER_COUNT, "order_count");
    }
}