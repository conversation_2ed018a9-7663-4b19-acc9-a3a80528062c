package com.huike.nova.common.enums;

import cn.hutool.core.util.StrUtil;
import com.huike.nova.common.exception.ReconciliationException;
import org.apache.commons.lang3.StringUtils;

/**
 * 总督对账错误码
 *
 * <AUTHOR>
 * @version LakalaTradeErrorEnum.java, v 0.1 2020-09-03 18:46 liluqing
 */
public enum ReconciliationApiErrorEnum {

    BILL_NOT_EXIST_ERROR("810300", "对账单未生成"),
    COMMON_ERROR("810400", "联动接口请求通用异常"),
    UM_SDK_RESPONSE_NULL_ERROR("810401", "联动SDK请求返回为空"),
    UM_SDK_RESPONSE_ERROR("810402", "联动SDK请求异常"),
    BIZ_RESPONSE_ERROR("810403", "联动接口业务异常"),
    CALL_EXTERNAL_ERROR("810404", "调用外部api接口异常"),
    UNKNOW_ERROR("810404", "未知异常"),
    BILL_DOWNLOAD_ERROR("810405", "对账单下载异常"),
    PLATFORM_BILL_NOT_EXIST_ERROR("810406", "平台方对账单未生成"),
    BUCKET_LOAD_ERROR("810407", "bucket加载异常"),
    DATA_LOAD_ERROR("810101", "数据加载器异常"),
    DIFF_COMPARE_ERROR("810102", "差异对比异常"),
    INIT_ERROR("810000", "项目初始化异常"),
    SUPPLY_ORDER_ERROR("810201", "补单接口业务异常"),
    REFUND_ORDER_REVISION_ERROR("810202", "退款失败订正接口业务异常"),
    ;

    private String code;
    private String msg;

    ReconciliationApiErrorEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static ReconciliationApiErrorEnum getByCode(String code) {
        ReconciliationApiErrorEnum[] valueList = ReconciliationApiErrorEnum.values();
        for (ReconciliationApiErrorEnum v : valueList) {
            if (StringUtils.equalsIgnoreCase(v.getCode(), code)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>code</tt>.
     *
     * @return property value of code
     */
    public String getCode() {
        return code;
    }

    /**
     * Getter method for property <tt>msg</tt>.
     *
     * @return property value of msg
     */
    public String getMsg() {
        return msg;
    }

    /**
     * 构造一个交易异常
     *
     * @return
     */
    public ReconciliationException newException() {
        return new ReconciliationException(this);
    }

    /**
     * 构造一个交易异常并重写消息描述
     *
     * @return
     */
    public ReconciliationException newException(String msg, Object... arg) {

        return new ReconciliationException(this.code, StrUtil.format(msg, arg));
    }

    /**
     * 构造一个交易异常并重写消息描述
     *
     * @return
     */
    public ReconciliationException newBizException(String bizCode, String msg, Object... arg) {
        return new ReconciliationException(this.code, StrUtil.format(msg, arg), bizCode);
    }

}
