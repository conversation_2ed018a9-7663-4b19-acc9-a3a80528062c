package com.huike.nova.common.config;

import com.huike.nova.common.enums.EnvEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (<EMAIL>)
 * @version SysConfig.java, v1.0 06/04/2024 09:55 John Exp$
 */
@Component
@Data
@RefreshScope
public class SysConfig {

    /**
     * env
     */
    @Value("${knowledge.env}")
    private String env;

    /**
     * 接口加签开关
     */
    @Value("${knowledge.sign-switch:true}")
    private Boolean signSwitch;

    /**
     * 导出 ip 白名单
     */
    @Value("${export-ip-white-list}")
    private String exportIpWhiteList;

    /**
     * 司南host
     */
    @Value("${knowledge.sinan-host:https://crmgw-test.51youdian.com}")
    private String sinanHost;

    /**
     * 推荐问题
     */
    @Value("${knowledge.recommend-question:问题1,问题2,问题3,问题4}")
    private String recommendQuestion;


    /**
     * 是否是正式环境
     *
     * @return true-是 false-否
     */
    public boolean isProd() {
        return StringUtils.equalsIgnoreCase(EnvEnum.PROD.getValue(), env);
    }

    /**
     * 是否是开发环境
     *
     * @return true-是 false-否
     */
    public boolean isDev() {
        return StringUtils.equalsIgnoreCase(EnvEnum.DEV.getValue(), env);
    }

    /**
     * 是否是测试环境
     *
     * @return true-是 false-否
     */
    public boolean isTest() {
        return StringUtils.equalsIgnoreCase(EnvEnum.TEST.getValue(), env);
    }

    public boolean isDevOrTest() {
        return isDev() || isTest();
    }

    /**
     * 钉钉默认的通知Token
     */
    @Value("${dingtalk.default.token:}")
    private String dingTalkDefaultToken;

    /**
     * 钉钉默认的通知Secret
     */
    @Value("${dingtalk.default.secret:}")
    private String dingTalkDefaultSecret;

    /**
     * 语雀文档搜索天数
     */
    @Value("${dingtalk.article-data-search-day:-2}")
    private Integer dingtalkArticleDataSearchDay;

}

