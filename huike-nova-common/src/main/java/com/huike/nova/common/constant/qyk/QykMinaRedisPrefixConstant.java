/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.common.constant.qyk;

/**
 * <AUTHOR>
 * @version QykMinaRedisPrefixConstant.java, v 0.1 2024-03-19 5:07 PM ruanzy
 */
public class QykMinaRedisPrefixConstant {

    /**
     * 支付宝小程序绑定激活商圈卡缓存key
     */
    public static final String QYK_MINA_CACHE_KEY_ACTIVATE_CARD = "qyk.mina.cache.key.activate.card.{}";


    /**
     * 抖音小程序退款更新权益卡缓存key
     */
    public static final String QYK_MINA_CACHE_KEY_UPDATE_REFUND_QYK_CARD = "qyk.mina.cache.key.update.refund.qyk.card.{}";

    /**
     * 短信发送缓存key（用于验证）
     * <p>
     * 第一个替换成小程序id
     * 第二个替换成场景类型
     * 第三个替换成手机号
     */
    public static final String QYK_MINA_CACHE_KEY_SMS_VERIFY = "qyk.mina.cache.sms.verify.{}.{}.{}";

    /**
     * 短信发送次数缓存key（用于风控）
     * <p>
     * 第一个替换成oem配置id
     * 第二个替换成场景类型
     * 第三个替换成手机号
     */
    public static final String QYK_MINA_CACHE_KEY_SMS_REQUEST_COUNT = "qyk.mina.cache.sms.request.count.{}.{}.{}";

    /**
     * 短信发送缓存key(用于风控)
     * <p>
     * 第一个替换成oem配置id
     * 第二个替换成场景类型
     * 第三个替换成手机号
     */
    public static final String QYK_MINA_CACHE_KEY_SMS_REQUEST = "qyk.mina.cache.sms.request.{}.{}.{}";

    /**
     * 短信发送次数缓存key（用于风控）
     * <p>
     * 第一个替换成订单号
     */
    public static final String QYK_CACHE_KEY_SMS_ORDER_REQUEST_COUNT = "qyk.cache.sms.order.request.count.{}";

    /**
     * 商家券库存锁
     * <p>
     * 商家券-商品id
     */
    public static final String QYK_MERCHANT_CARD_STOCK_KEY = "qyk.merchant.card.stock.key.{}";

    /**
     * 商家券核销锁
     * <p>
     * order_id
     */
    public static final String QYK_MERCHANT_CARD_VERIFY_KEY = "qyk.merchant.card.verify.key.{}";

}