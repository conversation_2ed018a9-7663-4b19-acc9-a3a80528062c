package com.huike.nova.common.util;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.excel.ExcelWidthStyleStrategy;
import com.huike.nova.common.exception.CommonException;
import lombok.Getter;
import lombok.SneakyThrows;
import org.assertj.core.util.Lists;

import java.io.File;
import java.util.Collection;
import java.util.Objects;

/**
 * Excel写入帮助类
 *
 * <AUTHOR> (<EMAIL>)
 * @version ExcelWriterHelper.java, v1.0 12/13/2023 19:36 John Exp$
 */
@Getter
public class ExcelWriterHelper<T> implements AutoCloseable {

    @SuppressWarnings("resource")
    public static <T> ExcelWriterHelper<T> create(String fileName, Class<T> clazz) {
        return new ExcelWriterHelper<T>().init(fileName, clazz, null);
    }


    private ExcelWriterHelper() {
    }

    /**
     * Excel对象
     */
    private ExcelWriter excelWriter;

    /**
     * 写入的Sheet
     */
    private WriteSheet writeSheet;

    /**
     * Excel文件
     */
    private File excelFile;
    
    /**
     * 写入的文件数量
     */
    private int writeLines = 0;

    @SneakyThrows
    public ExcelWriterHelper<T> init(String sheetName, Class<T> clazz, Collection<String> excludeColumnFiledNames) {
        // 创建一个临时文件
        excelFile = File.createTempFile("nova-tmp-", ".xlsx", new File(CommonConstant.TEMP_DIR));
        this.excelWriter = EasyExcel.write(excelFile).build();
        // 这里 需要指定写用哪个class去写
        this.writeSheet = EasyExcel.writerSheet(sheetName)
                .registerWriteHandler(ExportUtil.processStyle())
                .registerWriteHandler(new ExcelWidthStyleStrategy())
                .excludeColumnFieldNames(excludeColumnFiledNames)
                .head(clazz)
                .build();
        return this;
    }

    private void checkInit() {
        if (Objects.isNull(excelWriter) || Objects.isNull(writeSheet)) {
            throw new CommonException(ErrorCodeEnum.PARAM_ERROR).detailMessage("请调用init()后再调用write()方法");
        }
    }

    public synchronized void write(Collection<T> data) {
        checkInit();
        if (CollectionUtil.isNotEmpty(data)) {
            excelWriter.write(data, writeSheet);
            writeLines += data.size();
        }
    }

    public synchronized void write(T data) {
        checkInit();
        if (Objects.nonNull(data)) {
            excelWriter.write(Lists.newArrayList(data), writeSheet);
            writeLines += CommonConstant.ONE;
        }
    }

    public synchronized File finish() {
        // 没有数据导出
        if (writeLines == 0) {
            throw new CommonException(ErrorCodeEnum.EXPORT_FILE_IS_NO_CONTENT);
        }

        try {
            excelWriter.finish();
            excelWriter.close();
            excelWriter = null;
            return excelFile;
        } catch (Exception ex) {
            return null;
        }
    }


    @SuppressWarnings("ResultOfMethodCallIgnored")
    @Override
    public void close() throws Exception {
        if (Objects.nonNull(excelWriter)) {
            finish();
        }
        if (Objects.nonNull(excelFile) && excelFile.exists()) {
            excelFile.delete();
        }
    }
}
