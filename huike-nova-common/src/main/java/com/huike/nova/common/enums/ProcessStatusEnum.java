/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.enums;

/**
 * <AUTHOR>
 * @version AccountStatusEnum.java, v 0.1 2022-11-28 2:47 PM ruanzy
 */
public enum ProcessStatusEnum {
    /**
     * 初始化
     */
    INIT("初始化", 0),
    /**
     * 下载完成
     */
    DOWNLOAD_COMPLETE("下载完成", 1),
    /**
     * 向量完成
     */
    COMPLETE("向量完成", 2),
    ;

    private String name;
    private Integer value;

    ProcessStatusEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static ProcessStatusEnum getByValue(Integer value) {
        ProcessStatusEnum[] valueList = ProcessStatusEnum.values();
        for (ProcessStatusEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public Integer getValue() {
        return value;
    }
}