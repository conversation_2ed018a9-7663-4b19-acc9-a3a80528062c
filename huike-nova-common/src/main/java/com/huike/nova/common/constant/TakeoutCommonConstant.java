/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version TakeoutCommonConstant.java, v 0.1 2023-02-15 2:39 PM ruanzy
 */
public class TakeoutCommonConstant {

    /**
     * 抖音团购配送回调-消息ID
     */
    public final static String MSG_ID = "Msg-Id";

    /**
     * 抖音团购配送回调-签名
     */
    public final static String SIGNATURE = "X-Douyin-Signature";

    /**
     * 用户申请退款消息-签名
     */
    public final static String LIFE_SIGN = "x-life-sign";

    /**
     * 通用的骑手名称
     */
    public final static String RIDER_NAME = "骑手";

    /**
     * 抖音外卖SPI验签-client_key
     */
    public final static String CLIENT_KEY = "client_key";

    /**
     * 抖音外卖SPI验签-timestamp
     */
    public final static String TIMESTAMP = "timestamp";
}