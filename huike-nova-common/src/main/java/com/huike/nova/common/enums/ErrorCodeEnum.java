/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.enums;

import cn.hutool.core.util.StrUtil;
import com.huike.nova.common.constant.StringPool;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version ErrorCodeEnum.java, v 0.1 2022-08-31 3:32 PM ruanzy
 */
@Getter
public enum ErrorCodeEnum {

    /**
     * 成功响应
     */
    SUCCESS("00000", "成功"),

    /**
     * 抖音接口异常
     */
    TIKTOK_OPEN_ERROR("A0001", "抖音接口信息获取失败"),

    /**
     * 活动信息获取异常
     */
    ACTIVITY_QUERY_ERROR("B0001", "未查到活动信息"),

    /**
     * 参数不符合规则
     */
    ACTIVITY_PARAM_ERROR("B0002", "参数不符合规则"),

    /**
     * 话题数据获取异常
     */
    TIKTOK_TOPIC_ERROR("B0003", "话题数据获取异常"),

    /**
     * 热词数据获取异常
     */
    TIKTOK_HOT_WORDS_ERROR("B0004", "热词数据获取异常"),

    /**
     * 活动素材信息获取异常
     */
    SCENES_QUERY_ERROR("C0001", "活动素材信息获取异常"),

    /**
     * 服务端错误（一级宏观错误码）
     */
    SERVER_ERROR("D0001", "服务端错误"),

    /**
     * 登录异常
     */
    LOGIN_ERROR("E0001", "账号或密码不正确"),

    /**
     * Client初始化失败
     */
    ICE_INIT_CLIENT_ERROR("E0002", "Client初始化失败"),

    /**
     * 素材上传失败
     */
    ICE_MATERIAL_UPLOAD_ERROR("E0003", "素材上传失败"),

    /**
     * 素材合成失败
     */
    ICE_MATERIAL_SYNTHETIC_ERROR("E0004", "素材合成失败"),

    /**
     * 素材的场景太少
     */
    ICE_SCENE_MATERIAL_LESS_ERROR("E0005", "请上传多场景的素材"),

    /**
     * 合成视频信息查询异常
     */
    VIDEO_STATUS_ERROR("E0006", "合成视频信息查询异常"),

    /**
     * 获取Schema异常
     */
    CREATE_SCHEMA_ERROR("E0007", "获取Schema异常"),

    /**
     * 登录失效
     */
    LOGIN_INVALID("F0001", "登录失效"),

    /**
     * 接口校验失败
     */
    INTERFACE_INVALID("F0002", "接口校验失败"),

    /**
     * 参数错误(二级宏观错误码)
     */
    PARAM_ERROR("F0003", "参数错误"),
    /**
     * 素材不足
     */
    INSUFFICIENT_MATERIAL("F0004", "素材不足"),
    /**
     * 数据统计失败
     */
    COUNT_ERROR("G0001", "数据统计失败"),

    /**
     * 场景类目获取错误
     */
    SCENE_CATEGORY_ERROR("G0002", "场景类目获取错误"),

    /**
     * 商户视频权限没有配置,请联系客服配置
     */
    NOT_MERCHANT_VIDEO_CONFIG("H0001", "商户视频权限没有配置,请联系客服配置"),

    /**
     * 短信发送失败
     */
    SMS_SEND_ERROR("I0001", "短信发送失败"),

    /**
     * 小程序用户信息异常
     */
    CUSTOMER_ERROR("I0002", "用户信息异常"),

    /**
     * 订单信息异常
     */
    ORDER_ERROR("I0003", "订单信息异常"),

    /**
     * 手机号格式不正确
     */
    CHECK_PHONE_ERROR("J0001", "手机号格式不正确"),

    /**
     * 员工信息查询异常
     */
    EMPLOYEE_DETAIL_ERROR("J0002", "员工信息查询异常"),

    /**
     * 门店信息查询错误
     */
    STORE_ERROR("J0003", "门店信息查询错误"),

    /**
     * 商户信息查询错误
     */
    MERCHANT_ERROR("J0004", "商户信息查询错误"),

    /**
     * 查询视频进度错误
     */
    QUERY_RATE_ERROR("K0001", "视频合成进度查询错误"),

    /**
     * 外部错误（一级宏观错误码）
     */
    EXTERNAL_ERROR("L0001", "调用第三方服务出错"),

    /**
     * 博实结播报失败
     */
    BSJ_BROADCAST_ERROR("M0001", "博实结播报失败"),

    /**
     * 博实结打印失败
     */
    BSJ_PRINT_ERROR("M0002", "博实结打印失败"),
    /**
     * 获取分布式锁失败
     */
    GET_LOCK_ERROR("N0001", "获取分布式锁失败"),
    /**
     * 闭环撤销核销异常
     */
    CLOSE_CANCEL_VERIFY_ERROR("N0002", "闭环撤销核销异常"),
    /**
     * 闭环撤销核销,超过可撤销时限异常
     */
    CLOSE_CANCEL_VERIFY_TIMEOUT_ERROR("N0003", "撤销核销超时"),
    /**
     * 生成UrlTicket错误：重复Ticket
     */
    GENERATE_URL_TICKET_DUPLICATE_ERROR("O0001", "生成UrlTicket重复"),
    /**
     * 跳转链接不存在
     */
    TICKET_NOT_EXISTED("O0002", "跳转的链接不存在"),
    /**
     * 缺少必须得权限
     */
    MISSING_REQUIRED_PERMISSION("O0003", "缺少必要权限"),
    /**
     * APP通道错误
     */
    APP_CHANNEL_ERROR("Z001", "APP通道错误"),
    /**
     * 导出的文件没有内容
     */
    EXPORT_FILE_IS_NO_CONTENT("Z0002", "导出文件为空"),
    /**
     * 自定义错误
     */
    CUSTOM_NOTIFY_ERROR("ZZZZZ", StringPool.EMPTY),
    /**
     * 视频号验券错误
     */
    WECHAT_SHOP_VOUCHER_PREPARE_DELIVERY_ERROR("EC0001", "暂无可用的优惠券"),
    /**
     * 视频号核销失败
     */
    WECHAT_SHOP_VOUCHER_VERIFY_ERROR("EC0002", "核券错误"),
    /**
     * 视频号回调错误
     */
    WECHAT_SHOP_CALLBACK_ERROR("EC0003", "视频号回调错误"),

    /**
     * 重试失败
     */
    RETRYER_FAILED_ERROR("R0001", "重试器执行失败"),

    /**
     * Cookie信息错误
     */
    COOKIE_ERROR("D0001", "Cookie信息错误"),

    /**
     * 权益卡未到核销时间
     */
    QYK_NOT_REACH_TIME("QYK0001", "该券号开始激活时间：{}为让您及时享受该优惠，我们会在第一激活时间通知您"),

    /**
     * 美团点评业务错误
     */
    DIAN_PING_BIZ_ERROR("DP0001", "美团点评业务错误"),

    /**
     * 视频号核销失败
     */
    COUPON_VERIFY_ERROR("A0002", "核券错误"),

    /**
     * 美团点评接口错误
     */
    DIAN_PING_API_ERROR("DP0002", "美团点评接口错误"),

    CALL_LIMIT("N0004", "请勿重复调用")
    ,

    /**
     * 城市商圈卡审核商品信息错误
     */
    CITY_CIRCLE_CARD_COMMODITY_ERROR("CC0001", "城市商圈卡商品审核信息错误"),

    /**
     * 来团呗接口错误
     */
    NOVA_API_ERROR("N0005", "nova-api错误")
    ;

    /**
     * 响应码
     */
    private final String code;
    /**
     * 响应信息
     */
    private final String message;

    ErrorCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static ErrorCodeEnum getByCode(String code) {
        ErrorCodeEnum[] valueList = ErrorCodeEnum.values();
        for (ErrorCodeEnum v : valueList) {
            if (StrUtil.equalsIgnoreCase(v.getCode(), code)) {
                return v;
            }
        }
        return null;
    }
}