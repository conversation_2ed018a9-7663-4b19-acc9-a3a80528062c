package com.huike.nova.common.util;

import cn.hutool.core.util.StrUtil;
 import com.annimon.stream.function.Supplier;
import com.huike.nova.common.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version IpUtil.java, v 0.1 2019-05-31 10:52
 */
@Slf4j
public class IpUtil {

    private static final Pattern p = Pattern.compile("\\<dd class\\=\"fz24\">(.*?)\\<\\/dd>");

    /**
     * 真是请求IP
     *
     * @param request
     * @return
     */
    public static String getRealRequestIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (null != ip && ip.contains(",")) {
            ip = ip.substring(0, ip.indexOf(","));
        }
        return ip;
    }


    /**
     * 获得本地IP
     *
     * @param request
     * @return
     */
    public static String getIpAddr(HttpServletRequest request) {
        try {
            // 检查squid 或者 nginx的代理头
            String ip = request.getHeader(CommonConstant.X_FORWARDED_FOR);
            if (!preCheckIpValid(ip)) {
                // 检查apache代理头
                ip = request.getHeader(CommonConstant.PROXY_CLIENT_IP);
            }
            if (!preCheckIpValid(ip)) {
                // 检查apache webLogic代理通
                ip = request.getHeader(CommonConstant.WL_PROXY_CLIENT_IP);
            }
            if (!preCheckIpValid(ip)) {
                // 直接获得地址
                ip = request.getRemoteAddr();
                if (StringUtils.equals(CommonConstant.LOOP_BACK_IP, ip)) {
                    // 根据网卡取本机配置的IP
                    ip = Supplier.Util.safe(() -> InetAddress.getLocalHost().getHostAddress(), StrUtil.EMPTY).get();
                }
            }
            // 通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
            if (StringUtils.isNotBlank(ip)) {
                if (ip.contains(StrUtil.COMMA)) {
                    return StringUtils.split(ip, StrUtil.COMMA)[0];
                } else {
                    return ip;
                }
            }
            return ip;
        } catch (Exception e) {
            LogUtil.error(log, "getIpAddr >> 获取ip地址异常 >> msg={}", e);
            return StringUtils.EMPTY;
        }
    }

    /**
     * 检查Ip是否可用
     *
     * @return true可用，false不可用
     */
    private static boolean preCheckIpValid(CharSequence ip) {
        // 将空字符串或者IP转为UNKNOWN
        return !StringUtils.equalsIgnoreCase(
                StringUtils.defaultIfBlank(ip, CommonConstant.UNKNOWN),
                CommonConstant.UNKNOWN
        );
    }

    /**
     * 获取外网IP
     *
     * @return
     */
    public static String getV4IpAddr() {
        String ip = "";
        String chinaz = "http://ip.chinaz.com/";

        String inputLine = "";
        String read = "";
        try {
            URL url = new URL(chinaz);
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            BufferedReader in = new BufferedReader(new InputStreamReader(urlConnection.getInputStream()));
            while ((read = in.readLine()) != null) {
                inputLine += read;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        Matcher m = p.matcher(inputLine);
        if (m.find()) {
            ip = m.group(1);
        }
        return ip;
    }
}
