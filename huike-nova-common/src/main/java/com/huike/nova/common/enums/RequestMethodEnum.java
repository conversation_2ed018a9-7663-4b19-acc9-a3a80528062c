package com.huike.nova.common.enums;

/**
 * mq枚举类
 *
 * <AUTHOR>
 * @version ProduceEnum.java, v 0.1 2022/3/10 4:05 下午 mayucong
 */
public enum RequestMethodEnum {
    GET("GET请求", "GET"),
    POST("POST请求", "POST"),
    ;

    private String name;
    private String value;

    RequestMethodEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public static RequestMethodEnum getByValue(String value) {
        RequestMethodEnum[] valueList = RequestMethodEnum.values();
        for (RequestMethodEnum v : valueList) {
            if (v.getValue().equals(value)) {
                return v;
            }
        }
        return null;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public String getValue() {
        return value;
    }
}