/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.huike.nova.common.util;

import com.alibaba.nacos.api.utils.StringUtils;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version RegexUtil.java, v 0.1 2024-07-23 10:45 AM ruanzy
 */
public class RegexUtil {

    /**
     * 是否符合正则
     *
     * @param pattern 正则
     * @param input   输入内容
     * @return true:是 false:否
     */
    public static boolean match(Pattern pattern, String input) {
        if (StringUtils.isBlank(input)) {
            return Boolean.FALSE;
        }
        return pattern.matcher(input).matches();
    }
}