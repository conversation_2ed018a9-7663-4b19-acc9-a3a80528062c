package com.huike.nova.common.constant;

/**
 * 微信小程序
 *
 * <AUTHOR> (<EMAIL>)
 * @version WechatMinaConstants.java, v1.0 12/08/2023 15:32 John Exp$
 */
public class WechatMinaConstants {

    public static final String DEFAULT_IDENTIFIER_CLIENT = "LAI_TUAN_BEI-WECHAT-MINA-CLIENT";

    /**
     * 错误码：0 - 成功
     */
    public static final int ERROR_CODE_SUCCESS = 0;

    /**
     * 获取Token
     */
    public static final String API_OBTAIN_TOKEN = "https://api.weixin.qq.com/cgi-bin/token";

    /**
     * 小程序登录
     */
    public static final String API_MINA_CODE_TO_SESSION = "https://api.weixin.qq.com/sns/jscode2session";

    /**
     * 获得用户手机号
     */
    public static final String API_WXA_GET_USER_PHONE_NUMBER = "https://api.weixin.qq.com/wxa/business/getuserphonenumber";

    /**
     * AccessToken
     */
    public static final String ACCESS_TOKEN = "access_token";

    /**
     * 支付通道：付呗
     */
    public static final String PAY_CHANNEL_FUBEI = "FUBEI";

    /**
     * AccessToken错误
     */
    public static final Integer ACCESS_TOKEN_IS_INVALID = 40001;
}
