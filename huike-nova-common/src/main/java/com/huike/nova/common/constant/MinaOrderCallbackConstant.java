/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.constant;

/**
 * <AUTHOR>
 * @version MinaOrderCallbackConstant.java, v 0.1 2022-11-02 10:28 AM ruanzy
 */
public class MinaOrderCallbackConstant {

    /**
     * 签名时间戳
     */
    public final static String BYTE_TIMESTAMP = "byte-timestamp";
    /**
     * 签名随机字符串
     */
    public final static String BYTE_NONCE_STR = "byte-nonce-str";
    /**
     * 签名
     */
    public final static String BYTE_SIGNATURE = "byte-signature";
    /**
     * 签名日志
     */
    public final static String BYTE_LOGID = "byte-logid";
    /**
     * 订单详情页地址
     */
    public final static String ORDER_ENTRY_SCHEMA_PATH = "pages/order/orderDetails/index";
    /**
     * 订单号
     */
    public final static String ORDER_SN = "orderSn";
    /**
     * 商品ID
     */
    public final static String GOODS_ID = "goodsId";
}