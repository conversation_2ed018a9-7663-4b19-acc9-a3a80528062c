package com.huike.nova.common.util;

import cn.hutool.core.util.StrUtil;
import com.huike.nova.common.constant.CommonConstant;
import com.huike.nova.common.enums.ErrorCodeEnum;
import com.huike.nova.common.exception.CommonException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import javax.annotation.Nonnull;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * Redis锁帮助类
 *
 * <AUTHOR> (<EMAIL>)
 * @version RedisLockHelper.java, v1.0 07/02/2023 18:19 John Exp$
 */
@Slf4j
public final class RedisLockHelper implements AutoCloseable {

    /**
     * 获得RedisLock
     */
    private final RLock lock;

    /**
     * 当前调用的来源
     */
    private final Supplier<String> callSource;

    /**
     * 锁的Key值
     */
    private final String redisKeyName;

    /**
     * 快速创建
     *
     * @param redissonClient redis客户端
     * @param redisKeyTemplate redisKey模板
     * @param args 组合参数
     * @return 锁对象
     */
    public static RedisLockHelper create(RedissonClient redissonClient, String redisKeyTemplate, Object... args) {
        String key = StrUtil.format(redisKeyTemplate, args);
        return new RedisLockHelper(key, redissonClient);
    }


    public RedisLockHelper(@Nonnull String redisKey, RedissonClient redissonClient) {
        this.lock = redissonClient.getLock(redisKey);
        this.redisKeyName = redisKey;
        val ste = Thread.currentThread().getStackTrace()[2];
        // 获得当前调用的类
        callSource = () -> StrUtil.format("{}.{}", ste.getClassName(), ste.getMethodName());
    }

    /**
     * 尝试锁定
     *
     * @param waitTime 等待时间
     * @param leaseTime 锁有效时间
     * @param unit 时间单位 小时、分、秒、毫秒等
     */
    @SneakyThrows
    public void tryLock(long waitTime, long leaseTime, TimeUnit unit) {
        if (!lock.tryLock(waitTime, leaseTime, unit)) {
            LogUtil.warn(log, "RedisLockHelper.tryLock, {}锁获取失败.. RedisKey: {}", callSource.get(), redisKeyName);
            throw new CommonException(ErrorCodeEnum.SERVER_ERROR).detailMessage("操作频繁，请稍后再试");
        }
    }

    /**
     * 尝试锁定：默认参数
     */
    @SneakyThrows
    public void tryLock() {
        tryLock(CommonConstant.ONE_SECOND, CommonConstant.TEN_SECOND, TimeUnit.MINUTES);
    }

    /**
     * 尝试锁定：最大10秒
     *
     * @param seconds 秒
     */
    @SneakyThrows
    public void tryLockSec(int seconds) {
        tryLock(seconds, Math.max(CommonConstant.TEN_SECOND, seconds), TimeUnit.SECONDS);
    }

    @Override
    public void close() throws Exception {
        try {
            if (null != lock && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        } catch (Exception e) {
            LogUtil.warn(log, "{} >>>>> balancePayment..", callSource.get(), e);
        }
    }
}
