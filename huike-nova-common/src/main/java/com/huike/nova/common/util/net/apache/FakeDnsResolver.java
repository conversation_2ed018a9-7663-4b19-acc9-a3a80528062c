package com.huike.nova.common.util.net.apache;

import org.apache.http.conn.DnsResolver;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 假的DNS解析类
 *
 * <AUTHOR> (<EMAIL>)
 * @version FakeDnsResolver.java, v1.0 08/25/2023 10:52 John Exp$
 */
public class FakeDnsResolver implements DnsResolver {
    @Override
    public InetAddress[] resolve(String host) throws UnknownHostException {
        // Return some fake DNS record for every request, we won't be using it
        return new InetAddress[] { InetAddress.getByAddress(new byte[] { 1, 1, 1, 1 }) };
    }
}
