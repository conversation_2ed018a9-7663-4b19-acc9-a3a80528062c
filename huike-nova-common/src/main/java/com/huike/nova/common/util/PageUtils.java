package com.huike.nova.common.util;

/**
 * <AUTHOR>
 * @version 1.0 PageUtiles
 * @date 2022/6/1 12:20
 */
public class PageUtils {

    /**获取总页数
     * @param count
     * @param pageSize
     * @return
     */
    public static Integer getTotalPage(Integer count, Integer pageSize) {
        if (count % pageSize == 0) {
            return count / pageSize;
        } else {
            return count / pageSize + 1;
        }
    }
}