/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved
 */
package com.huike.nova.common.enums.acct;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version GenerateIdPrefixEnum.java, v 0.1 2024-05-31 5:20 下午 liubo
 */
public enum GenerateIdPrefixEnum {
    WITHDRAW_NO("提现单号", "LW"),
    TRANSFER_NO("转账单号", "LT"),
    SETTLE_PAYMENT_NO("入金单号", "LP"),
    SUP_ORDER_TASK("补单单号", "SUP"),
    ;

    private String name;
    private String value;

    GenerateIdPrefixEnum(String name, String value) {
        this.name = name;
        this.value = value;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */
    public String getName() {
        return name;
    }

    /**
     * Getter method for property <tt>value</tt>.
     *
     * @return property value of value
     */
    public String getValue() {
        return value;
    }

    public static GenerateIdPrefixEnum getByValue(String value) {
        GenerateIdPrefixEnum[] valueList = GenerateIdPrefixEnum.values();
        for (GenerateIdPrefixEnum v : valueList) {
            if (StringUtils.equalsIgnoreCase(v.getValue(), value)) {
                return v;
            }
        }
        return null;
    }
}