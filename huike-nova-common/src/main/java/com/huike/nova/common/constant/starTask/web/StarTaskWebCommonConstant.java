package com.huike.nova.common.constant.starTask.web;

/**
 * <AUTHOR>
 * @date 2023年12月07日 11:24
 */
public class StarTaskWebCommonConstant {

    /**
     * BalanceLogChangeRemark充值
     */
    public static final String BALANCE_LOG_CHANGE_REMARK_RECHARGE = "后台充值";

    /**
     * 审批单号str
     */
    public static final String APPROVAL_NUMBER_STR = "审批单号 ";


    /**
     * 网络异常线程休眠时间（秒）
     */
    public static final Integer NETWORK_EXCEPTION_SLEEP_TIME = 3;

    /**
     * deleteBucket 最大重试次数
     */
    public static final Integer DELETE_BUCKET_MAX_RETRY_COUNT = 3;

    /**
     * deleteBucket 初始重试次数
     */
    public static final Integer DELETE_BUCKET_INIT_RETRY_COUNT = 0;

    /**
     * 导出中心:未找到指定的业务场景
     */
    public static final String EXPORT_ERROR_BUSINESS_NOT_FOUND = "未找到指定的业务场景";

    /**
     * 导出中心:未找到指定的参数配置
     */
    public static final String EXPORT_ERROR_PARAMETER_NOT_FOUND = "未找到指定的参数配置";

    /**
     * 更改商家任务金锁
     */
    public static final String CHANGE_MERCHANT_TASK_MONEY_LOCK = "change.merchant.task.money.lock.{}";

    /**
     * 修改商家或达人的分销比例锁
     */
    public static final String CHANGE_DISTRIBUTION_RATE_LOCK = "change.distribution.rate.lock.{}";
}
