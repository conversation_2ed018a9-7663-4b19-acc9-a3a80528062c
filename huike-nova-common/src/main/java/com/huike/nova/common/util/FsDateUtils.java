/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.huike.nova.common.util;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.shaded.com.google.common.primitives.Ints;
import com.huike.nova.common.constant.CommonConstant;
import lombok.SneakyThrows;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Objects;


/**
 * <AUTHOR>
 * @version FsDateUtils.java, v 0.1 2022-09-02 11:08 AM ruanzy
 */
public class FsDateUtils {

    /**
     * 简单日期类型(yyyy-MM-dd)
     */
    public static final String SIMPLE_DATE_FORMAT = "yyyy-MM-dd";
    /**
     * 简单年月类型(yyyy-MM)
     */
    public static final String SIMPLE_MONTH_FORMAT = "yyyy-MM";

    public static final String CHINESE_DATE_FORMAT = "yyyy年MM月dd日";
    /**
     * 简单日期时间类型(yyyy-MM-dd HH:mm:ss)
     */
    public static final String SIMPLE_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 精确分钟日期时间类型(yyyy-MM-dd HH:mm)
     */
    public static final String MINUTE_DATETIME_FORMAT = "yyyy-MM-dd HH:mm";

    /**
     * 简单时间类型(HH:mm:ss)
     */
    public static final String SIMPLE_TIME_FORMAT = "HH:mm:ss";
    /**
     * 数字日期类型(yyyyMMdd)
     */
    public static final String NUMBER_DATE_FORMAT = "yyyyMMdd";
    /**
     * 数字日期时间类型(yyyyMMddHHmmss)
     */
    public static final String NUMBER_DATE_TIME_FORMAT = "yyyyMMddHHmmss";

    public static final String NUMBER_DATE_WITH_POINT = "yyyy.MM.dd";

    public static final String NORM_DATE_PATTERN = "yyyy-MM-dd";

    /**
     * 精确分钟日期时间类型(MM-dd HH:mm)
     */
    public static final String MM_DD_HH_MM = "MM-dd HH:mm";

    /**
     * 精确分钟日期时间类型(HH:mm)
     */
    public static final String HH_MM = "HH:mm";

    /**
     * 秒转时间用到的乘数
     */
    public static final Long TIME_IN_MILLIS = 1000L;

    /**
     * yyyyMMdd 日期转换
     */
    public static final DateTimeFormatter DATE_STRING_FORMATTER = DateTimeFormatter.ofPattern(SIMPLE_DATE_FORMAT);

    public static final SimpleDateFormat DATE_STRING_SIMPLE_DATE_FORMAT = new SimpleDateFormat(SIMPLE_DATE_FORMAT);

    /**
     * String日期 (yyyy-MM-dd) 转成 Integer (yyyyMMdd)
     *
     * @param date
     * @return
     */
    public static Integer dateToInteger(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        try {
            String format = DateUtil.format(DateUtil.parseDate(date), NUMBER_DATE_FORMAT);
            return Integer.valueOf(format);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将LocalDateTime转为yyyyMMdd的整型
     *
     * @param localDateTime
     * @return
     */
    public static Integer localDateTimeToInteger(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return null;
        }
        String dateTimeStr = localDateTime.format(DateTimeFormatter.ofPattern(NUMBER_DATE_FORMAT));
        return Ints.tryParse(dateTimeStr);
    }

    /**
     * 获取昨日Integer日期（yyyyMMdd）
     *
     * @return
     */
    public static Integer getLastDay() {
        DateTime now = DateTime.now();
        String format = DateUtil.format(DateUtils.addDays(now.toJdkDate(), -1), NUMBER_DATE_FORMAT);
        return Integer.valueOf(format);
    }

    /**
     * 获取今日Integer日期 （yyyyMMdd）
     *
     * @return
     */
    public static Integer getNowDay() {
        DateTime now = DateTime.now();
        String format = DateUtil.format(now, NUMBER_DATE_FORMAT);
        return Integer.valueOf(format);
    }


    /**
     * 获取两个日期中的每一天
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static List<Date> getPerDay(String startTime, String endTime) {

        Date startDate = DateUtil.parse(startTime, NORM_DATE_PATTERN);
        Date endDate = DateUtil.parse(endTime, NORM_DATE_PATTERN);
        //定义一个接受时间的集合
        List<Date> lDate = new ArrayList<>();
        lDate.add(startDate);
        Calendar calBegin = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calBegin.setTime(startDate);
        Calendar calEnd = Calendar.getInstance();
        // 使用给定的 Date 设置此 Calendar 的时间
        calEnd.setTime(endDate);
        // 测试此日期是否在指定日期之后
        while (endDate.after(calBegin.getTime())) {
            // 根据日历的规则，为给定的日历字段添加或减去指定的时间量
            calBegin.add(Calendar.DAY_OF_MONTH, 1);
            lDate.add(calBegin.getTime());
        }
        return lDate;
    }


    /**
     * 获取前后x个月的日期
     *
     * @param offset 传入【正值】时表示查询【后面】几个月的时间； 传入【负值】时表示查询【前面】几个月的时间；
     * @return
     */
    public static Date getOffsetMinuteDate(Integer offset) {
        //得到日历
        Calendar calendar = Calendar.getInstance();
        //把当前时间赋给日历
        calendar.setTime(new Date());
        //设置为前或后几个月
        calendar.add(Calendar.MINUTE, offset);
        return calendar.getTime();
    }

    public static Date asDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取系统当时时间前或者后几天的时间
     *
     * @param afterDay
     * @return
     */
    public static Date getAfterDay(int afterDay) {
        LocalDateTime localDateTime = LocalDateTime.now().plusDays(afterDay);
        return asDate(localDateTime);
    }

    public static long getBeginDay() {
        long now = System.currentTimeMillis() / 1000L;
        long daySecond = 60 * 60 * 24;
        long time = now - (now + 8 * 3600) % daySecond;
        return time * 1000;
    }

    public static long getEndDay() {
        long time = getEndDaySecond();
        return time * 1000;
    }

    public static long getBeginDaySecond() {
        long now = System.currentTimeMillis() / 1000L;
        long daySecond = 60 * 60 * 24;
        long time = now - (now + 8 * 3600) % daySecond;
        return time;
    }

    public static long getEndDaySecond() {
        long now = System.currentTimeMillis() / 1000L;
        long daySecond = 60 * 60 * 24;
        long startTime = now - (now + 8 * 3600) % daySecond;
        long time = startTime + 86400L;
        return time;
    }

    /**
     * 获取零点时间
     *
     * @param date
     * @return
     */
    public static Date getDayStart(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(11, 0);
        calendar.set(12, 0);
        calendar.set(13, 0);
        calendar.set(14, 0);
        return calendar.getTime();
    }

    public static Date getDayStart2(Date date) {
        // 当日期转为
        val localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), CommonConstant.GMT_8);
        val dayStart = localDateTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
        return new Date(dayStart.toInstant(CommonConstant.GMT_8).toEpochMilli());
    }

    public static Date getDayEnd2(Date date) {
        // 当日期转为
        val localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), CommonConstant.GMT_8);
        val dayStart = localDateTime.withHour(23).withMinute(59).withSecond(59).withNano(999999);
        return new Date(dayStart.toInstant(CommonConstant.GMT_8).toEpochMilli());
    }

    /**
     * 时间戳转换
     *
     * @param unixSecond
     * @return
     */
    public static Date longTimeToDate(Long unixSecond) {
        Calendar c1 = Calendar.getInstance();
        c1.setTimeInMillis(unixSecond);
        return c1.getTime();
    }

    /**
     * 将字符串类型的时间转换为秒数
     *
     * @param timeStr 时间字符串
     * @return 秒数
     */
    public static Integer convertTimeToStart(String timeStr) {
        try {
            Date date = Convert.toDate(timeStr);
            return (int) (DateUtil.beginOfDay(date).getTime() / 1000);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将字符串类型的时间转换为秒数
     *
     * @param timeStr 时间字符串
     * @return 秒数
     */
    public static Integer convertTimeToEnd(String timeStr) {
        try {
            Date date = Convert.toDate(timeStr);
            return (int) (DateUtil.endOfDay(date).getTime() / 1000);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * String日期 (yyyyMMdd) 转成 Integer (yyyyMMdd)
     *
     * @param date
     * @return
     */
    public static Integer numberDateToInteger(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        try {
            return Integer.valueOf(date);
        } catch (Exception e) {
            return null;
        }
    }


    /**
     * String日期 (yyyyMMdd) 转成 String (yyyy-MM-dd)
     *
     * @param date
     * @return
     */
    public static String numberDateToString(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        try {
            return DateUtil.format(DateUtil.parse(date, NUMBER_DATE_FORMAT), NORM_DATE_PATTERN);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据时间戳和格式返回时间
     *
     * @param second
     * @param format
     * @return
     */
    public static String formatSecond(Integer second, String format) {
        if (ObjectUtil.isNull(second) || StringUtils.isBlank(format)) {
            return StringUtils.EMPTY;
        }
        if (second <= 0) {
            return StringUtils.EMPTY;
        }
        Date date = secondToDate(second);
        return DateUtil.format(date, format);
    }

    /**
     * 时间戳转date
     *
     * @param second
     * @return
     */
    public static Date secondToDate(Integer second) {
        if (ObjectUtil.isNotNull(second)) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTimeInMillis(second.longValue() * TIME_IN_MILLIS);
            return calendar.getTime();
        } else {
            return new Date();
        }
    }

    /**
     * 字符串转LocalDateTime
     *
     * @param date 日期，格式为yyyy-MM-dd
     * @return 本地时间
     */
    public static LocalDateTime dateStrToLocalDateTime(String date) {
        return LocalDateTime.parse(date, DATE_STRING_FORMATTER);
    }

    /**
     * 字符串转LocalDateTime
     *
     * @param date 日期 格式为：yyyy-MM-dd HH:mm:ss
     * @return 本地时间
     */
    public static LocalDateTime dateTimeStrToLocalDateTime(String date) {
        if (StringUtils.isBlank(date)) {
            return null;
        }
        return LocalDateTimeUtil.parse(date, SIMPLE_DATETIME_FORMAT);
    }

    /**
     * 日期字符串转Date
     *
     * @param date 字符串,格式yyyy-MM-dd
     * @return date
     * @throws ParseException
     */
    @SneakyThrows
    public static Date dateStrToDate(String date) {
        return DATE_STRING_SIMPLE_DATE_FORMAT.parse(date);
    }

    /**
     * 获得今日日期
     *
     * @return 今日日期
     */
    public static Date today() {
        return DateUtil.beginOfDay(new Date());
    }

    /**
     * 添加日期
     *
     * @param days 日期
     * @return 日期
     */
    public static Date addDays(int days) {
        LocalDateTime target = LocalDateTime.now().plusDays(days);
        return new Date(target.toInstant(CommonConstant.GMT_8).toEpochMilli());
    }

    public static String toDateTimeString(Date date) {
        return DateUtil.format(date, SIMPLE_DATETIME_FORMAT);
    }

    /**
     * 格式化日期
     *
     * @param date
     * @param pattern
     * @return
     */
    public static String format(Date date, String pattern) {
        return DateFormatUtils.format(date, pattern);
    }

    /**
     * 格式化时间
     *
     * @param str
     * @param parsePatterns
     * @return
     */
    public static Date parseDate(String str, String... parsePatterns) {
        try {
            return DateUtils.parseDate(str, null, parsePatterns);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 日期转时间戳
     *
     * @param date
     * @return
     */
    public static Integer getTimeStamp(Date date) {
        if (null == date) {
            return 0;
        }
        Long times = date.getTime() / 1000L;
        return times.intValue();
    }

    public static String TimeStamp2SimpleDate(Integer unixSecond) {
        String date = new SimpleDateFormat("yyyy-MM-dd", Locale.CHINA).format(new Date(unixSecond * 1000L));
        return date;
    }

    public static String TimeStamp2SimpleDateTime(Integer unixSecond) {
        String date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINA).format(new Date(unixSecond * 1000L));
        return date;
    }

    /**
     * 获取昨日Integer日期（yyyyMMdd）
     *
     * @return
     */
    public static Integer dateToInt(Date date) {
        String format = DateUtil.format(date, NUMBER_DATE_FORMAT);
        return Integer.valueOf(format);
    }

    /**
     * 将毫秒时间戳转为日期类型
     *
     * @param timestampMillis 毫秒时间戳
     * @return 日期类型
     */
    public static Date timestampMillisToDate(String timestampMillis) {
        try {
            return longTimeToDate(Long.parseLong(timestampMillis));
        } catch (Exception ignored) {
        }
        return null;
    }

}