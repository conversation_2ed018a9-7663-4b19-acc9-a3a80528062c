package com.huike.nova.common.util;

import com.huike.nova.common.constant.StringPool;
import org.apache.commons.lang3.StringUtils;

/**
 * 手机号码处理工具
 *
 * <AUTHOR> (<EMAIL>)
 * @version PhoneNumberUtil.java, v1.0 01/10/2024 14:40 John Exp$
 */
public class PhoneNumberUtil {
    /**
     * 手机号码
     */
    private static final String REGEX_PHONE_NUMBER_WITHOUT_AREA_CODE = "(\\w{3})\\w*(\\w{4})";

    /**
     * 脱敏的手机号码
     */
    private static final String REPLACEMENT = "$1****$2";


    /**
     * 手机号码脱敏
     *
     * @param phoneNumber 手机号码
     * @return 脱敏的手机号码
     */
    public static String mask(String phoneNumber) {
        if (StringUtils.isBlank(phoneNumber)) {
            return StringPool.EMPTY;
        }
        return phoneNumber.replaceAll(REGEX_PHONE_NUMBER_WITHOUT_AREA_CODE, REPLACEMENT);
    }

}
